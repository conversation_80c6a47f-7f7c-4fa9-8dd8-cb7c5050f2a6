/*
========================================================================================
    Module-specific Configuration for Micro-C Pipeline
========================================================================================
    Process-specific resource requirements and configurations.
========================================================================================
*/

process {
    
    // EXTRACT_BWA_INDEX
    withName: 'EXTRACT_BWA_INDEX' {
        cpus   = { check_max( 2 * task.attempt, 'cpus' ) }
        memory = { check_max( 4.GB * task.attempt, 'memory' ) }
        time   = { check_max( 1.h * task.attempt, 'time' ) }
        publishDir = [
            path: { "${params.output_dir}/align/${meta.id}/genome_index" },
            mode: params.publish_dir_mode,
            saveAs: { filename -> filename.equals('versions.yml') ? null : filename }
        ]
    }
    
    // BWA_ALIGN_PAIRTOOLS
    withName: 'BWA_ALIGN_PAIRTOOLS' {
        cpus   = { check_max( params.bwa_cores * task.attempt, 'cpus' ) }
        memory = { check_max( 16.GB * task.attempt, 'memory' ) }
        time   = { check_max( 8.h * task.attempt, 'time' ) }
        publishDir = [
            path: { "${params.output_dir}/align/${meta.id}" },
            mode: params.publish_dir_mode,
            saveAs: { filename -> filename.equals('versions.yml') ? null : filename }
        ]
    }
    
    // MERGE_DEDUP_SPLIT
    withName: 'MERGE_DEDUP_SPLIT' {
        cpus   = { check_max( 12 * task.attempt, 'cpus' ) }
        memory = { check_max( 32.GB * task.attempt, 'memory' ) }
        time   = { check_max( 12.h * task.attempt, 'time' ) }
        publishDir = [
            path: { "${params.output_dir}/merge" },
            mode: params.publish_dir_mode,
            saveAs: { filename -> filename.equals('versions.yml') ? null : filename }
        ]
    }
    
    // COOLER_PROCESS
    withName: 'COOLER_PROCESS' {
        cpus   = { check_max( 8 * task.attempt, 'cpus' ) }
        memory = { check_max( 24.GB * task.attempt, 'memory' ) }
        time   = { check_max( 6.h * task.attempt, 'time' ) }
        publishDir = [
            path: { "${params.output_dir}/cooler" },
            mode: params.publish_dir_mode,
            saveAs: { filename -> filename.equals('versions.yml') ? null : filename }
        ]
    }
    
    // JUICER_HIC
    withName: 'JUICER_HIC' {
        cpus   = { check_max( params.bwa_cores * task.attempt, 'cpus' ) }
        memory = { check_max( 128.GB * task.attempt, 'memory' ) }
        time   = { check_max( 4.h * task.attempt, 'time' ) }
        errorStrategy = 'ignore'  // Allow to fail gracefully
        publishDir = [
            path: { "${params.output_dir}/hic" },
            mode: params.publish_dir_mode,
            saveAs: { filename -> filename.equals('versions.yml') ? null : filename }
        ]
    }
    
    // QC_METRICS
    withName: 'QC_METRICS' {
        cpus   = { check_max( 2 * task.attempt, 'cpus' ) }
        memory = { check_max( 4.GB * task.attempt, 'memory' ) }
        time   = { check_max( 1.h * task.attempt, 'time' ) }
        publishDir = [
            path: { "${params.output_dir}/qc" },
            mode: params.publish_dir_mode,
            saveAs: { filename -> filename.equals('versions.yml') ? null : filename }
        ]
    }
}

// Default publish directory mode
params.publish_dir_mode = 'copy'

// Function to ensure that resource requirements don't go beyond a maximum limit
def check_max(obj, type) {
    if (type == 'memory') {
        try {
            if (obj.compareTo(params.max_memory as nextflow.util.MemoryUnit) == 1)
                return params.max_memory as nextflow.util.MemoryUnit
            else
                return obj
        } catch (all) {
            println "   ### ERROR ###   You have not specified a max memory limit. Please set params.max_memory"
            return obj
        }
    } else if (type == 'time') {
        try {
            if (obj.compareTo(params.max_time as nextflow.util.Duration) == 1)
                return params.max_time as nextflow.util.Duration
            else
                return obj
        } catch (all) {
            println "   ### ERROR ###   You have not specified a max time limit. Please set params.max_time"
            return obj
        }
    } else if (type == 'cpus') {
        try {
            return Math.min( obj, params.max_cpus as int )
        } catch (all) {
            println "   ### ERROR ###   You have not specified a max cpus limit. Please set params.max_cpus"
            return obj
        }
    }
}
