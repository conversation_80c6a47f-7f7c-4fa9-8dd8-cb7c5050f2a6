/*
========================================================================================
    Base Configuration for Micro-C Pipeline
========================================================================================
    Default resource requirements and process configurations.
========================================================================================
*/

process {
    // Default resource requirements
    cpus   = { check_max( 1    * task.attempt, 'cpus'   ) }
    memory = { check_max( 6.GB * task.attempt, 'memory' ) }
    time   = { check_max( 4.h  * task.attempt, 'time'   ) }
    
    errorStrategy = { task.exitStatus in [143,137,104,134,139] ? 'retry' : 'finish' }
    maxRetries    = 1
    maxErrors     = '-1'
    
    // Process-specific resource requirements
    withLabel:process_low {
        cpus   = { check_max( 2     * task.attempt, 'cpus'    ) }
        memory = { check_max( 12.GB * task.attempt, 'memory'  ) }
        time   = { check_max( 4.h   * task.attempt, 'time'    ) }
    }
    withLabel:process_medium {
        cpus   = { check_max( 6     * task.attempt, 'cpus'    ) }
        memory = { check_max( 36.GB * task.attempt, 'memory'  ) }
        time   = { check_max( 8.h   * task.attempt, 'time'    ) }
    }
    withLabel:process_high {
        cpus   = { check_max( 12    * task.attempt, 'cpus'    ) }
        memory = { check_max( 72.GB * task.attempt, 'memory'  ) }
        time   = { check_max( 16.h  * task.attempt, 'time'    ) }
    }
    withLabel:process_long {
        time   = { check_max( 20.h  * task.attempt, 'time'    ) }
    }
    withLabel:process_high_memory {
        memory = { check_max( 200.GB * task.attempt, 'memory' ) }
    }
    
    // Error handling for specific processes
    withName:EXTRACT_BWA_INDEX {
        errorStrategy = 'retry'
        maxRetries    = 2
    }
    
    withName:BWA_ALIGN_PAIRTOOLS {
        errorStrategy = 'retry'
        maxRetries    = 2
    }
    
    withName:MERGE_DEDUP_SPLIT {
        errorStrategy = 'retry'
        maxRetries    = 2
    }
    
    withName:COOLER_PROCESS {
        errorStrategy = 'retry'
        maxRetries    = 2
    }
    
    withName:JUICER_HIC {
        errorStrategy = 'ignore'  // Allow to fail without stopping pipeline
        maxRetries    = 1
    }
    
    withName:QC_METRICS {
        errorStrategy = 'retry'
        maxRetries    = 2
    }
}

// Function to ensure that resource requirements don't go beyond a maximum limit
def check_max(obj, type) {
    if (type == 'memory') {
        try {
            if (obj.compareTo(params.max_memory as nextflow.util.MemoryUnit) == 1)
                return params.max_memory as nextflow.util.MemoryUnit
            else
                return obj
        } catch (all) {
            println "   ### ERROR ###   You have not specified a max memory limit. Please set params.max_memory"
            return obj
        }
    } else if (type == 'time') {
        try {
            if (obj.compareTo(params.max_time as nextflow.util.Duration) == 1)
                return params.max_time as nextflow.util.Duration
            else
                return obj
        } catch (all) {
            println "   ### ERROR ###   You have not specified a max time limit. Please set params.max_time"
            return obj
        }
    } else if (type == 'cpus') {
        try {
            return Math.min( obj, params.max_cpus as int )
        } catch (all) {
            println "   ### ERROR ###   You have not specified a max cpus limit. Please set params.max_cpus"
            return obj
        }
    }
}
