/*
========================================================================================
    Test Configuration for Micro-C Pipeline
========================================================================================
    Configuration for running the pipeline with test data.
========================================================================================
*/

params {
    config_profile_name        = 'Test profile'
    config_profile_description = 'Minimal test dataset to check pipeline function'
    
    // Limit resources so that this can run on GitHub Actions
    max_cpus   = 2
    max_memory = '6.GB'
    max_time   = '6.h'
    
    // Input data
    sample_id                  = 'test-sample'
    fastq_r1                   = 'tests/small-region-capture-micro-c/small_rcmc_r1.fq.gz'
    fastq_r2                   = 'tests/small-region-capture-micro-c/small_rcmc_r2.fq.gz'
    reference_bwa_idx          = 'tests/small-region-capture-micro-c/test_bwa_index.tgz'
    chrom_sizes                = 'tests/small-region-capture-micro-c/test.chrom.sizes'
    
    // Pipeline parameters
    resolution                 = 1000
    bwa_cores                  = 2
    mapq                       = 20
    
    // Output
    output_dir                 = './test_results'
}

process {
    // Reduce resource requirements for test
    withLabel:process_low {
        cpus   = 1
        memory = 2.GB
        time   = 1.h
    }
    withLabel:process_medium {
        cpus   = 2
        memory = 4.GB
        time   = 2.h
    }
    withLabel:process_high {
        cpus   = 2
        memory = 6.GB
        time   = 4.h
    }
}
