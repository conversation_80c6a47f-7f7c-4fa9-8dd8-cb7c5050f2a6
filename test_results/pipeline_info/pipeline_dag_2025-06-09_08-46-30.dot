digraph "pipeline_dag_20250609_084630" {
rankdir=TB;
v0 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromList"];
v1 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v0 -> v1;

v1 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v4 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="combine"];
v1 -> v4 [label="fastq_r1_ch"];

v2 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromList"];
v3 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v2 -> v3;

v3 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v4 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="combine"];
v3 -> v4 [label="fastq_r2_ch"];

v4 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="combine"];
v5 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v4 -> v5;

v5 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v8 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v5 -> v8 [label="fastq_pairs_ch"];

v6 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromPath"];
v9 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v6 -> v9 [label="bwa_index_ch"];

v7 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromPath"];
v12 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v7 -> v12 [label="chrom_sizes_ch"];

v8 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v10 [label="EXTRACT_BWA_INDEX"];
v8 -> v10;

v9 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v10 [label="EXTRACT_BWA_INDEX"];
v9 -> v10;

v10 [label="EXTRACT_BWA_INDEX"];
v13 [label="BWA_ALIGN_PAIRTOOLS"];
v10 -> v13;

v10 [label="EXTRACT_BWA_INDEX"];
v11 [shape=point];
v10 -> v11;

v12 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v13 [label="BWA_ALIGN_PAIRTOOLS"];
v12 -> v13;

v5 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v13 [label="BWA_ALIGN_PAIRTOOLS"];
v5 -> v13 [label="fastq_pairs_ch"];

v13 [label="BWA_ALIGN_PAIRTOOLS"];
v15 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v13 -> v15;

v13 [label="BWA_ALIGN_PAIRTOOLS"];
v14 [shape=point];
v13 -> v14;

v15 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v16 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v15 -> v16;

v16 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v17 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v16 -> v17;

v17 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v18 [label="MERGE_DEDUP_SPLIT"];
v17 -> v18 [label="pairsam_files_ch"];

v18 [label="MERGE_DEDUP_SPLIT"];
v23 [label="COOLER_PROCESS"];
v18 -> v23 [label="mapped_pairs"];

v18 [label="MERGE_DEDUP_SPLIT"];
v34 [label="QC_METRICS"];
v18 -> v34 [label="stats"];

v18 [label="MERGE_DEDUP_SPLIT"];
v21 [shape=point];
v18 -> v21 [label="bam"];

v18 [label="MERGE_DEDUP_SPLIT"];
v20 [shape=point];
v18 -> v20 [label="bai"];

v18 [label="MERGE_DEDUP_SPLIT"];
v19 [shape=point];
v18 -> v19;

v7 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromPath"];
v22 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v7 -> v22 [label="chrom_sizes_ch"];

v22 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v23 [label="COOLER_PROCESS"];
v22 -> v23;

v23 [label="COOLER_PROCESS"];
v27 [shape=point];
v23 -> v27 [label="cool"];

v23 [label="COOLER_PROCESS"];
v26 [shape=point];
v23 -> v26 [label="raw_mcool"];

v23 [label="COOLER_PROCESS"];
v25 [shape=point];
v23 -> v25 [label="balanced_mcool"];

v23 [label="COOLER_PROCESS"];
v24 [shape=point];
v23 -> v24;

v7 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromPath"];
v28 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v7 -> v28 [label="chrom_sizes_ch"];

v28 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v29 [label="JUICER_HIC"];
v28 -> v29;

v18 [label="MERGE_DEDUP_SPLIT"];
v29 [label="JUICER_HIC"];
v18 -> v29 [label="mapped_pairs"];

v29 [label="JUICER_HIC"];
v33 [shape=point];
v29 -> v33 [label="hic"];

v29 [label="JUICER_HIC"];
v32 [shape=point];
v29 -> v32;

v29 [label="JUICER_HIC"];
v31 [shape=point];
v29 -> v31;

v29 [label="JUICER_HIC"];
v30 [shape=point];
v29 -> v30;

v34 [label="QC_METRICS"];
v37 [shape=point];
v34 -> v37 [label="qc_metrics"];

v34 [label="QC_METRICS"];
v36 [shape=point];
v34 -> v36;

v34 [label="QC_METRICS"];
v35 [shape=point];
v34 -> v35;

}
