digraph "pipeline_dag_20250609_080439" {
rankdir=TB;
v0 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromPath"];
v2 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="combine"];
v0 -> v2 [label="fastq_r1_ch"];

v1 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromPath"];
v2 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="combine"];
v1 -> v2 [label="fastq_r2_ch"];

v2 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="combine"];
v3 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v2 -> v3;

v3 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v6 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v3 -> v6 [label="fastq_pairs_ch"];

v4 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromPath"];
v7 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v4 -> v7 [label="bwa_index_ch"];

v5 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromPath"];
v10 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v5 -> v10 [label="chrom_sizes_ch"];

v6 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v8 [label="EXTRACT_BWA_INDEX"];
v6 -> v8;

v7 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v8 [label="EXTRACT_BWA_INDEX"];
v7 -> v8;

v8 [label="EXTRACT_BWA_INDEX"];
v11 [label="BWA_ALIGN_PAIRTOOLS"];
v8 -> v11;

v8 [label="EXTRACT_BWA_INDEX"];
v9 [shape=point];
v8 -> v9;

v10 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v11 [label="BWA_ALIGN_PAIRTOOLS"];
v10 -> v11;

v3 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v11 [label="BWA_ALIGN_PAIRTOOLS"];
v3 -> v11 [label="fastq_pairs_ch"];

v11 [label="BWA_ALIGN_PAIRTOOLS"];
v13 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v11 -> v13;

v11 [label="BWA_ALIGN_PAIRTOOLS"];
v12 [shape=point];
v11 -> v12;

v13 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v14 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v13 -> v14;

v14 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v15 [label="MERGE_DEDUP_SPLIT"];
v14 -> v15 [label="pairsam_files_ch"];

v15 [label="MERGE_DEDUP_SPLIT"];
v20 [label="COOLER_PROCESS"];
v15 -> v20 [label="mapped_pairs"];

v15 [label="MERGE_DEDUP_SPLIT"];
v31 [label="QC_METRICS"];
v15 -> v31 [label="stats"];

v15 [label="MERGE_DEDUP_SPLIT"];
v18 [shape=point];
v15 -> v18 [label="bam"];

v15 [label="MERGE_DEDUP_SPLIT"];
v17 [shape=point];
v15 -> v17 [label="bai"];

v15 [label="MERGE_DEDUP_SPLIT"];
v16 [shape=point];
v15 -> v16;

v5 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromPath"];
v19 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v5 -> v19 [label="chrom_sizes_ch"];

v19 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v20 [label="COOLER_PROCESS"];
v19 -> v20;

v20 [label="COOLER_PROCESS"];
v24 [shape=point];
v20 -> v24 [label="cool"];

v20 [label="COOLER_PROCESS"];
v23 [shape=point];
v20 -> v23 [label="raw_mcool"];

v20 [label="COOLER_PROCESS"];
v22 [shape=point];
v20 -> v22 [label="balanced_mcool"];

v20 [label="COOLER_PROCESS"];
v21 [shape=point];
v20 -> v21;

v5 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromPath"];
v25 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v5 -> v25 [label="chrom_sizes_ch"];

v25 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v26 [label="JUICER_HIC"];
v25 -> v26;

v15 [label="MERGE_DEDUP_SPLIT"];
v26 [label="JUICER_HIC"];
v15 -> v26 [label="mapped_pairs"];

v26 [label="JUICER_HIC"];
v30 [shape=point];
v26 -> v30 [label="hic"];

v26 [label="JUICER_HIC"];
v29 [shape=point];
v26 -> v29;

v26 [label="JUICER_HIC"];
v28 [shape=point];
v26 -> v28;

v26 [label="JUICER_HIC"];
v27 [shape=point];
v26 -> v27;

v31 [label="QC_METRICS"];
v34 [shape=point];
v31 -> v34 [label="qc_metrics"];

v31 [label="QC_METRICS"];
v33 [shape=point];
v31 -> v33;

v31 [label="QC_METRICS"];
v32 [shape=point];
v31 -> v32;

}
