digraph "pipeline_dag_20250609_080253" {
rankdir=TB;
v0 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromPath"];
v2 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="combine"];
v0 -> v2 [label="fastq_r1_ch"];

v1 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromPath"];
v2 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="combine"];
v1 -> v2 [label="fastq_r2_ch"];

v2 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="combine"];
v3 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v2 -> v3;

v3 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v6 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v3 -> v6 [label="fastq_pairs_ch"];

v4 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromPath"];
v7 [label="EXTRACT_BWA_INDEX"];
v4 -> v7 [label="bwa_index_ch"];

v5 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromPath"];
v10 [label="BWA_ALIGN_PAIRTOOLS"];
v5 -> v10 [label="chrom_sizes_ch"];

v6 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v7 [label="EXTRACT_BWA_INDEX"];
v6 -> v7;

v7 [label="EXTRACT_BWA_INDEX"];
v10 [label="BWA_ALIGN_PAIRTOOLS"];
v7 -> v10;

v7 [label="EXTRACT_BWA_INDEX"];
v8 [shape=point];
v7 -> v8;

v3 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v10 [label="BWA_ALIGN_PAIRTOOLS"];
v3 -> v10 [label="fastq_pairs_ch"];

v10 [label="BWA_ALIGN_PAIRTOOLS"];
v12 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v10 -> v12;

v10 [label="BWA_ALIGN_PAIRTOOLS"];
v11 [shape=point];
v10 -> v11;

v12 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v13 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v12 -> v13;

v13 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v14 [label="MERGE_DEDUP_SPLIT"];
v13 -> v14 [label="pairsam_files_ch"];

v14 [label="MERGE_DEDUP_SPLIT"];
v19 [label="COOLER_PROCESS"];
v14 -> v19 [label="mapped_pairs"];

v14 [label="MERGE_DEDUP_SPLIT"];
v30 [label="QC_METRICS"];
v14 -> v30 [label="stats"];

v14 [label="MERGE_DEDUP_SPLIT"];
v16 [shape=point];
v14 -> v16 [label="bam"];

v14 [label="MERGE_DEDUP_SPLIT"];
v17 [shape=point];
v14 -> v17 [label="bai"];

v14 [label="MERGE_DEDUP_SPLIT"];
v15 [shape=point];
v14 -> v15;

v5 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromPath"];
v19 [label="COOLER_PROCESS"];
v5 -> v19 [label="chrom_sizes_ch"];

v19 [label="COOLER_PROCESS"];
v20 [shape=point];
v19 -> v20 [label="cool"];

v19 [label="COOLER_PROCESS"];
v23 [shape=point];
v19 -> v23 [label="raw_mcool"];

v19 [label="COOLER_PROCESS"];
v22 [shape=point];
v19 -> v22 [label="balanced_mcool"];

v19 [label="COOLER_PROCESS"];
v21 [shape=point];
v19 -> v21;

v14 [label="MERGE_DEDUP_SPLIT"];
v25 [label="JUICER_HIC"];
v14 -> v25 [label="mapped_pairs"];

v5 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromPath"];
v25 [label="JUICER_HIC"];
v5 -> v25 [label="chrom_sizes_ch"];

v25 [label="JUICER_HIC"];
v29 [shape=point];
v25 -> v29 [label="hic"];

v25 [label="JUICER_HIC"];
v28 [shape=point];
v25 -> v28;

v25 [label="JUICER_HIC"];
v27 [shape=point];
v25 -> v27;

v25 [label="JUICER_HIC"];
v26 [shape=point];
v25 -> v26;

v30 [label="QC_METRICS"];
v33 [shape=point];
v30 -> v33 [label="qc_metrics"];

v30 [label="QC_METRICS"];
v32 [shape=point];
v30 -> v32;

v30 [label="QC_METRICS"];
v31 [shape=point];
v30 -> v31;

}
