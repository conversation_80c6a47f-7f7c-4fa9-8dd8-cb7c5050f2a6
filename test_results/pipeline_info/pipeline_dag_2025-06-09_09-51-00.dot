digraph "pipeline_dag_20250609_095100" {
v0 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromList"];
v3 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v0 -> v3 [label="fastq_pairs_ch"];

v1 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromPath"];
v4 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v1 -> v4 [label="bwa_index_ch"];

v2 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromPath"];
v7 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v2 -> v7 [label="chrom_sizes_ch"];

v3 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v5 [label="EXTRACT_BWA_INDEX"];
v3 -> v5;

v4 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v5 [label="EXTRACT_BWA_INDEX"];
v4 -> v5;

v5 [label="EXTRACT_BWA_INDEX"];
v8 [label="BWA_ALIGN_PAIRTOOLS"];
v5 -> v8;

v5 [label="EXTRACT_BWA_INDEX"];
v6 [shape=point];
v5 -> v6;

v7 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v8 [label="BWA_ALIGN_PAIRTOOLS"];
v7 -> v8;

v0 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromList"];
v8 [label="BWA_ALIGN_PAIRTOOLS"];
v0 -> v8 [label="fastq_pairs_ch"];

v8 [label="BWA_ALIGN_PAIRTOOLS"];
v10 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v8 -> v10;

v8 [label="BWA_ALIGN_PAIRTOOLS"];
v9 [shape=point];
v8 -> v9;

v10 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v11 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v10 -> v11;

v11 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v12 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v11 -> v12;

v12 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v13 [label="MERGE_DEDUP_SPLIT"];
v12 -> v13 [label="pairsam_files_ch"];

v13 [label="MERGE_DEDUP_SPLIT"];
v18 [label="COOLER_PROCESS"];
v13 -> v18 [label="mapped_pairs"];

v13 [label="MERGE_DEDUP_SPLIT"];
v29 [label="QC_METRICS"];
v13 -> v29 [label="stats"];

v13 [label="MERGE_DEDUP_SPLIT"];
v16 [shape=point];
v13 -> v16 [label="bam"];

v13 [label="MERGE_DEDUP_SPLIT"];
v15 [shape=point];
v13 -> v15 [label="bai"];

v13 [label="MERGE_DEDUP_SPLIT"];
v14 [shape=point];
v13 -> v14;

v2 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromPath"];
v17 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v2 -> v17 [label="chrom_sizes_ch"];

v17 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v18 [label="COOLER_PROCESS"];
v17 -> v18;

v18 [label="COOLER_PROCESS"];
v22 [shape=point];
v18 -> v22 [label="cool"];

v18 [label="COOLER_PROCESS"];
v21 [shape=point];
v18 -> v21 [label="raw_mcool"];

v18 [label="COOLER_PROCESS"];
v20 [shape=point];
v18 -> v20 [label="balanced_mcool"];

v18 [label="COOLER_PROCESS"];
v19 [shape=point];
v18 -> v19;

v2 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromPath"];
v23 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v2 -> v23 [label="chrom_sizes_ch"];

v23 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v24 [label="JUICER_HIC"];
v23 -> v24;

v13 [label="MERGE_DEDUP_SPLIT"];
v24 [label="JUICER_HIC"];
v13 -> v24 [label="mapped_pairs"];

v24 [label="JUICER_HIC"];
v28 [shape=point];
v24 -> v28 [label="hic"];

v24 [label="JUICER_HIC"];
v27 [shape=point];
v24 -> v27;

v24 [label="JUICER_HIC"];
v26 [shape=point];
v24 -> v26;

v24 [label="JUICER_HIC"];
v25 [shape=point];
v24 -> v25;

v29 [label="QC_METRICS"];
v32 [shape=point];
v29 -> v32 [label="qc_metrics"];

v29 [label="QC_METRICS"];
v31 [shape=point];
v29 -> v31;

v29 [label="QC_METRICS"];
v30 [shape=point];
v29 -> v30;

}
