digraph "pipeline_dag_20250609_081013" {
rankdir=TB;
v0 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromList"];
v1 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v0 -> v1;

v1 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v4 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="combine"];
v1 -> v4 [label="fastq_r1_ch"];

v2 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromList"];
v3 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v2 -> v3;

v3 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v4 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="combine"];
v3 -> v4 [label="fastq_r2_ch"];

v4 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="combine"];
v5 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v4 -> v5;

v5 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v8 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v5 -> v8 [label="fastq_pairs_ch"];

v6 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromPath"];
v9 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v6 -> v9 [label="bwa_index_ch"];

v7 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromPath"];
v12 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v7 -> v12 [label="chrom_sizes_ch"];

v8 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v10 [label="EXTRACT_BWA_INDEX"];
v8 -> v10;

v9 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v10 [label="EXTRACT_BWA_INDEX"];
v9 -> v10;

v10 [label="EXTRACT_BWA_INDEX"];
v13 [label="BWA_ALIGN_PAIRTOOLS"];
v10 -> v13;

v10 [label="EXTRACT_BWA_INDEX"];
v11 [shape=point];
v10 -> v11;

v12 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v13 [label="BWA_ALIGN_PAIRTOOLS"];
v12 -> v13;

v5 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v13 [label="BWA_ALIGN_PAIRTOOLS"];
v5 -> v13 [label="fastq_pairs_ch"];

v13 [label="BWA_ALIGN_PAIRTOOLS"];
v15 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v13 -> v15;

v13 [label="BWA_ALIGN_PAIRTOOLS"];
v14 [shape=point];
v13 -> v14;

v15 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="collect"];
v16 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v15 -> v16;

v16 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="map"];
v17 [label="MERGE_DEDUP_SPLIT"];
v16 -> v17 [label="pairsam_files_ch"];

v17 [label="MERGE_DEDUP_SPLIT"];
v22 [label="COOLER_PROCESS"];
v17 -> v22 [label="mapped_pairs"];

v17 [label="MERGE_DEDUP_SPLIT"];
v33 [label="QC_METRICS"];
v17 -> v33 [label="stats"];

v17 [label="MERGE_DEDUP_SPLIT"];
v20 [shape=point];
v17 -> v20 [label="bam"];

v17 [label="MERGE_DEDUP_SPLIT"];
v19 [shape=point];
v17 -> v19 [label="bai"];

v17 [label="MERGE_DEDUP_SPLIT"];
v18 [shape=point];
v17 -> v18;

v7 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromPath"];
v21 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v7 -> v21 [label="chrom_sizes_ch"];

v21 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v22 [label="COOLER_PROCESS"];
v21 -> v22;

v22 [label="COOLER_PROCESS"];
v26 [shape=point];
v22 -> v26 [label="cool"];

v22 [label="COOLER_PROCESS"];
v25 [shape=point];
v22 -> v25 [label="raw_mcool"];

v22 [label="COOLER_PROCESS"];
v24 [shape=point];
v22 -> v24 [label="balanced_mcool"];

v22 [label="COOLER_PROCESS"];
v23 [shape=point];
v22 -> v23;

v7 [shape=point,label="",fixedsize=true,width=0.1,xlabel="Channel.fromPath"];
v27 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v7 -> v27 [label="chrom_sizes_ch"];

v27 [shape=circle,label="",fixedsize=true,width=0.1,xlabel="first"];
v28 [label="JUICER_HIC"];
v27 -> v28;

v17 [label="MERGE_DEDUP_SPLIT"];
v28 [label="JUICER_HIC"];
v17 -> v28 [label="mapped_pairs"];

v28 [label="JUICER_HIC"];
v32 [shape=point];
v28 -> v32 [label="hic"];

v28 [label="JUICER_HIC"];
v31 [shape=point];
v28 -> v31;

v28 [label="JUICER_HIC"];
v30 [shape=point];
v28 -> v30;

v28 [label="JUICER_HIC"];
v29 [shape=point];
v28 -> v29;

v33 [label="QC_METRICS"];
v36 [shape=point];
v33 -> v36 [label="qc_metrics"];

v33 [label="QC_METRICS"];
v35 [shape=point];
v33 -> v35;

v33 [label="QC_METRICS"];
v34 [shape=point];
v33 -> v34;

}
