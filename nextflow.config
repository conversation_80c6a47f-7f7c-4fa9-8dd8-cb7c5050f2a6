/*
========================================================================================
    Micro-C Pipeline Nextflow Configuration
========================================================================================
    Configuration file for the Micro-C pipeline Nextflow implementation.
    Defines resource requirements, container settings, and execution profiles.
========================================================================================
*/

// Global default params, used in configs
params {
    // Input options
    sample_id                  = null
    fastq_r1                   = null
    fastq_r2                   = null
    reference_bwa_idx          = null
    chrom_sizes                = null
    
    // Output options
    output_dir                 = './results'
    
    // Pipeline options
    resolution                 = 1000
    bwa_cores                  = 2
    mapq                       = 20
    
    // Boilerplate options
    help                       = false
    version                    = false
    validate_params            = true
    show_hidden_params         = false
    schema_ignore_params       = 'genomes'
    
    // Config options
    custom_config_version      = 'master'
    custom_config_base         = "https://raw.githubusercontent.com/nf-core/configs/${params.custom_config_version}"
    config_profile_description = null
    config_profile_contact     = null
    config_profile_url         = null
    config_profile_name        = null
    
    // Max resource options
    // Defaults only, expecting to be overwritten
    max_memory                 = '128.GB'
    max_cpus                   = 16
    max_time                   = '240.h'
    
    // Container options
    enable_conda               = false
    singularity_pull_docker_container = false
}

// Load base.config by default for all pipelines
includeConfig 'conf/base.config'

// Load modules.config for module-specific configuration
includeConfig 'conf/modules.config'

profiles {
    debug { process.beforeScript = 'echo $HOSTNAME' }
    
    conda {
        params.enable_conda    = true
        docker.enabled         = false
        singularity.enabled    = false
        podman.enabled         = false
        shifter.enabled        = false
        charliecloud.enabled   = false
    }
    
    mamba {
        params.enable_conda    = true
        conda.useMamba         = true
        docker.enabled         = false
        singularity.enabled    = false
        podman.enabled         = false
        shifter.enabled        = false
        charliecloud.enabled   = false
    }
    
    docker {
        docker.enabled         = true
        docker.userEmulation   = true
        singularity.enabled    = false
        podman.enabled         = false
        shifter.enabled        = false
        charliecloud.enabled   = false
    }
    
    arm {
        docker.runOptions = '-u $(id -u):$(id -g) --platform=linux/amd64'
    }
    
    singularity {
        singularity.enabled    = true
        singularity.autoMounts = true
        docker.enabled         = false
        podman.enabled         = false
        shifter.enabled        = false
        charliecloud.enabled   = false
    }
    
    podman {
        podman.enabled         = true
        docker.enabled         = false
        singularity.enabled    = false
        shifter.enabled        = false
        charliecloud.enabled   = false
    }
    
    shifter {
        shifter.enabled        = true
        docker.enabled         = false
        singularity.enabled    = false
        podman.enabled         = false
        charliecloud.enabled   = false
    }
    
    charliecloud {
        charliecloud.enabled   = true
        docker.enabled         = false
        singularity.enabled    = false
        podman.enabled         = false
        shifter.enabled        = false
    }
    
    local {
        process.executor       = 'local'
        process.cpus           = 4
        process.memory         = '8.GB'
    }
    
    cluster {
        process.executor       = 'slurm'
        process.queue          = 'normal'
        process.clusterOptions = '--account=your_account'
    }
    
    cloud {
        process.executor       = 'google-batch'
        google.location        = 'us-central1'
        google.region          = 'us-central1'
        workDir                = 'gs://your-bucket/work'
    }
    
    test {
        includeConfig 'conf/test.config'
    }
    
    test_full {
        includeConfig 'conf/test_full.config'
    }
}

// Export these variables to prevent local Python/R libraries from conflicting with those in the container
// The JULIA depot path has been adjusted to a fixed path `/usr/local/share/julia` that needs to be used for packages in the container.
// See https://apeltzer.github.io/post/03-julia-lang-nextflow/ for details on that. Once we have a common agreement on where to keep Julia packages, this is adjustable.

env {
    PYTHONNOUSERSITE = 1
    R_PROFILE_USER   = "/.Rprofile"
    R_ENVIRON_USER   = "/.Renviron"
    JULIA_DEPOT_PATH = "/usr/local/share/julia"
}

// Capture exit codes from upstream processes when piping
process.shell = ['/bin/bash', '-euo', 'pipefail']

def trace_timestamp = new java.util.Date().format( 'yyyy-MM-dd_HH-mm-ss')
timeline {
    enabled = true
    file    = "${params.output_dir}/pipeline_info/execution_timeline_${trace_timestamp}.html"
}
report {
    enabled = true
    file    = "${params.output_dir}/pipeline_info/execution_report_${trace_timestamp}.html"
}
trace {
    enabled = true
    file    = "${params.output_dir}/pipeline_info/execution_trace_${trace_timestamp}.txt"
}
dag {
    enabled = true
    file    = "${params.output_dir}/pipeline_info/pipeline_dag_${trace_timestamp}.svg"
}

manifest {
    name            = 'microc-pipeline'
    author          = 'Converted from Python implementation'
    homePage        = 'https://github.com/your-org/microc-pipeline'
    description     = 'Nextflow pipeline for processing Micro-C sequencing data'
    mainScript      = 'main.nf'
    nextflowVersion = '!>=21.10.3'
    version         = '1.0.0'
}

// Load igenomes.config if required
if (!params.igenomes_ignore) {
    includeConfig 'conf/igenomes.config'
} else {
    params.genomes = [:]
}

// Function to ensure that resource requirements don't go beyond
// a maximum limit
def check_max(obj, type) {
    if (type == 'memory') {
        try {
            if (obj.compareTo(params.max_memory as nextflow.util.MemoryUnit) == 1)
                return params.max_memory as nextflow.util.MemoryUnit
            else
                return obj
        } catch (all) {
            println "   ### ERROR ###   You have not specified a max memory limit. Please set params.max_memory"
            return obj
        }
    } else if (type == 'time') {
        try {
            if (obj.compareTo(params.max_time as nextflow.util.Duration) == 1)
                return params.max_time as nextflow.util.Duration
            else
                return obj
        } catch (all) {
            println "   ### ERROR ###   You have not specified a max time limit. Please set params.max_time"
            return obj
        }
    } else if (type == 'cpus') {
        try {
            return Math.min( obj, params.max_cpus as int )
        } catch (all) {
            println "   ### ERROR ###   You have not specified a max cpus limit. Please set params.max_cpus"
            return obj
        }
    }
}
