Jun-09 09:03:59.749 [main] DEBUG nextflow.cli.Launcher - $> nextflow run nf/main.nf -profile test
Jun-09 09:03:59.810 [main] INFO  nextflow.cli.CmdRun - N E X T F L O W  ~  version 23.10.1
Jun-09 09:03:59.823 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/Users/<USER>/.nextflow/plugins; core-plugins: nf-amazon@2.1.4,nf-azure@1.3.3,nf-cloudcache@0.3.0,nf-codecommit@0.1.5,nf-console@1.0.6,nf-ga4gh@1.1.0,nf-google@1.8.3,nf-tower@1.6.3,nf-wave@1.0.1
Jun-09 09:03:59.832 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
Jun-09 09:03:59.833 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
Jun-09 09:03:59.835 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.4.1 in 'deployment' mode
Jun-09 09:03:59.841 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
Jun-09 09:03:59.851 [main] DEBUG nextflow.config.ConfigBuilder - Found config base: /Users/<USER>/projects/topology-tools/nf/nextflow.config
Jun-09 09:03:59.852 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /Users/<USER>/projects/topology-tools/nf/nextflow.config
Jun-09 09:03:59.865 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `test`
Jun-09 09:04:00.523 [main] DEBUG nextflow.config.ConfigBuilder - Available config profiles: [cluster, debug, test, local, docker, cloud, shifter, mamba, charliecloud, conda, singularity, arm, test_full, podman]
Jun-09 09:04:00.545 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declararion
Jun-09 09:04:00.559 [main] INFO  nextflow.cli.CmdRun - Launching `nf/main.nf` [zen_poincare] DSL2 - revision: cb60860841
Jun-09 09:04:00.559 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
Jun-09 09:04:00.559 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[]
Jun-09 09:04:00.566 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /Users/<USER>/.nextflow/secrets/store.json
Jun-09 09:04:00.568 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@52ea0269] - activable => nextflow.secret.LocalSecretsProvider@52ea0269
Jun-09 09:04:00.611 [main] DEBUG nextflow.Session - Session UUID: 63ab83f0-058c-4f40-b77c-a1fc1214b201
Jun-09 09:04:00.611 [main] DEBUG nextflow.Session - Run name: zen_poincare
Jun-09 09:04:00.611 [main] DEBUG nextflow.Session - Executor pool size: 10
Jun-09 09:04:00.616 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
Jun-09 09:04:00.619 [main] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'FileTransfer' minSize=10; maxSize=30; workQueue=LinkedBlockingQueue[10000]; allowCoreThreadTimeout=false
Jun-09 09:04:00.650 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 23.10.1 build 5891
  Created: 12-01-2024 22:01 UTC (17:01 EDT)
  System: Mac OS X 15.4.1
  Runtime: Groovy 3.0.19 on OpenJDK 64-Bit Server VM 11.0.26+4-LTS
  Encoding: UTF-8 (UTF-8)
  Process: <EMAIL> [127.0.0.1]
  CPUs: 10 - Mem: 64 GB (1.9 GB) - Swap: 7 GB (1.4 GB)
Jun-09 09:04:00.659 [main] DEBUG nextflow.Session - Work-dir: /Users/<USER>/projects/topology-tools/work [Mac OS X]
Jun-09 09:04:00.659 [main] DEBUG nextflow.Session - Script base path does not exist or is not a directory: /Users/<USER>/projects/topology-tools/nf/bin
Jun-09 09:04:00.667 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
Jun-09 09:04:00.673 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
Jun-09 09:04:00.701 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
Jun-09 09:04:00.709 [main] DEBUG nextflow.util.CustomThreadPool - Creating default thread pool > poolSize: 11; maxThreads: 1000
Jun-09 09:04:00.760 [main] DEBUG nextflow.Session - Session start
Jun-09 09:04:00.763 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow started -- trace file: /Users/<USER>/projects/topology-tools/test_results/pipeline_info/execution_trace_2025-06-09_09-04-00.txt
Jun-09 09:04:00.981 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
Jun-09 09:04:01.341 [main] INFO  nextflow.Nextflow - 
========================================================================================
                        Micro-C Pipeline - Nextflow DSL2
========================================================================================
Sample ID       : small-rcmc
FASTQ R1        : tests/small-region-capture-micro-c/small_rcmc_r1.fq.gz,tests/small-region-capture-micro-c/small_rcmc-extra-reads_r1.fq.gz
FASTQ R2        : tests/small-region-capture-micro-c/small_rcmc_r2.fq.gz,tests/small-region-capture-micro-c/small_rcmc-extra-reads_r2.fq.gz
BWA Index       : tests/small-region-capture-micro-c/test_bwa_index.tgz
Chrom Sizes     : tests/small-region-capture-micro-c/test.chrom.sizes
Output Dir      : ./test_results
Resolution      : 1000
BWA Cores       : 2
Min MAPQ        : 20
========================================================================================

Jun-09 09:04:01.430 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name EXTRACT_BWA_INDEX
Jun-09 09:04:01.431 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:EXTRACT_BWA_INDEX` matches process EXTRACT_BWA_INDEX
Jun-09 09:04:01.435 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 09:04:01.435 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 09:04:01.439 [main] DEBUG nextflow.executor.Executor - [warm up] executor > local
Jun-09 09:04:01.442 [main] DEBUG n.processor.LocalPollingMonitor - Creating local task monitor for executor 'local' > cpus=10; memory=64 GB; capacity=10; pollInterval=100ms; dumpInterval=5m
Jun-09 09:04:01.444 [main] DEBUG n.processor.TaskPollingMonitor - >>> barrier register (monitor: local)
Jun-09 09:04:01.489 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name BWA_ALIGN_PAIRTOOLS
Jun-09 09:04:01.489 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:BWA_ALIGN_PAIRTOOLS` matches process BWA_ALIGN_PAIRTOOLS
Jun-09 09:04:01.490 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 09:04:01.490 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 09:04:01.501 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name MERGE_DEDUP_SPLIT
Jun-09 09:04:01.502 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:MERGE_DEDUP_SPLIT` matches process MERGE_DEDUP_SPLIT
Jun-09 09:04:01.502 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 09:04:01.502 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 09:04:01.507 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name COOLER_PROCESS
Jun-09 09:04:01.507 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:COOLER_PROCESS` matches process COOLER_PROCESS
Jun-09 09:04:01.508 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 09:04:01.508 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 09:04:01.514 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name JUICER_HIC
Jun-09 09:04:01.514 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:JUICER_HIC` matches process JUICER_HIC
Jun-09 09:04:01.517 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 09:04:01.517 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 09:04:01.520 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name QC_METRICS
Jun-09 09:04:01.520 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:QC_METRICS` matches process QC_METRICS
Jun-09 09:04:01.520 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 09:04:01.520 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 09:04:01.522 [main] DEBUG nextflow.Session - Workflow process names [dsl2]: MERGE_DEDUP_SPLIT, COOLER_PROCESS, EXTRACT_BWA_INDEX, JUICER_HIC, BWA_ALIGN_PAIRTOOLS, QC_METRICS
Jun-09 09:04:01.523 [main] DEBUG nextflow.Session - Igniting dataflow network (9)
Jun-09 09:04:01.529 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > EXTRACT_BWA_INDEX
Jun-09 09:04:01.529 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > BWA_ALIGN_PAIRTOOLS
Jun-09 09:04:01.529 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > MERGE_DEDUP_SPLIT
Jun-09 09:04:01.529 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > COOLER_PROCESS
Jun-09 09:04:01.530 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > JUICER_HIC
Jun-09 09:04:01.530 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > QC_METRICS
Jun-09 09:04:01.531 [main] DEBUG nextflow.script.ScriptRunner - Parsed script files:
  Script_43f01a504ba15b5e: /Users/<USER>/projects/topology-tools/nf/./modules/merge_dedup_split.nf
  Script_6c968723c735dde5: /Users/<USER>/projects/topology-tools/nf/./modules/qc_metrics.nf
  Script_900e8f290a0d3a82: /Users/<USER>/projects/topology-tools/nf/./modules/cooler_process.nf
  Script_3c186acb8ea2eb2c: /Users/<USER>/projects/topology-tools/nf/./modules/juicer_hic.nf
  Script_59383c090845de85: /Users/<USER>/projects/topology-tools/nf/main.nf
  Script_f4e17a1e4235a98c: /Users/<USER>/projects/topology-tools/nf/./modules/extract_bwa_index.nf
  Script_e05012147db5f27a: /Users/<USER>/projects/topology-tools/nf/./modules/bwa_align_pairtools.nf
Jun-09 09:04:01.532 [main] DEBUG nextflow.script.ScriptRunner - > Awaiting termination 
Jun-09 09:04:01.532 [main] DEBUG nextflow.Session - Session await
Jun-09 09:04:01.631 [Task submitter] WARN  n.executor.BashWrapperBuilder - Task runtime metrics are not reported when using macOS without a container engine
Jun-09 09:04:01.638 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 09:04:01.645 [Task submitter] INFO  nextflow.Session - [ef/aa4a3e] Submitted process > EXTRACT_BWA_INDEX (small_rcmc-extra-reads_r1.fq)
Jun-09 09:04:01.650 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 09:04:01.650 [Task submitter] INFO  nextflow.Session - [18/0c222c] Submitted process > EXTRACT_BWA_INDEX (small_rcmc_r1.fq)
Jun-09 09:04:01.730 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 2; name: EXTRACT_BWA_INDEX (small_rcmc-extra-reads_r1.fq); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/ef/aa4a3e9b77fc09f243585e5c9db63e]
Jun-09 09:04:01.758 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 1; name: EXTRACT_BWA_INDEX (small_rcmc_r1.fq); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/18/0c222c5d95d98e85bbe4baa28a66e0]
Jun-09 09:04:01.759 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 09:04:01.760 [Task submitter] INFO  nextflow.Session - [50/fd11e5] Submitted process > BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq)
Jun-09 09:04:01.766 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 09:04:01.766 [Task submitter] INFO  nextflow.Session - [e4/dcf6dd] Submitted process > BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq)
Jun-09 09:04:03.944 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 4; name: BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/e4/dcf6dd975c32a604685d9e1fa11d93]
Jun-09 09:04:11.090 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 3; name: BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/50/fd11e50a0b796bf1adbd98b2b6e21c]
Jun-09 09:04:11.101 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 09:04:11.101 [Task submitter] INFO  nextflow.Session - [8b/00008b] Submitted process > MERGE_DEDUP_SPLIT (small-rcmc)
Jun-09 09:04:14.810 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 5; name: MERGE_DEDUP_SPLIT (small-rcmc); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/8b/00008b12c95363e795ba483b7a563e]
Jun-09 09:04:14.818 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 09:04:14.818 [Task submitter] INFO  nextflow.Session - [12/6417e3] Submitted process > QC_METRICS (small-rcmc)
Jun-09 09:04:14.824 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 09:04:14.824 [Task submitter] INFO  nextflow.Session - [b4/21da1b] Submitted process > COOLER_PROCESS (small-rcmc)
Jun-09 09:04:14.827 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 09:04:14.827 [Task submitter] INFO  nextflow.Session - [6e/8be72d] Submitted process > JUICER_HIC (small-rcmc)
Jun-09 09:04:14.914 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 8; name: QC_METRICS (small-rcmc); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/12/6417e321de310064dab7fcd43e38b9]
Jun-09 09:04:16.665 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 6; name: JUICER_HIC (small-rcmc); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/6e/8be72dbc5baea239299e49e6e42d9c]
Jun-09 09:04:16.667 [Task monitor] DEBUG nextflow.processor.TaskProcessor - Process `JUICER_HIC (small-rcmc)` is unable to find [UnixPath]: `/Users/<USER>/projects/topology-tools/work/6e/8be72dbc5baea239299e49e6e42d9c/versions.yml` (pattern: `versions.yml`)
Jun-09 09:04:16.669 [Task monitor] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=JUICER_HIC (small-rcmc); work-dir=/Users/<USER>/projects/topology-tools/work/6e/8be72dbc5baea239299e49e6e42d9c
  error [nextflow.exception.MissingFileException]: Missing output file(s) `versions.yml` expected by process `JUICER_HIC (small-rcmc)`
Jun-09 09:04:16.670 [Task monitor] INFO  nextflow.processor.TaskProcessor - [6e/8be72d] NOTE: Missing output file(s) `versions.yml` expected by process `JUICER_HIC (small-rcmc)` -- Error is ignored
Jun-09 09:04:20.599 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 7; name: COOLER_PROCESS (small-rcmc); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/b4/21da1bfe59830f602d916eaaa27927]
Jun-09 09:04:20.602 [main] DEBUG nextflow.Session - Session await > all processes finished
Jun-09 09:04:20.603 [Task monitor] DEBUG n.processor.TaskPollingMonitor - <<< barrier arrives (monitor: local) - terminating tasks monitor poll loop
Jun-09 09:04:20.603 [main] DEBUG nextflow.Session - Session await > all barriers passed
Jun-09 09:04:20.604 [main] INFO  nextflow.Nextflow - 
========================================================================================
                        Pipeline Execution Summary
========================================================================================
Completed at    : 2025-06-09T09:04:20.604083-04:00
Duration        : 19.9s
Success         : true
Work directory  : /Users/<USER>/projects/topology-tools/work
Exit status     : 0
Error message   : None
========================================================================================

Jun-09 09:04:20.605 [main] INFO  nextflow.Nextflow - 🎉 Pipeline completed successfully!
Jun-09 09:04:20.605 [main] INFO  nextflow.Nextflow - 📁 Results are in: ./test_results
Jun-09 09:04:20.607 [main] DEBUG n.trace.WorkflowStatsObserver - Workflow completed > WorkflowStats[succeededCount=7; failedCount=1; ignoredCount=1; cachedCount=0; pendingCount=0; submittedCount=0; runningCount=0; retriesCount=0; abortedCount=0; succeedDuration=42s; failedDuration=3.6s; cachedDuration=0ms;loadCpus=0; loadMemory=0; peakRunning=2; peakCpus=4; peakMemory=12 GB; ]
Jun-09 09:04:20.607 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow completed -- saving trace file
Jun-09 09:04:20.607 [main] DEBUG nextflow.trace.ReportObserver - Workflow completed -- rendering execution report
Jun-09 09:04:21.514 [main] DEBUG nextflow.trace.TimelineObserver - Workflow completed -- rendering execution timeline
Jun-09 09:04:21.616 [main] WARN  nextflow.dag.GraphvizRenderer - Graphviz is required to render the execution DAG in the given format -- See http://www.graphviz.org for more info.
Jun-09 09:04:21.697 [main] DEBUG nextflow.cache.CacheDB - Closing CacheDB done
Jun-09 09:04:21.708 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'FileTransfer' shutdown completed (hard=false)
Jun-09 09:04:21.708 [main] DEBUG nextflow.script.ScriptRunner - > Execution complete -- Goodbye
