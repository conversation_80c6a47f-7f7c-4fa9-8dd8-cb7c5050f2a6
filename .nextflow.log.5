Jun-09 08:49:21.220 [main] DEBUG nextflow.cli.Launcher - $> nextflow run nf/main.nf -profile test -stub-run -resume
Jun-09 08:49:21.280 [main] DEBUG nextflow.cli.CmdRun - N E X T F L O W  ~  version 25.04.3
Jun-09 08:49:21.305 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/Users/<USER>/.nextflow/plugins; core-plugins: nf-amazon@2.15.0,nf-azure@1.16.0,nf-cloudcache@0.4.3,nf-codecommit@0.2.3,nf-console@1.2.1,nf-google@1.21.0,nf-k8s@1.0.0,nf-tower@1.11.3,nf-wave@1.12.1
Jun-09 08:49:21.328 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
Jun-09 08:49:21.328 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
Jun-09 08:49:21.331 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.12.0 in 'deployment' mode
Jun-09 08:49:21.338 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
Jun-09 08:49:21.352 [main] DEBUG nextflow.config.ConfigBuilder - Found config base: /Users/<USER>/projects/topology-tools/nf/nextflow.config
Jun-09 08:49:21.355 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /Users/<USER>/projects/topology-tools/nf/nextflow.config
Jun-09 08:49:21.378 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /Users/<USER>/.nextflow/secrets/store.json
Jun-09 08:49:21.380 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@7cedfa63] - activable => nextflow.secret.LocalSecretsProvider@7cedfa63
Jun-09 08:49:21.384 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `test`
Jun-09 08:49:22.006 [main] DEBUG nextflow.config.ConfigBuilder - Available config profiles: [cluster, debug, test, local, docker, cloud, shifter, mamba, charliecloud, conda, singularity, arm, test_full, podman]
Jun-09 08:49:22.028 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declaration
Jun-09 08:49:22.039 [main] DEBUG nextflow.cli.CmdRun - Launching `nf/main.nf` [spontaneous_pasteur] DSL2 - revision: cb60860841
Jun-09 08:49:22.040 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
Jun-09 08:49:22.040 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[]
Jun-09 08:49:22.074 [main] DEBUG nextflow.Session - Session UUID: e5aaa852-a770-4009-a442-be1302948275
Jun-09 08:49:22.074 [main] DEBUG nextflow.Session - Run name: spontaneous_pasteur
Jun-09 08:49:22.075 [main] DEBUG nextflow.Session - Executor pool size: 10
Jun-09 08:49:22.079 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
Jun-09 08:49:22.083 [main] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'FileTransfer' minSize=10; maxSize=30; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jun-09 08:49:22.099 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 25.04.3 build 5949
  Created: 02-06-2025 20:56 UTC (16:56 EDT)
  System: Mac OS X 15.4.1
  Runtime: Groovy 4.0.26 on OpenJDK 64-Bit Server VM 23.0.2+7
  Encoding: UTF-8 (UTF-8)
  Process: <EMAIL> [127.0.0.1]
  CPUs: 10 - Mem: 64 GB (603.5 MB) - Swap: 7 GB (1.4 GB)
Jun-09 08:49:22.111 [main] DEBUG nextflow.Session - Work-dir: /Users/<USER>/projects/topology-tools/work [Mac OS X]
Jun-09 08:49:22.111 [main] DEBUG nextflow.Session - Script base path does not exist or is not a directory: /Users/<USER>/projects/topology-tools/nf/bin
Jun-09 08:49:22.118 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
Jun-09 08:49:22.124 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
Jun-09 08:49:22.139 [main] DEBUG nextflow.Session - Observer factory (v2): LinObserverFactory
Jun-09 08:49:22.163 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
Jun-09 08:49:22.169 [main] DEBUG nextflow.util.CustomThreadPool - Creating default thread pool > poolSize: 11; maxThreads: 1000
Jun-09 08:49:22.225 [main] DEBUG nextflow.Session - Session start
Jun-09 08:49:22.228 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow started -- trace file: /Users/<USER>/projects/topology-tools/test_results/pipeline_info/execution_trace_2025-06-09_08-49-21.txt
Jun-09 08:49:22.407 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
Jun-09 08:49:22.715 [main] INFO  nextflow.Nextflow - 
========================================================================================
                        Micro-C Pipeline - Nextflow DSL2
========================================================================================
Sample ID       : small-rcmc
FASTQ R1        : tests/small-region-capture-micro-c/small_rcmc_r1.fq.gz,tests/small-region-capture-micro-c/small_rcmc-extra-reads_r1.fq.gz
FASTQ R2        : tests/small-region-capture-micro-c/small_rcmc_r2.fq.gz,tests/small-region-capture-micro-c/small_rcmc-extra-reads_r2.fq.gz
BWA Index       : tests/small-region-capture-micro-c/test_bwa_index.tgz
Chrom Sizes     : tests/small-region-capture-micro-c/test.chrom.sizes
Output Dir      : ./test_results
Resolution      : 1000
BWA Cores       : 2
Min MAPQ        : 20
========================================================================================

Jun-09 08:49:22.809 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name EXTRACT_BWA_INDEX
Jun-09 08:49:22.811 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:EXTRACT_BWA_INDEX` matches process EXTRACT_BWA_INDEX
Jun-09 08:49:22.819 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:49:22.820 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:49:22.825 [main] DEBUG nextflow.executor.Executor - [warm up] executor > local
Jun-09 08:49:22.829 [main] DEBUG n.processor.LocalPollingMonitor - Creating local task monitor for executor 'local' > cpus=10; memory=64 GB; capacity=10; pollInterval=100ms; dumpInterval=5m
Jun-09 08:49:22.831 [main] DEBUG n.processor.TaskPollingMonitor - >>> barrier register (monitor: local)
Jun-09 08:49:22.844 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'EXTRACT_BWA_INDEX': maxForks=0; fair=false; array=0
Jun-09 08:49:22.870 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name BWA_ALIGN_PAIRTOOLS
Jun-09 08:49:22.871 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:BWA_ALIGN_PAIRTOOLS` matches process BWA_ALIGN_PAIRTOOLS
Jun-09 08:49:22.872 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:49:22.872 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:49:22.873 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'BWA_ALIGN_PAIRTOOLS': maxForks=0; fair=false; array=0
Jun-09 08:49:22.886 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name MERGE_DEDUP_SPLIT
Jun-09 08:49:22.887 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:MERGE_DEDUP_SPLIT` matches process MERGE_DEDUP_SPLIT
Jun-09 08:49:22.888 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:49:22.889 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:49:22.889 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'MERGE_DEDUP_SPLIT': maxForks=0; fair=false; array=0
Jun-09 08:49:22.895 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name COOLER_PROCESS
Jun-09 08:49:22.896 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:COOLER_PROCESS` matches process COOLER_PROCESS
Jun-09 08:49:22.897 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:49:22.897 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:49:22.898 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'COOLER_PROCESS': maxForks=0; fair=false; array=0
Jun-09 08:49:22.906 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name JUICER_HIC
Jun-09 08:49:22.906 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:JUICER_HIC` matches process JUICER_HIC
Jun-09 08:49:22.908 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:49:22.908 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:49:22.908 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'JUICER_HIC': maxForks=0; fair=false; array=0
Jun-09 08:49:22.914 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name QC_METRICS
Jun-09 08:49:22.914 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:QC_METRICS` matches process QC_METRICS
Jun-09 08:49:22.915 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:49:22.915 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:49:22.916 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'QC_METRICS': maxForks=0; fair=false; array=0
Jun-09 08:49:22.918 [main] DEBUG nextflow.Session - Workflow process names [dsl2]: MERGE_DEDUP_SPLIT, COOLER_PROCESS, EXTRACT_BWA_INDEX, JUICER_HIC, BWA_ALIGN_PAIRTOOLS, QC_METRICS
Jun-09 08:49:22.920 [main] DEBUG nextflow.Session - Igniting dataflow network (9)
Jun-09 08:49:22.926 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > EXTRACT_BWA_INDEX
Jun-09 08:49:22.926 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > BWA_ALIGN_PAIRTOOLS
Jun-09 08:49:22.926 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > MERGE_DEDUP_SPLIT
Jun-09 08:49:22.926 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > COOLER_PROCESS
Jun-09 08:49:22.926 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > JUICER_HIC
Jun-09 08:49:22.926 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > QC_METRICS
Jun-09 08:49:22.927 [main] DEBUG nextflow.script.ScriptRunner - Parsed script files:
  Script_43f01a504ba15b5e: /Users/<USER>/projects/topology-tools/nf/modules/merge_dedup_split.nf
  Script_6c968723c735dde5: /Users/<USER>/projects/topology-tools/nf/modules/qc_metrics.nf
  Script_900e8f290a0d3a82: /Users/<USER>/projects/topology-tools/nf/modules/cooler_process.nf
  Script_a457b5df1e6a90ec: /Users/<USER>/projects/topology-tools/nf/modules/bwa_align_pairtools.nf
  Script_3c186acb8ea2eb2c: /Users/<USER>/projects/topology-tools/nf/modules/juicer_hic.nf
  Script_59383c090845de85: /Users/<USER>/projects/topology-tools/nf/main.nf
  Script_f4e17a1e4235a98c: /Users/<USER>/projects/topology-tools/nf/modules/extract_bwa_index.nf
Jun-09 08:49:22.927 [main] DEBUG nextflow.script.ScriptRunner - > Awaiting termination 
Jun-09 08:49:22.927 [main] DEBUG nextflow.Session - Session await
Jun-09 08:49:23.041 [Actor Thread 7] INFO  nextflow.processor.TaskProcessor - [c5/9f78e7] Cached process > EXTRACT_BWA_INDEX (small_rcmc_r1.fq)
Jun-09 08:49:23.041 [Actor Thread 6] INFO  nextflow.processor.TaskProcessor - [c5/3ba4ca] Cached process > EXTRACT_BWA_INDEX (small_rcmc-extra-reads_r1.fq)
Jun-09 08:49:23.091 [Actor Thread 5] INFO  nextflow.processor.TaskProcessor - [f0/1aa16f] Cached process > BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq)
Jun-09 08:49:23.091 [Actor Thread 10] INFO  nextflow.processor.TaskProcessor - [2b/c26573] Cached process > BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq)
Jun-09 08:49:23.107 [Actor Thread 8] INFO  nextflow.processor.TaskProcessor - [d1/131582] Cached process > MERGE_DEDUP_SPLIT (small-rcmc)
Jun-09 08:49:23.117 [Actor Thread 3] INFO  nextflow.processor.TaskProcessor - [0f/e829cd] Cached process > QC_METRICS (small-rcmc)
Jun-09 08:49:23.119 [Actor Thread 2] DEBUG nextflow.processor.TaskProcessor - Process `JUICER_HIC` is unable to find [UnixPath]: `/Users/<USER>/projects/topology-tools/work/15/0c04dd6270bce8c28dc424b7d6cc07/juicer_error.log` (pattern: `juicer_error.log`)
Jun-09 08:49:23.120 [Actor Thread 2] INFO  nextflow.processor.TaskProcessor - [15/0c04dd] Cached process > JUICER_HIC (small-rcmc)
Jun-09 08:49:23.121 [Actor Thread 4] INFO  nextflow.processor.TaskProcessor - [4e/6d54e8] Cached process > COOLER_PROCESS (small-rcmc)
Jun-09 08:49:23.182 [Actor Thread 2] DEBUG nextflow.processor.TaskProcessor - Process JUICER_HIC > Skipping output binding because one or more optional files are missing: fileoutparam<3>
Jun-09 08:49:23.184 [main] DEBUG nextflow.Session - Session await > all processes finished
Jun-09 08:49:23.246 [Task monitor] DEBUG n.processor.TaskPollingMonitor - <<< barrier arrives (monitor: local) - terminating tasks monitor poll loop
Jun-09 08:49:23.246 [main] DEBUG nextflow.Session - Session await > all barriers passed
Jun-09 08:49:23.248 [main] INFO  nextflow.Nextflow - 
========================================================================================
                        Pipeline Execution Summary
========================================================================================
Completed at    : 2025-06-09T08:49:23.247143-04:00
Duration        : 1.1s
Success         : true
Work directory  : /Users/<USER>/projects/topology-tools/work
Exit status     : 0
Error message   : None
========================================================================================

Jun-09 08:49:23.249 [main] INFO  nextflow.Nextflow - 🎉 Pipeline completed successfully!
Jun-09 08:49:23.249 [main] INFO  nextflow.Nextflow - 📁 Results are in: ./test_results
Jun-09 08:49:23.252 [main] DEBUG n.trace.WorkflowStatsObserver - Workflow completed > WorkflowStats[succeededCount=0; failedCount=0; ignoredCount=0; cachedCount=8; pendingCount=0; submittedCount=0; runningCount=0; retriesCount=0; abortedCount=0; succeedDuration=0ms; failedDuration=0ms; cachedDuration=398ms;loadCpus=0; loadMemory=0; peakRunning=0; peakCpus=0; peakMemory=0; ]
Jun-09 08:49:23.252 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow completed -- saving trace file
Jun-09 08:49:23.253 [main] DEBUG nextflow.trace.ReportObserver - Workflow completed -- rendering execution report
Jun-09 08:49:24.151 [main] DEBUG nextflow.trace.TimelineObserver - Workflow completed -- rendering execution timeline
Jun-09 08:49:24.254 [main] WARN  nextflow.dag.GraphvizRenderer - Graphviz is required to render the execution DAG in the given format -- See http://www.graphviz.org for more info.
Jun-09 08:49:24.339 [main] DEBUG nextflow.cache.CacheDB - Closing CacheDB done
Jun-09 08:49:24.350 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'FileTransfer' shutdown completed (hard=false)
Jun-09 08:49:24.350 [main] DEBUG nextflow.script.ScriptRunner - > Execution complete -- Goodbye
