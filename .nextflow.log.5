Jun-09 08:09:11.204 [main] DEBUG nextflow.cli.Launcher - $> nextflow run nf/main.nf -profile test
Jun-09 08:09:11.260 [main] DEBUG nextflow.cli.CmdRun - N E X T F L O W  ~  version 25.04.3
Jun-09 08:09:11.284 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/Users/<USER>/.nextflow/plugins; core-plugins: nf-amazon@2.15.0,nf-azure@1.16.0,nf-cloudcache@0.4.3,nf-codecommit@0.2.3,nf-console@1.2.1,nf-google@1.21.0,nf-k8s@1.0.0,nf-tower@1.11.3,nf-wave@1.12.1
Jun-09 08:09:11.307 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
Jun-09 08:09:11.307 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
Jun-09 08:09:11.309 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.12.0 in 'deployment' mode
Jun-09 08:09:11.316 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
Jun-09 08:09:11.330 [main] DEBUG nextflow.config.ConfigBuilder - Found config base: /Users/<USER>/projects/topology-tools/nf/nextflow.config
Jun-09 08:09:11.333 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /Users/<USER>/projects/topology-tools/nf/nextflow.config
Jun-09 08:09:11.359 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /Users/<USER>/.nextflow/secrets/store.json
Jun-09 08:09:11.362 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@73877e19] - activable => nextflow.secret.LocalSecretsProvider@73877e19
Jun-09 08:09:11.366 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `test`
Jun-09 08:09:12.006 [main] DEBUG nextflow.config.ConfigBuilder - Available config profiles: [cluster, debug, test, local, docker, cloud, shifter, mamba, charliecloud, conda, singularity, arm, test_full, podman]
Jun-09 08:09:12.028 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declaration
Jun-09 08:09:12.041 [main] DEBUG nextflow.cli.CmdRun - Launching `nf/main.nf` [intergalactic_heyrovsky] DSL2 - revision: 26f924d8d5
Jun-09 08:09:12.042 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
Jun-09 08:09:12.042 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[]
Jun-09 08:09:12.078 [main] DEBUG nextflow.Session - Session UUID: c6a9f635-329e-4375-b4c0-4ea14f68245f
Jun-09 08:09:12.079 [main] DEBUG nextflow.Session - Run name: intergalactic_heyrovsky
Jun-09 08:09:12.079 [main] DEBUG nextflow.Session - Executor pool size: 10
Jun-09 08:09:12.083 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
Jun-09 08:09:12.086 [main] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'FileTransfer' minSize=10; maxSize=30; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jun-09 08:09:12.103 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 25.04.3 build 5949
  Created: 02-06-2025 20:56 UTC (16:56 EDT)
  System: Mac OS X 15.4.1
  Runtime: Groovy 4.0.26 on OpenJDK 64-Bit Server VM 23.0.2+7
  Encoding: UTF-8 (UTF-8)
  Process: <EMAIL> [127.0.0.1]
  CPUs: 10 - Mem: 64 GB (83.3 MB) - Swap: 7 GB (1.4 GB)
Jun-09 08:09:12.113 [main] DEBUG nextflow.Session - Work-dir: /Users/<USER>/projects/topology-tools/work [Mac OS X]
Jun-09 08:09:12.114 [main] DEBUG nextflow.Session - Script base path does not exist or is not a directory: /Users/<USER>/projects/topology-tools/nf/bin
Jun-09 08:09:12.122 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
Jun-09 08:09:12.128 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
Jun-09 08:09:12.144 [main] DEBUG nextflow.Session - Observer factory (v2): LinObserverFactory
Jun-09 08:09:12.170 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
Jun-09 08:09:12.177 [main] DEBUG nextflow.util.CustomThreadPool - Creating default thread pool > poolSize: 11; maxThreads: 1000
Jun-09 08:09:12.242 [main] DEBUG nextflow.Session - Session start
Jun-09 08:09:12.246 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow started -- trace file: /Users/<USER>/projects/topology-tools/test_results/pipeline_info/execution_trace_2025-06-09_08-09-11.txt
Jun-09 08:09:12.404 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
Jun-09 08:09:12.756 [main] INFO  nextflow.Nextflow - 
========================================================================================
                        Micro-C Pipeline - Nextflow DSL2
========================================================================================
Sample ID       : test-sample
FASTQ R1        : ../tests/small-region-capture-micro-c/small_rcmc_r1.fq.gz
FASTQ R2        : ../tests/small-region-capture-micro-c/small_rcmc_r2.fq.gz
BWA Index       : ../tests/small-region-capture-micro-c/test_bwa_index.tgz
Chrom Sizes     : ../tests/small-region-capture-micro-c/test.chrom.sizes
Output Dir      : ./test_results
Resolution      : 1000
BWA Cores       : 2
Min MAPQ        : 20
========================================================================================

Jun-09 08:09:12.876 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name EXTRACT_BWA_INDEX
Jun-09 08:09:12.879 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:EXTRACT_BWA_INDEX` matches process EXTRACT_BWA_INDEX
Jun-09 08:09:12.889 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:09:12.890 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:09:12.894 [main] DEBUG nextflow.executor.Executor - [warm up] executor > local
Jun-09 08:09:12.898 [main] DEBUG n.processor.LocalPollingMonitor - Creating local task monitor for executor 'local' > cpus=10; memory=64 GB; capacity=10; pollInterval=100ms; dumpInterval=5m
Jun-09 08:09:12.900 [main] DEBUG n.processor.TaskPollingMonitor - >>> barrier register (monitor: local)
Jun-09 08:09:12.913 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'EXTRACT_BWA_INDEX': maxForks=0; fair=false; array=0
Jun-09 08:09:12.939 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name BWA_ALIGN_PAIRTOOLS
Jun-09 08:09:12.939 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:BWA_ALIGN_PAIRTOOLS` matches process BWA_ALIGN_PAIRTOOLS
Jun-09 08:09:12.941 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:09:12.941 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:09:12.942 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'BWA_ALIGN_PAIRTOOLS': maxForks=0; fair=false; array=0
Jun-09 08:09:12.955 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name MERGE_DEDUP_SPLIT
Jun-09 08:09:12.955 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:MERGE_DEDUP_SPLIT` matches process MERGE_DEDUP_SPLIT
Jun-09 08:09:12.957 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:09:12.957 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:09:12.958 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'MERGE_DEDUP_SPLIT': maxForks=0; fair=false; array=0
Jun-09 08:09:12.965 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name COOLER_PROCESS
Jun-09 08:09:12.966 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:COOLER_PROCESS` matches process COOLER_PROCESS
Jun-09 08:09:12.967 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:09:12.967 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:09:12.968 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'COOLER_PROCESS': maxForks=0; fair=false; array=0
Jun-09 08:09:12.976 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name JUICER_HIC
Jun-09 08:09:12.976 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:JUICER_HIC` matches process JUICER_HIC
Jun-09 08:09:12.978 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:09:12.978 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:09:12.978 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'JUICER_HIC': maxForks=0; fair=false; array=0
Jun-09 08:09:12.984 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name QC_METRICS
Jun-09 08:09:12.985 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:QC_METRICS` matches process QC_METRICS
Jun-09 08:09:12.986 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:09:12.986 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:09:12.986 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'QC_METRICS': maxForks=0; fair=false; array=0
Jun-09 08:09:12.989 [main] DEBUG nextflow.Session - Workflow process names [dsl2]: MERGE_DEDUP_SPLIT, COOLER_PROCESS, EXTRACT_BWA_INDEX, JUICER_HIC, BWA_ALIGN_PAIRTOOLS, QC_METRICS
Jun-09 08:09:12.991 [main] DEBUG nextflow.Session - Igniting dataflow network (10)
Jun-09 08:09:12.996 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > EXTRACT_BWA_INDEX
Jun-09 08:09:12.996 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > BWA_ALIGN_PAIRTOOLS
Jun-09 08:09:12.996 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > MERGE_DEDUP_SPLIT
Jun-09 08:09:12.996 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > COOLER_PROCESS
Jun-09 08:09:12.996 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > JUICER_HIC
Jun-09 08:09:12.996 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > QC_METRICS
Jun-09 08:09:12.997 [main] DEBUG nextflow.script.ScriptRunner - Parsed script files:
  Script_43f01a504ba15b5e: /Users/<USER>/projects/topology-tools/nf/modules/merge_dedup_split.nf
  Script_6c968723c735dde5: /Users/<USER>/projects/topology-tools/nf/modules/qc_metrics.nf
  Script_a457b5df1e6a90ec: /Users/<USER>/projects/topology-tools/nf/modules/bwa_align_pairtools.nf
  Script_3c186acb8ea2eb2c: /Users/<USER>/projects/topology-tools/nf/modules/juicer_hic.nf
  Script_900e8f290a0d3a82: /Users/<USER>/projects/topology-tools/nf/modules/cooler_process.nf
  Script_3cb8aa18b03099d1: /Users/<USER>/projects/topology-tools/nf/main.nf
  Script_e2cca4cd5c47e076: /Users/<USER>/projects/topology-tools/nf/modules/extract_bwa_index.nf
Jun-09 08:09:12.997 [main] DEBUG nextflow.script.ScriptRunner - > Awaiting termination 
Jun-09 08:09:12.997 [main] DEBUG nextflow.Session - Session await
Jun-09 08:09:13.046 [Actor Thread 10] DEBUG nextflow.util.HashBuilder - Unable to get file attributes file: /Users/<USER>/projects/tests/small-region-capture-micro-c/test_bwa_index.tgz -- Cause: java.nio.file.NoSuchFileException: /Users/<USER>/projects/tests/small-region-capture-micro-c/test_bwa_index.tgz
Jun-09 08:09:13.103 [Task submitter] WARN  n.executor.BashWrapperBuilder - Task runtime metrics are not reported when using macOS without a container engine
Jun-09 08:09:13.115 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:09:13.116 [Task submitter] INFO  nextflow.Session - [ea/f8851c] Submitted process > EXTRACT_BWA_INDEX (small_rcmc_r1.fq)
Jun-09 08:09:13.447 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 1; name: EXTRACT_BWA_INDEX (small_rcmc_r1.fq); status: COMPLETED; exit: 1; error: -; workDir: /Users/<USER>/projects/topology-tools/work/ea/f8851c5384643909901b2c3f50c11e]
Jun-09 08:09:13.448 [Task monitor] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'TaskFinalizer' minSize=10; maxSize=30; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jun-09 08:09:13.454 [TaskFinalizer-1] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=EXTRACT_BWA_INDEX (small_rcmc_r1.fq); work-dir=/Users/<USER>/projects/topology-tools/work/ea/f8851c5384643909901b2c3f50c11e
  error [nextflow.exception.ProcessFailedException]: Process `EXTRACT_BWA_INDEX (small_rcmc_r1.fq)` terminated with an error exit status (1)
Jun-09 08:09:13.460 [TaskFinalizer-1] INFO  nextflow.processor.TaskProcessor - [ea/f8851c] NOTE: Process `EXTRACT_BWA_INDEX (small_rcmc_r1.fq)` terminated with an error exit status (1) -- Execution is retried (1)
Jun-09 08:09:13.467 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:09:13.467 [Task submitter] INFO  nextflow.Session - [8e/7185e9] Re-submitted process > EXTRACT_BWA_INDEX (small_rcmc_r1.fq)
Jun-09 08:09:13.514 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 2; name: EXTRACT_BWA_INDEX (small_rcmc_r1.fq); status: COMPLETED; exit: 1; error: -; workDir: /Users/<USER>/projects/topology-tools/work/8e/7185e98cb1f6293ce9a26c70f2c664]
Jun-09 08:09:13.515 [TaskFinalizer-2] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=EXTRACT_BWA_INDEX (small_rcmc_r1.fq); work-dir=/Users/<USER>/projects/topology-tools/work/8e/7185e98cb1f6293ce9a26c70f2c664
  error [nextflow.exception.ProcessFailedException]: Process `EXTRACT_BWA_INDEX (small_rcmc_r1.fq)` terminated with an error exit status (1)
Jun-09 08:09:13.516 [TaskFinalizer-2] INFO  nextflow.processor.TaskProcessor - [8e/7185e9] NOTE: Process `EXTRACT_BWA_INDEX (small_rcmc_r1.fq)` terminated with an error exit status (1) -- Execution is retried (2)
Jun-09 08:09:13.523 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:09:13.523 [Task submitter] INFO  nextflow.Session - [7b/2e1c56] Re-submitted process > EXTRACT_BWA_INDEX (small_rcmc_r1.fq)
Jun-09 08:09:13.572 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 3; name: EXTRACT_BWA_INDEX (small_rcmc_r1.fq); status: COMPLETED; exit: 1; error: -; workDir: /Users/<USER>/projects/topology-tools/work/7b/2e1c568d49c18ae378eb6f0f6e3961]
Jun-09 08:09:13.574 [TaskFinalizer-3] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=EXTRACT_BWA_INDEX (small_rcmc_r1.fq); work-dir=/Users/<USER>/projects/topology-tools/work/7b/2e1c568d49c18ae378eb6f0f6e3961
  error [nextflow.exception.ProcessFailedException]: Process `EXTRACT_BWA_INDEX (small_rcmc_r1.fq)` terminated with an error exit status (1)
Jun-09 08:09:13.583 [TaskFinalizer-3] ERROR nextflow.processor.TaskProcessor - Error executing process > 'EXTRACT_BWA_INDEX (small_rcmc_r1.fq)'

Caused by:
  Process `EXTRACT_BWA_INDEX (small_rcmc_r1.fq)` terminated with an error exit status (1)


Command executed:

  # Create genome index directory
  mkdir -p genome_index
  
  # Extract BWA index (compatible with BusyBox tar)
  if tar --help 2>&1 | grep -q "GNU tar"; then
      # GNU tar supports -z flag
      tar zxvf test_bwa_index.tgz -C genome_index
  else
      # BusyBox tar doesn't support -z, use gunzip + tar
      gunzip -c test_bwa_index.tgz | tar xvf - -C genome_index
  fi
  
  # Find the reference FASTA file (should have .bwt extension companion)
  bwt_file=$(find genome_index -name "*.bwt" | head -1)
  if [ -z "$bwt_file" ]; then
      echo "Error: No BWA index files found in test_bwa_index.tgz"
      exit 1
  fi
  
  # Log the extracted files
  echo "Extracted BWA index files:"
  ls -la genome_index/
  
  # Create versions file
  cat <<-END_VERSIONS > versions.yml
  "EXTRACT_BWA_INDEX":
      bwa: $(echo $(bwa 2>&1) | sed 's/^.*Version: //; s/Contact:.*$//')
      tar: $(tar --version 2>/dev/null | head -1 | sed 's/tar (GNU tar) //' || echo "BusyBox tar")
  END_VERSIONS

Command exit status:
  1

Command output:
  (empty)

Command error:
  gunzip: can't stat: test_bwa_index.tgz (test_bwa_index.tgz.gz): No such file or directory

Work dir:
  /Users/<USER>/projects/topology-tools/work/7b/2e1c568d49c18ae378eb6f0f6e3961

Tip: view the complete command output by changing to the process work dir and entering the command `cat .command.out`
Jun-09 08:09:13.586 [TaskFinalizer-3] DEBUG nextflow.Session - Session aborted -- Cause: Process `EXTRACT_BWA_INDEX (small_rcmc_r1.fq)` terminated with an error exit status (1)
Jun-09 08:09:13.588 [main] DEBUG nextflow.Session - Session await > all processes finished
Jun-09 08:09:13.600 [TaskFinalizer-3] ERROR nextflow.Nextflow - Pipeline execution stopped with the following message: gunzip: can't stat: test_bwa_index.tgz (test_bwa_index.tgz.gz): No such file or directory
Jun-09 08:09:13.600 [main] DEBUG nextflow.Session - Session await > all barriers passed
Jun-09 08:09:13.601 [Task monitor] DEBUG n.processor.TaskPollingMonitor - <<< barrier arrives (monitor: local) - terminating tasks monitor poll loop
Jun-09 08:09:13.603 [main] INFO  nextflow.Nextflow - 
========================================================================================
                        Pipeline Execution Summary
========================================================================================
Completed at    : 2025-06-09T08:09:13.601475-04:00
Duration        : 1.5s
Success         : false
Work directory  : /Users/<USER>/projects/topology-tools/work
Exit status     : 1
Error message   : gunzip: can't stat: test_bwa_index.tgz (test_bwa_index.tgz.gz): No such file or directory
========================================================================================

Jun-09 08:09:13.603 [main] ERROR nextflow.Nextflow - 💥 Pipeline failed!
Jun-09 08:09:13.603 [main] ERROR nextflow.Nextflow - Check the error message above and workflow logs for details.
Jun-09 08:09:13.606 [main] DEBUG n.trace.WorkflowStatsObserver - Workflow completed > WorkflowStats[succeededCount=0; failedCount=3; ignoredCount=0; cachedCount=0; pendingCount=0; submittedCount=0; runningCount=0; retriesCount=2; abortedCount=0; succeedDuration=0ms; failedDuration=468ms; cachedDuration=0ms;loadCpus=0; loadMemory=0; peakRunning=1; peakCpus=2; peakMemory=6 GB; ]
Jun-09 08:09:13.606 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow completed -- saving trace file
Jun-09 08:09:13.607 [main] DEBUG nextflow.trace.ReportObserver - Workflow completed -- rendering execution report
Jun-09 08:09:14.524 [main] DEBUG nextflow.trace.TimelineObserver - Workflow completed -- rendering execution timeline
Jun-09 08:09:14.662 [main] WARN  nextflow.dag.GraphvizRenderer - Graphviz is required to render the execution DAG in the given format -- See http://www.graphviz.org for more info.
Jun-09 08:09:14.666 [main] DEBUG nextflow.cache.CacheDB - Closing CacheDB done
Jun-09 08:09:14.676 [main] DEBUG nextflow.script.ScriptRunner - > Execution complete -- Goodbye
