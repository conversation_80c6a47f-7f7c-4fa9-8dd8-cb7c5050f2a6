/*
========================================================================================
    Test Configuration for Micro-C Pipeline
========================================================================================
    Configuration for running the pipeline with test data.
    This configuration matches the WDL JSON specification in:
    tests/small-region-capture-micro-c/small_rcmc.json
========================================================================================
*/

params {
    config_profile_name        = 'Test profile'
    config_profile_description = 'Test dataset matching WDL JSON specification (small_rcmc.json)'

    // Limit resources so that this can run on GitHub Actions
    max_cpus   = 2
    max_memory = '6.GB'
    max_time   = '6.h'

    // Input data - EXACT MATCH to WDL JSON specification
    // From small_rcmc.json: "microc.sample_id": "small-rcmc"
    sample_id                  = 'small-rcmc'

    // From small_rcmc.json: "microc.fastq_r1": [main_file, extra_reads_file]
    fastq_r1                   = 'tests/small-region-capture-micro-c/small_rcmc_r1.fq.gz,tests/small-region-capture-micro-c/small_rcmc-extra-reads_r1.fq.gz'

    // From small_rcmc.json: "microc.fastq_r2": [main_file, extra_reads_file]
    fastq_r2                   = 'tests/small-region-capture-micro-c/small_rcmc_r2.fq.gz,tests/small-region-capture-micro-c/small_rcmc-extra-reads_r2.fq.gz'

    // From small_rcmc.json: "microc.reference_bwa_idx"
    reference_bwa_idx          = 'tests/small-region-capture-micro-c/test_bwa_index.tgz'

    // From small_rcmc.json: "microc.chrom_sizes"
    chrom_sizes                = 'tests/small-region-capture-micro-c/test.chrom.sizes'

    // Pipeline parameters - EXACT MATCH to WDL JSON specification
    // From small_rcmc.json: "microc.cooler.resolution": "1000"
    resolution                 = 1000

    // From small_rcmc.json: "microc.microc_align.bwa_cores": "2"
    bwa_cores                  = 2

    // From small_rcmc.json: "microc.num_reads_per_chunk": 50000
    num_reads_per_chunk        = 50000

    // Default parameters (not specified in WDL JSON)
    mapq                       = 20

    // Output
    output_dir                 = './test_results'
}

process {
    // Reduce resource requirements for test
    withLabel:process_low {
        cpus   = 1
        memory = 2.GB
        time   = 1.h
    }
    withLabel:process_medium {
        cpus   = 2
        memory = 4.GB
        time   = 2.h
    }
    withLabel:process_high {
        cpus   = 2
        memory = 6.GB
        time   = 4.h
    }
}
