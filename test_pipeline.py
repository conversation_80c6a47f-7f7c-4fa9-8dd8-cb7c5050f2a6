#!/usr/bin/env python3
"""
Simple test script to verify the Micro-C pipeline works correctly.
"""

import os
import subprocess
import sys

def test_environment():
    """Test that all required tools are available"""
    print("Testing environment...")
    
    tools = ['bwa', 'samtools', 'pairtools', 'cooler']
    missing_tools = []
    
    for tool in tools:
        try:
            result = subprocess.run(['which', tool], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ {tool}: {result.stdout.strip()}")
            else:
                missing_tools.append(tool)
                print(f"❌ {tool}: not found")
        except Exception as e:
            missing_tools.append(tool)
            print(f"❌ {tool}: error checking - {e}")
    
    # Check Java
    try:
        result = subprocess.run(['java', '-version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ java: available")
        else:
            print(f"❌ java: not available")
    except Exception as e:
        print(f"❌ java: error checking - {e}")
    
    # Check Juicer tools
    juicer_jar = "juicer_tools/juicer_tools.jar"
    if os.path.exists(juicer_jar):
        print(f"✅ juicer_tools: {juicer_jar}")
    else:
        print(f"❌ juicer_tools: {juicer_jar} not found")
    
    return len(missing_tools) == 0

def test_small_dataset():
    """Test pipeline on small dataset"""
    print("\nTesting pipeline on small dataset...")
    
    cmd = [
        'python', 'microc_pipeline.py',
        '--sample_id', 'test-small',
        '--fastq_r1', 'tests/small-region-capture-micro-c/small_rcmc_r1.fq.gz',
        '--fastq_r2', 'tests/small-region-capture-micro-c/small_rcmc_r2.fq.gz',
        '--reference_bwa_idx', 'tests/small-region-capture-micro-c/test_bwa_index.tgz',
        '--chrom_sizes', 'tests/small-region-capture-micro-c/test.chrom.sizes',
        '--output_dir', 'test_output',
        '--resolution', '1000',
        '--bwa_cores', '2'
    ]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ Pipeline completed successfully")
            
            # Check output files
            expected_files = [
                'test_output/merge/test-small.mapped.pairs',
                'test_output/merge/test-small.bam',
                'test_output/cooler/test-small.raw.mcool',
                'test_output/cooler/test-small.balanced.mcool'
            ]
            
            all_files_exist = True
            for file_path in expected_files:
                if os.path.exists(file_path):
                    size_mb = os.path.getsize(file_path) / (1024*1024)
                    print(f"✅ {file_path} ({size_mb:.2f} MB)")
                else:
                    print(f"❌ {file_path} missing")
                    all_files_exist = False
            
            return all_files_exist
            
        else:
            print(f"❌ Pipeline failed with return code {result.returncode}")
            print("STDOUT:", result.stdout[-500:])  # Last 500 chars
            print("STDERR:", result.stderr[-500:])  # Last 500 chars
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Pipeline timed out (>5 minutes)")
        return False
    except Exception as e:
        print(f"❌ Error running pipeline: {e}")
        return False

def main():
    print("=== MICRO-C PIPELINE TEST SUITE ===\n")
    
    # Test 1: Environment
    env_ok = test_environment()
    
    if not env_ok:
        print("\n❌ Environment test failed. Please check missing dependencies.")
        sys.exit(1)
    
    # Test 2: Small dataset
    pipeline_ok = test_small_dataset()
    
    if not pipeline_ok:
        print("\n❌ Pipeline test failed.")
        sys.exit(1)
    
    print("\n🎉 All tests passed! The Micro-C pipeline is working correctly.")
    print("\nNext steps:")
    print("1. Run performance benchmarks: python benchmark_pipeline.py --small_test --large_test")
    print("2. Analyze results: python performance_analysis.py")
    print("3. Read the full analysis: README_PIPELINE_ANALYSIS.md")

if __name__ == "__main__":
    main()
