#!/usr/bin/env python3
"""
Performance benchmarking script for the Micro-C pipeline.
Profiles execution time for each major step and identifies bottlenecks.
"""

import os
import time
import subprocess
import argparse
import json
import psutil
from datetime import datetime
import pandas as pd

class PipelineBenchmark:
    def __init__(self, output_dir="benchmark_results"):
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
        self.results = []
        
    def run_benchmark(self, sample_id, fastq_r1, fastq_r2, reference_bwa_idx, 
                     chrom_sizes, resolution=1000, bwa_cores=2):
        """Run pipeline with timing and resource monitoring"""
        
        print(f"Starting benchmark for {sample_id}")
        print(f"Input files: {len(fastq_r1)} R1 files, {len(fastq_r2)} R2 files")
        
        # Get input file sizes
        total_input_size = 0
        for f in fastq_r1 + fastq_r2:
            if os.path.exists(f):
                total_input_size += os.path.getsize(f)
        
        total_input_gb = total_input_size / (1024**3)
        print(f"Total input size: {total_input_gb:.2f} GB")
        
        # Prepare command
        cmd = [
            "python", "microc_pipeline.py",
            "--sample_id", sample_id,
            "--fastq_r1"] + fastq_r1 + [
            "--fastq_r2"] + fastq_r2 + [
            "--reference_bwa_idx", reference_bwa_idx,
            "--chrom_sizes", chrom_sizes,
            "--output_dir", os.path.join(self.output_dir, f"{sample_id}_output"),
            "--resolution", str(resolution),
            "--bwa_cores", str(bwa_cores)
        ]
        
        # Monitor system resources
        process = psutil.Process()
        initial_memory = process.memory_info().rss / (1024**3)  # GB
        
        # Run pipeline with timing
        start_time = time.time()
        start_datetime = datetime.now()
        
        print(f"Running command: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        end_time = time.time()
        end_datetime = datetime.now()
        total_time = end_time - start_time
        
        # Get final memory usage
        final_memory = process.memory_info().rss / (1024**3)  # GB
        peak_memory = max(initial_memory, final_memory)
        
        # Parse timing information from log output
        timing_info = self.parse_timing_from_output(result.stdout)
        
        # Collect results
        benchmark_result = {
            "sample_id": sample_id,
            "start_time": start_datetime.isoformat(),
            "end_time": end_datetime.isoformat(),
            "total_time_seconds": total_time,
            "total_time_minutes": total_time / 60,
            "input_size_gb": total_input_gb,
            "throughput_gb_per_hour": total_input_gb / (total_time / 3600) if total_time > 0 else 0,
            "initial_memory_gb": initial_memory,
            "final_memory_gb": final_memory,
            "peak_memory_gb": peak_memory,
            "return_code": result.returncode,
            "success": result.returncode == 0,
            "num_cores": bwa_cores,
            "resolution": resolution,
            "timing_breakdown": timing_info
        }
        
        # Add stderr if there were errors
        if result.stderr:
            benchmark_result["stderr"] = result.stderr
            
        self.results.append(benchmark_result)
        
        # Save individual result
        result_file = os.path.join(self.output_dir, f"{sample_id}_benchmark.json")
        with open(result_file, 'w') as f:
            json.dump(benchmark_result, f, indent=2)
            
        print(f"Benchmark completed in {total_time:.2f} seconds ({total_time/60:.2f} minutes)")
        print(f"Throughput: {benchmark_result['throughput_gb_per_hour']:.2f} GB/hour")
        print(f"Peak memory: {peak_memory:.2f} GB")
        
        return benchmark_result
    
    def parse_timing_from_output(self, output):
        """Parse timing information from pipeline log output"""
        timing_info = {}
        lines = output.split('\n')
        
        for line in lines:
            if "Completed" in line and "in" in line and "seconds" in line:
                # Extract step name and timing
                parts = line.split(" - INFO - Completed ")
                if len(parts) > 1:
                    step_and_time = parts[1]
                    if " in " in step_and_time:
                        step_name = step_and_time.split(" in ")[0]
                        time_part = step_and_time.split(" in ")[1].split(" seconds")[0]
                        try:
                            timing_info[step_name] = float(time_part)
                        except ValueError:
                            pass
        
        return timing_info
    
    def save_summary(self):
        """Save summary of all benchmark results"""
        if not self.results:
            print("No benchmark results to save")
            return
            
        # Save detailed JSON
        summary_file = os.path.join(self.output_dir, "benchmark_summary.json")
        with open(summary_file, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        # Create CSV summary
        df_data = []
        for result in self.results:
            row = {
                "sample_id": result["sample_id"],
                "total_time_minutes": result["total_time_minutes"],
                "input_size_gb": result["input_size_gb"],
                "throughput_gb_per_hour": result["throughput_gb_per_hour"],
                "peak_memory_gb": result["peak_memory_gb"],
                "success": result["success"],
                "num_cores": result["num_cores"]
            }
            
            # Add timing breakdown
            for step, time_sec in result.get("timing_breakdown", {}).items():
                row[f"time_{step.replace(' ', '_').lower()}"] = time_sec
                
            df_data.append(row)
        
        df = pd.DataFrame(df_data)
        csv_file = os.path.join(self.output_dir, "benchmark_summary.csv")
        df.to_csv(csv_file, index=False)
        
        print(f"Benchmark summary saved to {summary_file} and {csv_file}")
        
        # Print summary statistics
        print("\n=== BENCHMARK SUMMARY ===")
        print(f"Total runs: {len(self.results)}")
        print(f"Successful runs: {sum(1 for r in self.results if r['success'])}")
        
        if df_data:
            print(f"Average total time: {df['total_time_minutes'].mean():.2f} minutes")
            print(f"Average throughput: {df['throughput_gb_per_hour'].mean():.2f} GB/hour")
            print(f"Average peak memory: {df['peak_memory_gb'].mean():.2f} GB")
            
            # Identify bottleneck steps
            timing_columns = [col for col in df.columns if col.startswith('time_')]
            if timing_columns:
                print("\n=== STEP TIMING ANALYSIS ===")
                for col in timing_columns:
                    step_name = col.replace('time_', '').replace('_', ' ').title()
                    avg_time = df[col].mean()
                    print(f"{step_name}: {avg_time:.2f} seconds average")

def main():
    parser = argparse.ArgumentParser(description="Benchmark Micro-C pipeline performance")
    parser.add_argument("--small_test", action="store_true", 
                       help="Run benchmark on small test data")
    parser.add_argument("--large_test", action="store_true",
                       help="Run benchmark on large test data")
    parser.add_argument("--cores", type=int, default=2,
                       help="Number of cores to use")
    parser.add_argument("--output_dir", default="benchmark_results",
                       help="Output directory for benchmark results")
    
    args = parser.parse_args()
    
    benchmark = PipelineBenchmark(args.output_dir)
    
    if args.small_test:
        print("Running benchmark on small test data...")
        benchmark.run_benchmark(
            sample_id="small-rcmc-benchmark",
            fastq_r1=["tests/small-region-capture-micro-c/small_rcmc_r1.fq.gz",
                     "tests/small-region-capture-micro-c/small_rcmc-extra-reads_r1.fq.gz"],
            fastq_r2=["tests/small-region-capture-micro-c/small_rcmc_r2.fq.gz",
                     "tests/small-region-capture-micro-c/small_rcmc-extra-reads_r2.fq.gz"],
            reference_bwa_idx="tests/small-region-capture-micro-c/test_bwa_index.tgz",
            chrom_sizes="tests/small-region-capture-micro-c/test.chrom.sizes",
            bwa_cores=args.cores
        )
    
    if args.large_test:
        print("Running benchmark on large test data...")
        benchmark.run_benchmark(
            sample_id="large-rcmc-benchmark",
            fastq_r1=["tests/large-test-data/large_rcmc_20x_r1.fq.gz"],
            fastq_r2=["tests/large-test-data/large_rcmc_20x_r2.fq.gz"],
            reference_bwa_idx="tests/large-test-data/test_bwa_index.tgz",
            chrom_sizes="tests/large-test-data/test.chrom.sizes",
            bwa_cores=args.cores
        )
    
    benchmark.save_summary()

if __name__ == "__main__":
    main()
