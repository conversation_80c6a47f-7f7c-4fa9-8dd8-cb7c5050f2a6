#!/usr/bin/env python3
"""
Configuration validation script for the Micro-C pipeline.
Validates input files, parameters, and system requirements.
"""

import os
import sys
import argparse
import subprocess
import gzip
import tarfile
from pathlib import Path

class ConfigValidator:
    def __init__(self):
        self.errors = []
        self.warnings = []
        self.info = []
        
    def add_error(self, message):
        self.errors.append(f"❌ ERROR: {message}")
        
    def add_warning(self, message):
        self.warnings.append(f"⚠️  WARNING: {message}")
        
    def add_info(self, message):
        self.info.append(f"ℹ️  INFO: {message}")
    
    def validate_file_exists(self, filepath, description):
        """Validate that a file exists and is readable"""
        if not os.path.exists(filepath):
            self.add_error(f"{description} does not exist: {filepath}")
            return False
        
        if not os.path.isfile(filepath):
            self.add_error(f"{description} is not a file: {filepath}")
            return False
            
        if not os.access(filepath, os.R_OK):
            self.add_error(f"{description} is not readable: {filepath}")
            return False
            
        return True
    
    def validate_fastq_files(self, fastq_files, read_type):
        """Validate FASTQ files"""
        self.add_info(f"Validating {len(fastq_files)} {read_type} FASTQ files...")
        
        total_size = 0
        total_reads = 0
        
        for i, fastq_file in enumerate(fastq_files):
            if not self.validate_file_exists(fastq_file, f"{read_type} FASTQ file {i+1}"):
                continue
                
            # Check file size
            size_mb = os.path.getsize(fastq_file) / (1024**2)
            total_size += size_mb
            
            if size_mb < 1:
                self.add_warning(f"{fastq_file} is very small ({size_mb:.2f} MB)")
            
            # Try to read first few lines to validate format
            try:
                if fastq_file.endswith('.gz'):
                    opener = gzip.open
                else:
                    opener = open
                    
                with opener(fastq_file, 'rt') as f:
                    lines = [f.readline().strip() for _ in range(8)]
                    
                # Basic FASTQ format validation
                if not lines[0].startswith('@'):
                    self.add_error(f"{fastq_file} does not appear to be a valid FASTQ file (first line should start with @)")
                    continue
                    
                if not lines[2].startswith('+'):
                    self.add_error(f"{fastq_file} does not appear to be a valid FASTQ file (third line should start with +)")
                    continue
                
                # Count approximate reads (very rough estimate)
                if lines[0] and lines[1]:
                    # Estimate based on file size and average line length
                    avg_line_length = len(lines[1]) + 20  # sequence + headers
                    estimated_reads = (size_mb * 1024 * 1024) / (avg_line_length * 4)
                    total_reads += estimated_reads
                    
            except Exception as e:
                self.add_error(f"Cannot read {fastq_file}: {e}")
        
        self.add_info(f"Total {read_type} data: {total_size:.1f} MB, ~{total_reads/1000:.0f}K reads estimated")
        return total_size, total_reads
    
    def validate_bwa_index(self, index_file):
        """Validate BWA index file"""
        if not self.validate_file_exists(index_file, "BWA index"):
            return False
            
        # Check if it's a tar.gz file
        if not (index_file.endswith('.tar.gz') or index_file.endswith('.tgz')):
            self.add_warning(f"BWA index file should be a .tar.gz or .tgz file: {index_file}")
        
        # Try to list contents
        try:
            with tarfile.open(index_file, 'r:gz') as tar:
                members = tar.getnames()
                
                # Look for BWA index files
                bwa_extensions = ['.amb', '.ann', '.bwt', '.pac', '.sa']
                found_extensions = set()
                
                for member in members:
                    for ext in bwa_extensions:
                        if member.endswith(ext):
                            found_extensions.add(ext)
                
                missing_extensions = set(bwa_extensions) - found_extensions
                if missing_extensions:
                    self.add_error(f"BWA index missing files with extensions: {missing_extensions}")
                else:
                    self.add_info(f"BWA index appears complete with {len(members)} files")
                    
        except Exception as e:
            self.add_error(f"Cannot read BWA index file {index_file}: {e}")
            return False
            
        return True
    
    def validate_chrom_sizes(self, chrom_sizes_file):
        """Validate chromosome sizes file"""
        if not self.validate_file_exists(chrom_sizes_file, "Chromosome sizes file"):
            return False
            
        try:
            with open(chrom_sizes_file, 'r') as f:
                lines = f.readlines()
                
            if len(lines) == 0:
                self.add_error("Chromosome sizes file is empty")
                return False
                
            total_genome_size = 0
            chrom_count = 0
            
            for i, line in enumerate(lines):
                line = line.strip()
                if not line:
                    continue
                    
                parts = line.split('\t')
                if len(parts) != 2:
                    self.add_error(f"Invalid format in chromosome sizes file, line {i+1}: {line}")
                    continue
                    
                chrom_name, size_str = parts
                try:
                    size = int(size_str)
                    total_genome_size += size
                    chrom_count += 1
                except ValueError:
                    self.add_error(f"Invalid chromosome size in line {i+1}: {size_str}")
            
            self.add_info(f"Chromosome sizes file: {chrom_count} chromosomes, {total_genome_size/1e6:.1f} Mb total")
            
        except Exception as e:
            self.add_error(f"Cannot read chromosome sizes file: {e}")
            return False
            
        return True
    
    def validate_system_requirements(self):
        """Validate system requirements and tool availability"""
        self.add_info("Checking system requirements...")
        
        # Check available tools
        required_tools = ['bwa', 'samtools', 'pairtools', 'cooler', 'java']
        
        for tool in required_tools:
            try:
                result = subprocess.run(['which', tool], capture_output=True, text=True)
                if result.returncode == 0:
                    # Get version if possible
                    version_cmd = {
                        'bwa': ['bwa'],
                        'samtools': ['samtools', '--version'],
                        'pairtools': ['pairtools', '--version'],
                        'cooler': ['cooler', '--version'],
                        'java': ['java', '-version']
                    }
                    
                    try:
                        version_result = subprocess.run(version_cmd[tool], 
                                                      capture_output=True, text=True, timeout=5)
                        version_info = version_result.stdout or version_result.stderr
                        version_line = version_info.split('\n')[0] if version_info else "unknown"
                        self.add_info(f"{tool}: {result.stdout.strip()} ({version_line})")
                    except:
                        self.add_info(f"{tool}: {result.stdout.strip()}")
                else:
                    self.add_error(f"Required tool not found: {tool}")
            except Exception as e:
                self.add_error(f"Error checking tool {tool}: {e}")
        
        # Check Juicer tools
        juicer_jar = "juicer_tools/juicer_tools.jar"
        if os.path.exists(juicer_jar):
            size_mb = os.path.getsize(juicer_jar) / (1024**2)
            self.add_info(f"Juicer tools: {juicer_jar} ({size_mb:.1f} MB)")
        else:
            self.add_warning(f"Juicer tools not found at {juicer_jar} - HiC generation will fail")
    
    def validate_output_directory(self, output_dir):
        """Validate output directory"""
        if os.path.exists(output_dir):
            if not os.path.isdir(output_dir):
                self.add_error(f"Output path exists but is not a directory: {output_dir}")
                return False
            
            if not os.access(output_dir, os.W_OK):
                self.add_error(f"Output directory is not writable: {output_dir}")
                return False
                
            # Check if directory is empty
            if os.listdir(output_dir):
                self.add_warning(f"Output directory is not empty: {output_dir}")
        else:
            # Try to create it
            try:
                os.makedirs(output_dir, exist_ok=True)
                self.add_info(f"Created output directory: {output_dir}")
            except Exception as e:
                self.add_error(f"Cannot create output directory {output_dir}: {e}")
                return False
        
        return True
    
    def validate_parameters(self, args):
        """Validate pipeline parameters"""
        # Check resolution
        if args.resolution <= 0:
            self.add_error(f"Resolution must be positive: {args.resolution}")
        elif args.resolution < 1000:
            self.add_warning(f"Very high resolution ({args.resolution}bp) may be slow and memory-intensive")
        
        # Check cores
        if args.bwa_cores <= 0:
            self.add_error(f"BWA cores must be positive: {args.bwa_cores}")
        elif args.bwa_cores > 32:
            self.add_warning(f"Very high core count ({args.bwa_cores}) may not improve performance")
        
        # Check FASTQ file counts match
        if len(args.fastq_r1) != len(args.fastq_r2):
            self.add_error(f"Number of R1 files ({len(args.fastq_r1)}) does not match R2 files ({len(args.fastq_r2)})")
    
    def print_summary(self):
        """Print validation summary"""
        print("\n" + "="*80)
        print("🔍 CONFIGURATION VALIDATION SUMMARY")
        print("="*80)
        
        if self.info:
            print("\nℹ️  INFORMATION:")
            for msg in self.info:
                print(f"   {msg}")
        
        if self.warnings:
            print("\n⚠️  WARNINGS:")
            for msg in self.warnings:
                print(f"   {msg}")
        
        if self.errors:
            print("\n❌ ERRORS:")
            for msg in self.errors:
                print(f"   {msg}")
        
        print("\n" + "="*80)
        
        if self.errors:
            print("❌ VALIDATION FAILED - Please fix the errors above before running the pipeline")
            return False
        elif self.warnings:
            print("⚠️  VALIDATION PASSED WITH WARNINGS - Pipeline should work but check warnings")
            return True
        else:
            print("✅ VALIDATION PASSED - Configuration looks good!")
            return True

def main():
    parser = argparse.ArgumentParser(description="Validate Micro-C pipeline configuration")
    parser.add_argument("--sample_id", required=True, help="Sample identifier")
    parser.add_argument("--fastq_r1", nargs='+', required=True, help="R1 FASTQ files")
    parser.add_argument("--fastq_r2", nargs='+', required=True, help="R2 FASTQ files")
    parser.add_argument("--reference_bwa_idx", required=True, help="BWA index tar.gz file")
    parser.add_argument("--chrom_sizes", required=True, help="Chromosome sizes file")
    parser.add_argument("--output_dir", required=True, help="Output directory")
    parser.add_argument("--resolution", type=int, default=1000, help="Resolution in bp")
    parser.add_argument("--bwa_cores", type=int, default=2, help="Number of BWA cores")
    
    args = parser.parse_args()
    
    validator = ConfigValidator()
    
    print("🔍 Validating Micro-C pipeline configuration...")
    print(f"📋 Sample ID: {args.sample_id}")
    
    # Validate all components
    validator.validate_system_requirements()
    validator.validate_parameters(args)
    validator.validate_fastq_files(args.fastq_r1, "R1")
    validator.validate_fastq_files(args.fastq_r2, "R2")
    validator.validate_bwa_index(args.reference_bwa_idx)
    validator.validate_chrom_sizes(args.chrom_sizes)
    validator.validate_output_directory(args.output_dir)
    
    # Print summary and exit with appropriate code
    success = validator.print_summary()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
