Jun-09 09:51:00.060 [main] DEBUG nextflow.cli.Launcher - $> nextflow run nf/main.nf -profile test
Jun-09 09:51:00.125 [main] INFO  nextflow.cli.CmdRun - N E X T F L O W  ~  version 23.10.1
Jun-09 09:51:00.139 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/Users/<USER>/.nextflow/plugins; core-plugins: nf-amazon@2.1.4,nf-azure@1.3.3,nf-cloudcache@0.3.0,nf-codecommit@0.1.5,nf-console@1.0.6,nf-ga4gh@1.1.0,nf-google@1.8.3,nf-tower@1.6.3,nf-wave@1.0.1
Jun-09 09:51:00.145 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
Jun-09 09:51:00.146 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
Jun-09 09:51:00.148 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.4.1 in 'deployment' mode
Jun-09 09:51:00.155 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
Jun-09 09:51:00.165 [main] DEBUG nextflow.config.ConfigBuilder - Found config base: /Users/<USER>/projects/topology-tools/nf/nextflow.config
Jun-09 09:51:00.167 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /Users/<USER>/projects/topology-tools/nf/nextflow.config
Jun-09 09:51:00.181 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `test`
Jun-09 09:51:00.825 [main] DEBUG nextflow.config.ConfigBuilder - Available config profiles: [cluster, debug, test, local, docker, cloud, shifter, mamba, charliecloud, conda, singularity, arm, test_full, podman]
Jun-09 09:51:00.846 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declararion
Jun-09 09:51:00.860 [main] INFO  nextflow.cli.CmdRun - Launching `nf/main.nf` [boring_stone] DSL2 - revision: cb60860841
Jun-09 09:51:00.860 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
Jun-09 09:51:00.860 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[]
Jun-09 09:51:00.867 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /Users/<USER>/.nextflow/secrets/store.json
Jun-09 09:51:00.869 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@52ea0269] - activable => nextflow.secret.LocalSecretsProvider@52ea0269
Jun-09 09:51:00.908 [main] DEBUG nextflow.Session - Session UUID: 6e0f4b55-def6-4db4-bbd9-7375ba54bd48
Jun-09 09:51:00.908 [main] DEBUG nextflow.Session - Run name: boring_stone
Jun-09 09:51:00.909 [main] DEBUG nextflow.Session - Executor pool size: 10
Jun-09 09:51:00.915 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
Jun-09 09:51:00.918 [main] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'FileTransfer' minSize=10; maxSize=30; workQueue=LinkedBlockingQueue[10000]; allowCoreThreadTimeout=false
Jun-09 09:51:00.971 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 23.10.1 build 5891
  Created: 12-01-2024 22:01 UTC (17:01 EDT)
  System: Mac OS X 15.4.1
  Runtime: Groovy 3.0.19 on OpenJDK 64-Bit Server VM 11.0.26+4-LTS
  Encoding: UTF-8 (UTF-8)
  Process: <EMAIL> [127.0.0.1]
  CPUs: 10 - Mem: 64 GB (6.4 GB) - Swap: 7 GB (1.4 GB)
Jun-09 09:51:00.983 [main] DEBUG nextflow.Session - Work-dir: /Users/<USER>/projects/topology-tools/work [Mac OS X]
Jun-09 09:51:00.983 [main] DEBUG nextflow.Session - Script base path does not exist or is not a directory: /Users/<USER>/projects/topology-tools/nf/bin
Jun-09 09:51:00.990 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
Jun-09 09:51:00.996 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
Jun-09 09:51:01.022 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
Jun-09 09:51:01.029 [main] DEBUG nextflow.util.CustomThreadPool - Creating default thread pool > poolSize: 11; maxThreads: 1000
Jun-09 09:51:01.081 [main] DEBUG nextflow.Session - Session start
Jun-09 09:51:01.084 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow started -- trace file: /Users/<USER>/projects/topology-tools/test_results/pipeline_info/execution_trace_2025-06-09_09-51-00.txt
Jun-09 09:51:01.286 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
Jun-09 09:51:01.651 [main] INFO  nextflow.Nextflow - 
========================================================================================
                        Micro-C Pipeline - Nextflow DSL2
========================================================================================
Sample ID       : small-rcmc
FASTQ R1        : tests/small-region-capture-micro-c/small_rcmc_r1.fq.gz,tests/small-region-capture-micro-c/small_rcmc-extra-reads_r1.fq.gz
FASTQ R2        : tests/small-region-capture-micro-c/small_rcmc_r2.fq.gz,tests/small-region-capture-micro-c/small_rcmc-extra-reads_r2.fq.gz
BWA Index       : tests/small-region-capture-micro-c/test_bwa_index.tgz
Chrom Sizes     : tests/small-region-capture-micro-c/test.chrom.sizes
Output Dir      : ./test_results
Resolution      : 1000
BWA Cores       : 2
Min MAPQ        : 20
========================================================================================

Jun-09 09:51:01.745 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name EXTRACT_BWA_INDEX
Jun-09 09:51:01.746 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:EXTRACT_BWA_INDEX` matches process EXTRACT_BWA_INDEX
Jun-09 09:51:01.749 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 09:51:01.750 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 09:51:01.754 [main] DEBUG nextflow.executor.Executor - [warm up] executor > local
Jun-09 09:51:01.757 [main] DEBUG n.processor.LocalPollingMonitor - Creating local task monitor for executor 'local' > cpus=10; memory=64 GB; capacity=10; pollInterval=100ms; dumpInterval=5m
Jun-09 09:51:01.758 [main] DEBUG n.processor.TaskPollingMonitor - >>> barrier register (monitor: local)
Jun-09 09:51:01.798 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name BWA_ALIGN_PAIRTOOLS
Jun-09 09:51:01.799 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:BWA_ALIGN_PAIRTOOLS` matches process BWA_ALIGN_PAIRTOOLS
Jun-09 09:51:01.799 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 09:51:01.799 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 09:51:01.811 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name MERGE_DEDUP_SPLIT
Jun-09 09:51:01.811 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:MERGE_DEDUP_SPLIT` matches process MERGE_DEDUP_SPLIT
Jun-09 09:51:01.812 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 09:51:01.812 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 09:51:01.816 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name COOLER_PROCESS
Jun-09 09:51:01.817 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:COOLER_PROCESS` matches process COOLER_PROCESS
Jun-09 09:51:01.818 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 09:51:01.818 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 09:51:01.823 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name JUICER_HIC
Jun-09 09:51:01.823 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:JUICER_HIC` matches process JUICER_HIC
Jun-09 09:51:01.824 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 09:51:01.824 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 09:51:01.827 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name QC_METRICS
Jun-09 09:51:01.827 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:QC_METRICS` matches process QC_METRICS
Jun-09 09:51:01.828 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 09:51:01.828 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 09:51:01.830 [main] DEBUG nextflow.Session - Workflow process names [dsl2]: MERGE_DEDUP_SPLIT, COOLER_PROCESS, EXTRACT_BWA_INDEX, JUICER_HIC, BWA_ALIGN_PAIRTOOLS, QC_METRICS
Jun-09 09:51:01.830 [main] DEBUG nextflow.Session - Igniting dataflow network (9)
Jun-09 09:51:01.839 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > EXTRACT_BWA_INDEX
Jun-09 09:51:01.839 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > BWA_ALIGN_PAIRTOOLS
Jun-09 09:51:01.840 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > MERGE_DEDUP_SPLIT
Jun-09 09:51:01.840 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > COOLER_PROCESS
Jun-09 09:51:01.840 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > JUICER_HIC
Jun-09 09:51:01.840 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > QC_METRICS
Jun-09 09:51:01.841 [main] DEBUG nextflow.script.ScriptRunner - Parsed script files:
  Script_43f01a504ba15b5e: /Users/<USER>/projects/topology-tools/nf/./modules/merge_dedup_split.nf
  Script_6c968723c735dde5: /Users/<USER>/projects/topology-tools/nf/./modules/qc_metrics.nf
  Script_900e8f290a0d3a82: /Users/<USER>/projects/topology-tools/nf/./modules/cooler_process.nf
  Script_3c186acb8ea2eb2c: /Users/<USER>/projects/topology-tools/nf/./modules/juicer_hic.nf
  Script_59383c090845de85: /Users/<USER>/projects/topology-tools/nf/main.nf
  Script_f4e17a1e4235a98c: /Users/<USER>/projects/topology-tools/nf/./modules/extract_bwa_index.nf
  Script_e05012147db5f27a: /Users/<USER>/projects/topology-tools/nf/./modules/bwa_align_pairtools.nf
Jun-09 09:51:01.841 [main] DEBUG nextflow.script.ScriptRunner - > Awaiting termination 
Jun-09 09:51:01.841 [main] DEBUG nextflow.Session - Session await
Jun-09 09:51:01.941 [Task submitter] WARN  n.executor.BashWrapperBuilder - Task runtime metrics are not reported when using macOS without a container engine
Jun-09 09:51:01.952 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 09:51:01.959 [Task submitter] INFO  nextflow.Session - [7b/a43055] Submitted process > EXTRACT_BWA_INDEX (small_rcmc_r1.fq)
Jun-09 09:51:01.964 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 09:51:01.964 [Task submitter] INFO  nextflow.Session - [ca/3f9b4c] Submitted process > EXTRACT_BWA_INDEX (small_rcmc-extra-reads_r1.fq)
Jun-09 09:51:02.217 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 1; name: EXTRACT_BWA_INDEX (small_rcmc_r1.fq); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/7b/a4305588fcea081380597e6d454ff2]
Jun-09 09:51:02.246 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 2; name: EXTRACT_BWA_INDEX (small_rcmc-extra-reads_r1.fq); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/ca/3f9b4ce5d88912b3d7c326e41d50e6]
Jun-09 09:51:02.247 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 09:51:02.247 [Task submitter] INFO  nextflow.Session - [0f/632645] Submitted process > BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq)
Jun-09 09:51:02.255 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 09:51:02.256 [Task submitter] INFO  nextflow.Session - [24/ac7766] Submitted process > BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq)
Jun-09 09:51:05.356 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 4; name: BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/24/ac77667b92b110e9b92369c326de50]
Jun-09 09:51:11.688 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 3; name: BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/0f/6326458ae5e7a1a320b168aa9ddf1b]
Jun-09 09:51:11.700 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 09:51:11.701 [Task submitter] INFO  nextflow.Session - [7b/194835] Submitted process > MERGE_DEDUP_SPLIT (small-rcmc)
Jun-09 09:51:15.402 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 5; name: MERGE_DEDUP_SPLIT (small-rcmc); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/7b/19483588b9a9857001dc5f5e1b5804]
Jun-09 09:51:15.412 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 09:51:15.412 [Task submitter] INFO  nextflow.Session - [13/ce2bb6] Submitted process > QC_METRICS (small-rcmc)
Jun-09 09:51:15.416 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 09:51:15.416 [Task submitter] INFO  nextflow.Session - [1d/8c4497] Submitted process > COOLER_PROCESS (small-rcmc)
Jun-09 09:51:15.419 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 09:51:15.419 [Task submitter] INFO  nextflow.Session - [72/faab82] Submitted process > JUICER_HIC (small-rcmc)
Jun-09 09:51:15.513 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 8; name: QC_METRICS (small-rcmc); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/13/ce2bb64a7f64ec0b87d155e377b036]
Jun-09 09:51:21.476 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 7; name: COOLER_PROCESS (small-rcmc); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/1d/8c4497bd4b8d534335cc773b6fda9e]
Jun-09 09:51:23.718 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 6; name: JUICER_HIC (small-rcmc); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/72/faab82d2d2ec37c271994e1ece40f0]
Jun-09 09:51:23.719 [Task monitor] DEBUG nextflow.processor.TaskProcessor - Process `JUICER_HIC (small-rcmc)` is unable to find [UnixPath]: `/Users/<USER>/projects/topology-tools/work/72/faab82d2d2ec37c271994e1ece40f0/versions.yml` (pattern: `versions.yml`)
Jun-09 09:51:23.721 [Task monitor] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=JUICER_HIC (small-rcmc); work-dir=/Users/<USER>/projects/topology-tools/work/72/faab82d2d2ec37c271994e1ece40f0
  error [nextflow.exception.MissingFileException]: Missing output file(s) `versions.yml` expected by process `JUICER_HIC (small-rcmc)`
Jun-09 09:51:23.722 [Task monitor] INFO  nextflow.processor.TaskProcessor - [72/faab82] NOTE: Missing output file(s) `versions.yml` expected by process `JUICER_HIC (small-rcmc)` -- Error is ignored
Jun-09 09:51:23.723 [main] DEBUG nextflow.Session - Session await > all processes finished
Jun-09 09:51:23.724 [Task monitor] DEBUG n.processor.TaskPollingMonitor - <<< barrier arrives (monitor: local) - terminating tasks monitor poll loop
Jun-09 09:51:23.724 [main] DEBUG nextflow.Session - Session await > all barriers passed
Jun-09 09:51:23.725 [main] INFO  nextflow.Nextflow - 
========================================================================================
                        Pipeline Execution Summary
========================================================================================
Completed at    : 2025-06-09T09:51:23.724887-04:00
Duration        : 22.7s
Success         : true
Work directory  : /Users/<USER>/projects/topology-tools/work
Exit status     : 0
Error message   : None
========================================================================================

Jun-09 09:51:23.725 [main] INFO  nextflow.Nextflow - 🎉 Pipeline completed successfully!
Jun-09 09:51:23.725 [main] INFO  nextflow.Nextflow - 📁 Results are in: ./test_results
Jun-09 09:51:23.727 [main] DEBUG n.trace.WorkflowStatsObserver - Workflow completed > WorkflowStats[succeededCount=7; failedCount=1; ignoredCount=1; cachedCount=0; pendingCount=0; submittedCount=0; runningCount=0; retriesCount=0; abortedCount=0; succeedDuration=44.7s; failedDuration=16.5s; cachedDuration=0ms;loadCpus=0; loadMemory=0; peakRunning=3; peakCpus=6; peakMemory=16 GB; ]
Jun-09 09:51:23.727 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow completed -- saving trace file
Jun-09 09:51:23.728 [main] DEBUG nextflow.trace.ReportObserver - Workflow completed -- rendering execution report
Jun-09 09:51:24.631 [main] DEBUG nextflow.trace.TimelineObserver - Workflow completed -- rendering execution timeline
Jun-09 09:51:24.730 [main] WARN  nextflow.dag.GraphvizRenderer - Graphviz is required to render the execution DAG in the given format -- See http://www.graphviz.org for more info.
Jun-09 09:51:24.853 [main] DEBUG nextflow.cache.CacheDB - Closing CacheDB done
Jun-09 09:51:24.863 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'FileTransfer' shutdown completed (hard=false)
Jun-09 09:51:24.864 [main] DEBUG nextflow.script.ScriptRunner - > Execution complete -- Goodbye
