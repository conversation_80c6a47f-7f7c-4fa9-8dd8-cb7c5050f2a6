Jun-09 08:55:59.922 [main] DEBUG nextflow.cli.Launcher - $> nextflow run nf/main.nf -profile test,docker
Jun-09 08:55:59.979 [main] DEBUG nextflow.cli.CmdRun - N E X T F L O W  ~  version 25.04.3
Jun-09 08:55:59.999 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/Users/<USER>/.nextflow/plugins; core-plugins: nf-amazon@2.15.0,nf-azure@1.16.0,nf-cloudcache@0.4.3,nf-codecommit@0.2.3,nf-console@1.2.1,nf-google@1.21.0,nf-k8s@1.0.0,nf-tower@1.11.3,nf-wave@1.12.1
Jun-09 08:56:00.021 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
Jun-09 08:56:00.021 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
Jun-09 08:56:00.023 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.12.0 in 'deployment' mode
Jun-09 08:56:00.031 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
Jun-09 08:56:00.043 [main] DEBUG nextflow.config.ConfigBuilder - Found config base: /Users/<USER>/projects/topology-tools/nf/nextflow.config
Jun-09 08:56:00.045 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /Users/<USER>/projects/topology-tools/nf/nextflow.config
Jun-09 08:56:00.064 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /Users/<USER>/.nextflow/secrets/store.json
Jun-09 08:56:00.067 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@5d235104] - activable => nextflow.secret.LocalSecretsProvider@5d235104
Jun-09 08:56:00.070 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `test,docker`
Jun-09 08:56:00.643 [main] DEBUG nextflow.config.ConfigBuilder - Available config profiles: [cluster, debug, test, local, docker, cloud, shifter, mamba, charliecloud, conda, singularity, arm, test_full, podman]
Jun-09 08:56:00.662 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declaration
Jun-09 08:56:00.672 [main] DEBUG nextflow.cli.CmdRun - Launching `nf/main.nf` [evil_sax] DSL2 - revision: cb60860841
Jun-09 08:56:00.673 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
Jun-09 08:56:00.674 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[]
Jun-09 08:56:00.705 [main] DEBUG nextflow.Session - Session UUID: 6e74c77d-de70-4791-bd81-3d23d14d2cb8
Jun-09 08:56:00.705 [main] DEBUG nextflow.Session - Run name: evil_sax
Jun-09 08:56:00.705 [main] DEBUG nextflow.Session - Executor pool size: 10
Jun-09 08:56:00.710 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
Jun-09 08:56:00.713 [main] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'FileTransfer' minSize=10; maxSize=30; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jun-09 08:56:00.728 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 25.04.3 build 5949
  Created: 02-06-2025 20:56 UTC (16:56 EDT)
  System: Mac OS X 15.4.1
  Runtime: Groovy 4.0.26 on OpenJDK 64-Bit Server VM 23.0.2+7
  Encoding: UTF-8 (UTF-8)
  Process: <EMAIL> [127.0.0.1]
  CPUs: 10 - Mem: 64 GB (556.3 MB) - Swap: 7 GB (1.4 GB)
Jun-09 08:56:00.737 [main] DEBUG nextflow.Session - Work-dir: /Users/<USER>/projects/topology-tools/work [Mac OS X]
Jun-09 08:56:00.737 [main] DEBUG nextflow.Session - Script base path does not exist or is not a directory: /Users/<USER>/projects/topology-tools/nf/bin
Jun-09 08:56:00.743 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
Jun-09 08:56:00.749 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
Jun-09 08:56:00.764 [main] DEBUG nextflow.Session - Observer factory (v2): LinObserverFactory
Jun-09 08:56:00.787 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
Jun-09 08:56:00.792 [main] DEBUG nextflow.util.CustomThreadPool - Creating default thread pool > poolSize: 11; maxThreads: 1000
Jun-09 08:56:00.828 [main] DEBUG nextflow.Session - Session start
Jun-09 08:56:00.830 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow started -- trace file: /Users/<USER>/projects/topology-tools/test_results/pipeline_info/execution_trace_2025-06-09_08-56-00.txt
Jun-09 08:56:00.994 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
Jun-09 08:56:01.282 [main] INFO  nextflow.Nextflow - 
========================================================================================
                        Micro-C Pipeline - Nextflow DSL2
========================================================================================
Sample ID       : small-rcmc
FASTQ R1        : tests/small-region-capture-micro-c/small_rcmc_r1.fq.gz,tests/small-region-capture-micro-c/small_rcmc-extra-reads_r1.fq.gz
FASTQ R2        : tests/small-region-capture-micro-c/small_rcmc_r2.fq.gz,tests/small-region-capture-micro-c/small_rcmc-extra-reads_r2.fq.gz
BWA Index       : tests/small-region-capture-micro-c/test_bwa_index.tgz
Chrom Sizes     : tests/small-region-capture-micro-c/test.chrom.sizes
Output Dir      : ./test_results
Resolution      : 1000
BWA Cores       : 2
Min MAPQ        : 20
========================================================================================

Jun-09 08:56:01.368 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name EXTRACT_BWA_INDEX
Jun-09 08:56:01.369 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:EXTRACT_BWA_INDEX` matches process EXTRACT_BWA_INDEX
Jun-09 08:56:01.378 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:56:01.378 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:56:01.382 [main] DEBUG nextflow.executor.Executor - [warm up] executor > local
Jun-09 08:56:01.386 [main] DEBUG n.processor.LocalPollingMonitor - Creating local task monitor for executor 'local' > cpus=10; memory=64 GB; capacity=10; pollInterval=100ms; dumpInterval=5m
Jun-09 08:56:01.388 [main] DEBUG n.processor.TaskPollingMonitor - >>> barrier register (monitor: local)
Jun-09 08:56:01.401 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'EXTRACT_BWA_INDEX': maxForks=0; fair=false; array=0
Jun-09 08:56:01.422 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name BWA_ALIGN_PAIRTOOLS
Jun-09 08:56:01.422 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:BWA_ALIGN_PAIRTOOLS` matches process BWA_ALIGN_PAIRTOOLS
Jun-09 08:56:01.424 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:56:01.424 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:56:01.424 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'BWA_ALIGN_PAIRTOOLS': maxForks=0; fair=false; array=0
Jun-09 08:56:01.435 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name MERGE_DEDUP_SPLIT
Jun-09 08:56:01.436 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:MERGE_DEDUP_SPLIT` matches process MERGE_DEDUP_SPLIT
Jun-09 08:56:01.437 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:56:01.437 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:56:01.438 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'MERGE_DEDUP_SPLIT': maxForks=0; fair=false; array=0
Jun-09 08:56:01.442 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name COOLER_PROCESS
Jun-09 08:56:01.443 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:COOLER_PROCESS` matches process COOLER_PROCESS
Jun-09 08:56:01.444 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:56:01.444 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:56:01.444 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'COOLER_PROCESS': maxForks=0; fair=false; array=0
Jun-09 08:56:01.453 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name JUICER_HIC
Jun-09 08:56:01.453 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:JUICER_HIC` matches process JUICER_HIC
Jun-09 08:56:01.454 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:56:01.454 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:56:01.455 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'JUICER_HIC': maxForks=0; fair=false; array=0
Jun-09 08:56:01.460 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name QC_METRICS
Jun-09 08:56:01.461 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:QC_METRICS` matches process QC_METRICS
Jun-09 08:56:01.462 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:56:01.462 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:56:01.462 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'QC_METRICS': maxForks=0; fair=false; array=0
Jun-09 08:56:01.465 [main] DEBUG nextflow.Session - Workflow process names [dsl2]: MERGE_DEDUP_SPLIT, COOLER_PROCESS, EXTRACT_BWA_INDEX, JUICER_HIC, BWA_ALIGN_PAIRTOOLS, QC_METRICS
Jun-09 08:56:01.467 [main] DEBUG nextflow.Session - Igniting dataflow network (9)
Jun-09 08:56:01.471 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > EXTRACT_BWA_INDEX
Jun-09 08:56:01.471 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > BWA_ALIGN_PAIRTOOLS
Jun-09 08:56:01.477 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > MERGE_DEDUP_SPLIT
Jun-09 08:56:01.477 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > COOLER_PROCESS
Jun-09 08:56:01.477 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > JUICER_HIC
Jun-09 08:56:01.477 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > QC_METRICS
Jun-09 08:56:01.477 [main] DEBUG nextflow.script.ScriptRunner - Parsed script files:
  Script_43f01a504ba15b5e: /Users/<USER>/projects/topology-tools/nf/modules/merge_dedup_split.nf
  Script_6c968723c735dde5: /Users/<USER>/projects/topology-tools/nf/modules/qc_metrics.nf
  Script_900e8f290a0d3a82: /Users/<USER>/projects/topology-tools/nf/modules/cooler_process.nf
  Script_3c186acb8ea2eb2c: /Users/<USER>/projects/topology-tools/nf/modules/juicer_hic.nf
  Script_59383c090845de85: /Users/<USER>/projects/topology-tools/nf/main.nf
  Script_e05012147db5f27a: /Users/<USER>/projects/topology-tools/nf/modules/bwa_align_pairtools.nf
  Script_f4e17a1e4235a98c: /Users/<USER>/projects/topology-tools/nf/modules/extract_bwa_index.nf
Jun-09 08:56:01.477 [main] DEBUG nextflow.script.ScriptRunner - > Awaiting termination 
Jun-09 08:56:01.477 [main] DEBUG nextflow.Session - Session await
Jun-09 08:56:01.578 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:56:01.579 [Task submitter] INFO  nextflow.Session - [57/ad7cad] Submitted process > EXTRACT_BWA_INDEX (small_rcmc-extra-reads_r1.fq)
Jun-09 08:56:01.586 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:56:01.586 [Task submitter] INFO  nextflow.Session - [d5/c908b5] Submitted process > EXTRACT_BWA_INDEX (small_rcmc_r1.fq)
Jun-09 08:56:04.377 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 2; name: EXTRACT_BWA_INDEX (small_rcmc-extra-reads_r1.fq); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/57/ad7cada82d42eb1dcb13352b923d79]
Jun-09 08:56:04.378 [Task monitor] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'TaskFinalizer' minSize=10; maxSize=30; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jun-09 08:56:04.381 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 1; name: EXTRACT_BWA_INDEX (small_rcmc_r1.fq); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/d5/c908b545ae1d5de9bf44cbd0c18f3b]
Jun-09 08:56:04.409 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:56:04.410 [Task submitter] INFO  nextflow.Session - [3c/53f6db] Submitted process > BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq)
Jun-09 08:56:04.417 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:56:04.418 [Task submitter] INFO  nextflow.Session - [ee/ccd9e8] Submitted process > BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq)
Jun-09 08:56:04.803 [TaskFinalizer-1] DEBUG nextflow.processor.PublishDir - Failed to publish file: /Users/<USER>/projects/topology-tools/work/57/ad7cada82d42eb1dcb13352b923d79/genome_index; to: /Users/<USER>/projects/topology-tools/test_results/align/[:]/genome_index/genome_index [symlink] -- attempt: 1; reason: /Users/<USER>/projects/topology-tools/test_results/align/[:]/genome_index/genome_index
Jun-09 08:56:05.211 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 4; name: BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq); status: COMPLETED; exit: 125; error: -; workDir: /Users/<USER>/projects/topology-tools/work/ee/ccd9e85edec2c36dfb8806014066f5]
Jun-09 08:56:05.213 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 3; name: BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq); status: COMPLETED; exit: 125; error: -; workDir: /Users/<USER>/projects/topology-tools/work/3c/53f6db651d34663ac15ce0bebb5dc3]
Jun-09 08:56:05.214 [TaskFinalizer-3] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq); work-dir=/Users/<USER>/projects/topology-tools/work/ee/ccd9e85edec2c36dfb8806014066f5
  error [nextflow.exception.ProcessFailedException]: Process `BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq)` terminated with an error exit status (125)
Jun-09 08:56:05.219 [TaskFinalizer-3] INFO  nextflow.processor.TaskProcessor - [ee/ccd9e8] NOTE: Process `BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq)` terminated with an error exit status (125) -- Execution is retried (1)
Jun-09 08:56:05.219 [TaskFinalizer-4] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq); work-dir=/Users/<USER>/projects/topology-tools/work/3c/53f6db651d34663ac15ce0bebb5dc3
  error [nextflow.exception.ProcessFailedException]: Process `BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq)` terminated with an error exit status (125)
Jun-09 08:56:05.220 [TaskFinalizer-4] INFO  nextflow.processor.TaskProcessor - [3c/53f6db] NOTE: Process `BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq)` terminated with an error exit status (125) -- Execution is retried (1)
Jun-09 08:56:05.225 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:56:05.225 [Task submitter] INFO  nextflow.Session - [d8/29f7bc] Re-submitted process > BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq)
Jun-09 08:56:05.238 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:56:05.238 [Task submitter] INFO  nextflow.Session - [50/120406] Re-submitted process > BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq)
Jun-09 08:56:06.049 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 6; name: BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq); status: COMPLETED; exit: 125; error: -; workDir: /Users/<USER>/projects/topology-tools/work/50/120406443caf5a3eb310e8856b1de2]
Jun-09 08:56:06.050 [TaskFinalizer-5] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq); work-dir=/Users/<USER>/projects/topology-tools/work/50/120406443caf5a3eb310e8856b1de2
  error [nextflow.exception.ProcessFailedException]: Process `BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq)` terminated with an error exit status (125)
Jun-09 08:56:06.051 [TaskFinalizer-5] INFO  nextflow.processor.TaskProcessor - [50/120406] NOTE: Process `BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq)` terminated with an error exit status (125) -- Execution is retried (2)
Jun-09 08:56:06.052 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 5; name: BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq); status: COMPLETED; exit: 125; error: -; workDir: /Users/<USER>/projects/topology-tools/work/d8/29f7bc162c00f27284027418193099]
Jun-09 08:56:06.053 [TaskFinalizer-6] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq); work-dir=/Users/<USER>/projects/topology-tools/work/d8/29f7bc162c00f27284027418193099
  error [nextflow.exception.ProcessFailedException]: Process `BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq)` terminated with an error exit status (125)
Jun-09 08:56:06.053 [TaskFinalizer-6] INFO  nextflow.processor.TaskProcessor - [d8/29f7bc] NOTE: Process `BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq)` terminated with an error exit status (125) -- Execution is retried (2)
Jun-09 08:56:06.057 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:56:06.057 [Task submitter] INFO  nextflow.Session - [0f/77ff4d] Re-submitted process > BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq)
Jun-09 08:56:06.060 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:56:06.060 [Task submitter] INFO  nextflow.Session - [10/477956] Re-submitted process > BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq)
Jun-09 08:56:06.868 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 7; name: BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq); status: COMPLETED; exit: 125; error: -; workDir: /Users/<USER>/projects/topology-tools/work/0f/77ff4d2b1f2907c416f9ad2e344c34]
Jun-09 08:56:06.869 [TaskFinalizer-7] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq); work-dir=/Users/<USER>/projects/topology-tools/work/0f/77ff4d2b1f2907c416f9ad2e344c34
  error [nextflow.exception.ProcessFailedException]: Process `BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq)` terminated with an error exit status (125)
Jun-09 08:56:06.876 [TaskFinalizer-7] ERROR nextflow.processor.TaskProcessor - Error executing process > 'BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq)'

Caused by:
  Process `BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq)` terminated with an error exit status (125)


Command executed:

  # Find the reference genome file (follow symlinks with -L)
  bwt_file=$(find -L genome_index -name "*.bwt" | head -1)
  if [ -z "$bwt_file" ]; then
      echo "Error: No BWA index files found in genome_index"
      exit 1
  fi
  
  # Get the reference genome prefix (remove .bwt extension)
  genome_index_fa=${bwt_file%.bwt}
  
  echo "Using BWA index: $genome_index_fa"
  echo "Processing FASTQ files: small_rcmc_r1.fq.gz, small_rcmc_r2.fq.gz"
  echo "Output file: small_rcmc_r1.fq.pairsam.gz"
  
  # Run BWA alignment piped to pairtools
  bwa mem -5SP -T0 -t2 $genome_index_fa small_rcmc_r1.fq.gz small_rcmc_r2.fq.gz | \
  pairtools parse \
      --min-mapq 20 \
      --walks-policy 5unique \
      --max-inter-align-gap 30 \
      --add-columns pos5,pos3,dist_to_5,dist_to_3,read_len \
      --nproc-in 2 \
      --nproc-out 2 \
      --chroms-path test.chrom.sizes | \
  pairtools sort \
      --nproc 2 \
      -o small_rcmc_r1.fq.pairsam.gz
  
  # Verify output file was created
  if [ ! -f "small_rcmc_r1.fq.pairsam.gz" ]; then
      echo "Error: Output file small_rcmc_r1.fq.pairsam.gz was not created"
      exit 1
  fi
  
  echo "Successfully created small_rcmc_r1.fq.pairsam.gz"
  echo "File size: $(du -h small_rcmc_r1.fq.pairsam.gz)"
  
  # Create versions file
  cat <<-END_VERSIONS > versions.yml
  "BWA_ALIGN_PAIRTOOLS":
      bwa: $(echo $(bwa 2>&1) | sed 's/^.*Version: //; s/Contact:.*$//')
      pairtools: $(pairtools --version 2>&1 | sed 's/pairtools, version //')
      samtools: $(echo $(samtools --version 2>&1) | sed 's/^.*samtools //; s/Using.*$//')
  END_VERSIONS

Command exit status:
  125

Command output:
  (empty)

Command error:
  Unable to find image 'quay.io/biocontainers/mulled-v2-bwa-pairtools-samtools:latest' locally
  docker: Error response from daemon: unauthorized: access to the requested resource is not authorized.
  See 'docker run --help'.

Work dir:
  /Users/<USER>/projects/topology-tools/work/0f/77ff4d2b1f2907c416f9ad2e344c34

Container:
  quay.io/biocontainers/mulled-v2-bwa-pairtools-samtools:latest

Tip: you can try to figure out what's wrong by changing to the process work dir and showing the script file named `.command.sh`
Jun-09 08:56:06.878 [TaskFinalizer-7] DEBUG nextflow.Session - Session aborted -- Cause: Process `BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq)` terminated with an error exit status (125)
Jun-09 08:56:06.882 [Task monitor] DEBUG n.processor.TaskPollingMonitor - <<< barrier arrives (monitor: local) - terminating tasks monitor poll loop
Jun-09 08:56:06.885 [TaskFinalizer-7] DEBUG nextflow.Session - The following nodes are still active:
[process] MERGE_DEDUP_SPLIT
  status=ACTIVE
  port 0: (value) OPEN  ; channel: -
  port 1: (cntrl) -     ; channel: $

[process] COOLER_PROCESS
  status=ACTIVE
  port 0: (value) OPEN  ; channel: -
  port 1: (value) bound ; channel: chrom_sizes
  port 2: (cntrl) -     ; channel: $

[process] JUICER_HIC
  status=ACTIVE
  port 0: (value) OPEN  ; channel: -
  port 1: (value) bound ; channel: chrom_sizes
  port 2: (cntrl) -     ; channel: $

[process] QC_METRICS
  status=ACTIVE
  port 0: (value) OPEN  ; channel: -
  port 1: (cntrl) -     ; channel: $

Jun-09 08:56:06.888 [TaskFinalizer-7] ERROR nextflow.Nextflow - Pipeline execution stopped with the following message: Unable to find image 'quay.io/biocontainers/mulled-v2-bwa-pairtools-samtools:latest' locally
docker: Error response from daemon: unauthorized: access to the requested resource is not authorized.
See 'docker run --help'.
Jun-09 08:56:06.889 [main] DEBUG nextflow.Session - Session await > all processes finished
Jun-09 08:56:06.889 [main] DEBUG nextflow.Session - Session await > all barriers passed
Jun-09 08:56:06.890 [main] INFO  nextflow.Nextflow - 
    ========================================================================================
                            Pipeline Execution Summary
    ========================================================================================
    Completed at    : 2025-06-09T08:56:06.889556-04:00
    Duration        : 6.1s
    Success         : false
    Work directory  : /Users/<USER>/projects/topology-tools/work
    Exit status     : 125
    Error message   : Unable to find image 'quay.io/biocontainers/mulled-v2-bwa-pairtools-samtools:latest' locally
docker: Error response from daemon: unauthorized: access to the requested resource is not authorized.
See 'docker run --help'.
    ========================================================================================
    
Jun-09 08:56:06.891 [main] ERROR nextflow.Nextflow - 💥 Pipeline failed!
Jun-09 08:56:06.891 [main] ERROR nextflow.Nextflow - Check the error message above and workflow logs for details.
Jun-09 08:56:06.893 [main] WARN  n.processor.TaskPollingMonitor - Killing running tasks (1)
Jun-09 08:56:06.901 [main] DEBUG n.executor.local.LocalTaskHandler - Unable to kill BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq) -- command: kill -TERM 82219; exit: 1 
 bash: line 0: kill: (82219) - No such process

Jun-09 08:56:06.903 [main] DEBUG n.trace.WorkflowStatsObserver - Workflow completed > WorkflowStats[succeededCount=2; failedCount=5; ignoredCount=0; cachedCount=0; pendingCount=0; submittedCount=0; runningCount=0; retriesCount=4; abortedCount=1; succeedDuration=4s; failedDuration=7.3s; cachedDuration=0ms;loadCpus=0; loadMemory=0; peakRunning=3; peakCpus=6; peakMemory=16 GB; ]
Jun-09 08:56:06.903 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow completed -- saving trace file
Jun-09 08:56:06.904 [main] DEBUG nextflow.trace.ReportObserver - Workflow completed -- rendering execution report
Jun-09 08:56:07.775 [main] DEBUG nextflow.trace.TimelineObserver - Workflow completed -- rendering execution timeline
Jun-09 08:56:07.871 [main] WARN  nextflow.dag.GraphvizRenderer - Graphviz is required to render the execution DAG in the given format -- See http://www.graphviz.org for more info.
Jun-09 08:56:07.874 [main] DEBUG nextflow.cache.CacheDB - Closing CacheDB done
Jun-09 08:56:07.884 [main] DEBUG nextflow.script.ScriptRunner - > Execution complete -- Goodbye
