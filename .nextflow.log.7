Jun-09 08:05:37.259 [main] DEBUG nextflow.cli.Launcher - $> nextflow run nf/main.nf -profile test,docker
Jun-09 08:05:37.309 [main] DEBUG nextflow.cli.CmdRun - N E X T F L O W  ~  version 25.04.3
Jun-09 08:05:37.329 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/Users/<USER>/.nextflow/plugins; core-plugins: nf-amazon@2.15.0,nf-azure@1.16.0,nf-cloudcache@0.4.3,nf-codecommit@0.2.3,nf-console@1.2.1,nf-google@1.21.0,nf-k8s@1.0.0,nf-tower@1.11.3,nf-wave@1.12.1
Jun-09 08:05:37.350 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
Jun-09 08:05:37.351 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
Jun-09 08:05:37.352 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.12.0 in 'deployment' mode
Jun-09 08:05:37.358 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
Jun-09 08:05:37.370 [main] DEBUG nextflow.config.ConfigBuilder - Found config base: /Users/<USER>/projects/topology-tools/nf/nextflow.config
Jun-09 08:05:37.372 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /Users/<USER>/projects/topology-tools/nf/nextflow.config
Jun-09 08:05:37.391 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /Users/<USER>/.nextflow/secrets/store.json
Jun-09 08:05:37.393 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@73877e19] - activable => nextflow.secret.LocalSecretsProvider@73877e19
Jun-09 08:05:37.397 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `test,docker`
Jun-09 08:05:37.954 [main] DEBUG nextflow.config.ConfigBuilder - Available config profiles: [cluster, debug, test, local, docker, cloud, shifter, mamba, charliecloud, conda, singularity, arm, test_full, podman]
Jun-09 08:05:37.972 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declaration
Jun-09 08:05:37.982 [main] DEBUG nextflow.cli.CmdRun - Launching `nf/main.nf` [gloomy_stone] DSL2 - revision: 26f924d8d5
Jun-09 08:05:37.983 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
Jun-09 08:05:37.983 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[]
Jun-09 08:05:38.012 [main] DEBUG nextflow.Session - Session UUID: 19b21d9e-1746-43e0-9963-032254605de9
Jun-09 08:05:38.012 [main] DEBUG nextflow.Session - Run name: gloomy_stone
Jun-09 08:05:38.012 [main] DEBUG nextflow.Session - Executor pool size: 10
Jun-09 08:05:38.017 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
Jun-09 08:05:38.020 [main] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'FileTransfer' minSize=10; maxSize=30; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jun-09 08:05:38.035 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 25.04.3 build 5949
  Created: 02-06-2025 20:56 UTC (16:56 EDT)
  System: Mac OS X 15.4.1
  Runtime: Groovy 4.0.26 on OpenJDK 64-Bit Server VM 23.0.2+7
  Encoding: UTF-8 (UTF-8)
  Process: <EMAIL> [127.0.0.1]
  CPUs: 10 - Mem: 64 GB (1 GB) - Swap: 7 GB (1.4 GB)
Jun-09 08:05:38.043 [main] DEBUG nextflow.Session - Work-dir: /Users/<USER>/projects/topology-tools/work [Mac OS X]
Jun-09 08:05:38.044 [main] DEBUG nextflow.Session - Script base path does not exist or is not a directory: /Users/<USER>/projects/topology-tools/nf/bin
Jun-09 08:05:38.050 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
Jun-09 08:05:38.055 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
Jun-09 08:05:38.070 [main] DEBUG nextflow.Session - Observer factory (v2): LinObserverFactory
Jun-09 08:05:38.094 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
Jun-09 08:05:38.100 [main] DEBUG nextflow.util.CustomThreadPool - Creating default thread pool > poolSize: 11; maxThreads: 1000
Jun-09 08:05:38.135 [main] DEBUG nextflow.Session - Session start
Jun-09 08:05:38.137 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow started -- trace file: /Users/<USER>/projects/topology-tools/test_results/pipeline_info/execution_trace_2025-06-09_08-05-37.txt
Jun-09 08:05:38.276 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
Jun-09 08:05:38.574 [main] INFO  nextflow.Nextflow - 
========================================================================================
                        Micro-C Pipeline - Nextflow DSL2
========================================================================================
Sample ID       : test-sample
FASTQ R1        : ../tests/small-region-capture-micro-c/small_rcmc_r1.fq.gz
FASTQ R2        : ../tests/small-region-capture-micro-c/small_rcmc_r2.fq.gz
BWA Index       : ../tests/small-region-capture-micro-c/test_bwa_index.tgz
Chrom Sizes     : ../tests/small-region-capture-micro-c/test.chrom.sizes
Output Dir      : ./test_results
Resolution      : 1000
BWA Cores       : 2
Min MAPQ        : 20
========================================================================================

Jun-09 08:05:38.665 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name EXTRACT_BWA_INDEX
Jun-09 08:05:38.667 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:EXTRACT_BWA_INDEX` matches process EXTRACT_BWA_INDEX
Jun-09 08:05:38.677 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:05:38.678 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:05:38.683 [main] DEBUG nextflow.executor.Executor - [warm up] executor > local
Jun-09 08:05:38.688 [main] DEBUG n.processor.LocalPollingMonitor - Creating local task monitor for executor 'local' > cpus=10; memory=64 GB; capacity=10; pollInterval=100ms; dumpInterval=5m
Jun-09 08:05:38.690 [main] DEBUG n.processor.TaskPollingMonitor - >>> barrier register (monitor: local)
Jun-09 08:05:38.705 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'EXTRACT_BWA_INDEX': maxForks=0; fair=false; array=0
Jun-09 08:05:38.731 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name BWA_ALIGN_PAIRTOOLS
Jun-09 08:05:38.732 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:BWA_ALIGN_PAIRTOOLS` matches process BWA_ALIGN_PAIRTOOLS
Jun-09 08:05:38.733 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:05:38.733 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:05:38.734 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'BWA_ALIGN_PAIRTOOLS': maxForks=0; fair=false; array=0
Jun-09 08:05:38.742 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name MERGE_DEDUP_SPLIT
Jun-09 08:05:38.743 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:MERGE_DEDUP_SPLIT` matches process MERGE_DEDUP_SPLIT
Jun-09 08:05:38.744 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:05:38.744 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:05:38.745 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'MERGE_DEDUP_SPLIT': maxForks=0; fair=false; array=0
Jun-09 08:05:38.750 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name COOLER_PROCESS
Jun-09 08:05:38.750 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:COOLER_PROCESS` matches process COOLER_PROCESS
Jun-09 08:05:38.751 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:05:38.751 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:05:38.752 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'COOLER_PROCESS': maxForks=0; fair=false; array=0
Jun-09 08:05:38.758 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name JUICER_HIC
Jun-09 08:05:38.758 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:JUICER_HIC` matches process JUICER_HIC
Jun-09 08:05:38.759 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:05:38.760 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:05:38.760 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'JUICER_HIC': maxForks=0; fair=false; array=0
Jun-09 08:05:38.765 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name QC_METRICS
Jun-09 08:05:38.765 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:QC_METRICS` matches process QC_METRICS
Jun-09 08:05:38.766 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:05:38.766 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:05:38.767 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'QC_METRICS': maxForks=0; fair=false; array=0
Jun-09 08:05:38.769 [main] DEBUG nextflow.Session - Workflow process names [dsl2]: MERGE_DEDUP_SPLIT, COOLER_PROCESS, EXTRACT_BWA_INDEX, JUICER_HIC, BWA_ALIGN_PAIRTOOLS, QC_METRICS
Jun-09 08:05:38.771 [main] DEBUG nextflow.Session - Igniting dataflow network (10)
Jun-09 08:05:38.776 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > EXTRACT_BWA_INDEX
Jun-09 08:05:38.777 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > BWA_ALIGN_PAIRTOOLS
Jun-09 08:05:38.777 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > MERGE_DEDUP_SPLIT
Jun-09 08:05:38.777 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > COOLER_PROCESS
Jun-09 08:05:38.777 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > JUICER_HIC
Jun-09 08:05:38.777 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > QC_METRICS
Jun-09 08:05:38.777 [main] DEBUG nextflow.script.ScriptRunner - Parsed script files:
  Script_43f01a504ba15b5e: /Users/<USER>/projects/topology-tools/nf/modules/merge_dedup_split.nf
  Script_6c968723c735dde5: /Users/<USER>/projects/topology-tools/nf/modules/qc_metrics.nf
  Script_a457b5df1e6a90ec: /Users/<USER>/projects/topology-tools/nf/modules/bwa_align_pairtools.nf
  Script_900e8f290a0d3a82: /Users/<USER>/projects/topology-tools/nf/modules/cooler_process.nf
  Script_3c186acb8ea2eb2c: /Users/<USER>/projects/topology-tools/nf/modules/juicer_hic.nf
  Script_3cb8aa18b03099d1: /Users/<USER>/projects/topology-tools/nf/main.nf
  Script_50236dab2297e7d7: /Users/<USER>/projects/topology-tools/nf/modules/extract_bwa_index.nf
Jun-09 08:05:38.777 [main] DEBUG nextflow.script.ScriptRunner - > Awaiting termination 
Jun-09 08:05:38.777 [main] DEBUG nextflow.Session - Session await
Jun-09 08:05:38.826 [Actor Thread 5] DEBUG nextflow.util.HashBuilder - Unable to get file attributes file: /Users/<USER>/projects/tests/small-region-capture-micro-c/test_bwa_index.tgz -- Cause: java.nio.file.NoSuchFileException: /Users/<USER>/projects/tests/small-region-capture-micro-c/test_bwa_index.tgz
Jun-09 08:05:38.868 [Task submitter] WARN  nextflow.container.DockerBuilder - Undocumented setting `docker.userEmulation` is not supported any more - please remove it from your config
Jun-09 08:05:38.883 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:05:38.884 [Task submitter] INFO  nextflow.Session - [ed/99b75a] Submitted process > EXTRACT_BWA_INDEX (small_rcmc_r1.fq)
Jun-09 08:07:53.981 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 1; name: EXTRACT_BWA_INDEX (small_rcmc_r1.fq); status: COMPLETED; exit: 1; error: -; workDir: /Users/<USER>/projects/topology-tools/work/ed/99b75a45b46e369ae1f365fe7f9abe]
Jun-09 08:07:53.981 [Task monitor] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'TaskFinalizer' minSize=10; maxSize=30; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jun-09 08:07:53.987 [TaskFinalizer-1] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=EXTRACT_BWA_INDEX (small_rcmc_r1.fq); work-dir=/Users/<USER>/projects/topology-tools/work/ed/99b75a45b46e369ae1f365fe7f9abe
  error [nextflow.exception.ProcessFailedException]: Process `EXTRACT_BWA_INDEX (small_rcmc_r1.fq)` terminated with an error exit status (1)
Jun-09 08:07:53.994 [TaskFinalizer-1] INFO  nextflow.processor.TaskProcessor - [ed/99b75a] NOTE: Process `EXTRACT_BWA_INDEX (small_rcmc_r1.fq)` terminated with an error exit status (1) -- Execution is retried (1)
Jun-09 08:07:53.999 [Task submitter] WARN  nextflow.container.DockerBuilder - Undocumented setting `docker.userEmulation` is not supported any more - please remove it from your config
Jun-09 08:07:54.003 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:07:54.003 [Task submitter] INFO  nextflow.Session - [10/91ccce] Re-submitted process > EXTRACT_BWA_INDEX (small_rcmc_r1.fq)
Jun-09 08:07:54.721 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 2; name: EXTRACT_BWA_INDEX (small_rcmc_r1.fq); status: COMPLETED; exit: 1; error: -; workDir: /Users/<USER>/projects/topology-tools/work/10/91cccedb2b222ec2d39226bc7d6df4]
Jun-09 08:07:54.722 [TaskFinalizer-2] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=EXTRACT_BWA_INDEX (small_rcmc_r1.fq); work-dir=/Users/<USER>/projects/topology-tools/work/10/91cccedb2b222ec2d39226bc7d6df4
  error [nextflow.exception.ProcessFailedException]: Process `EXTRACT_BWA_INDEX (small_rcmc_r1.fq)` terminated with an error exit status (1)
Jun-09 08:07:54.722 [TaskFinalizer-2] INFO  nextflow.processor.TaskProcessor - [10/91ccce] NOTE: Process `EXTRACT_BWA_INDEX (small_rcmc_r1.fq)` terminated with an error exit status (1) -- Execution is retried (2)
Jun-09 08:07:54.728 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:07:54.728 [Task submitter] INFO  nextflow.Session - [b4/88f3b0] Re-submitted process > EXTRACT_BWA_INDEX (small_rcmc_r1.fq)
Jun-09 08:07:55.448 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 3; name: EXTRACT_BWA_INDEX (small_rcmc_r1.fq); status: COMPLETED; exit: 1; error: -; workDir: /Users/<USER>/projects/topology-tools/work/b4/88f3b040daae3082c80c7147316cb4]
Jun-09 08:07:55.449 [TaskFinalizer-3] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=EXTRACT_BWA_INDEX (small_rcmc_r1.fq); work-dir=/Users/<USER>/projects/topology-tools/work/b4/88f3b040daae3082c80c7147316cb4
  error [nextflow.exception.ProcessFailedException]: Process `EXTRACT_BWA_INDEX (small_rcmc_r1.fq)` terminated with an error exit status (1)
Jun-09 08:07:55.457 [TaskFinalizer-3] ERROR nextflow.processor.TaskProcessor - Error executing process > 'EXTRACT_BWA_INDEX (small_rcmc_r1.fq)'

Caused by:
  Process `EXTRACT_BWA_INDEX (small_rcmc_r1.fq)` terminated with an error exit status (1)


Command executed:

  # Create genome index directory
  mkdir -p genome_index
  
  # Extract BWA index
  tar zxvf test_bwa_index.tgz -C genome_index
  
  # Find the reference FASTA file (should have .bwt extension companion)
  bwt_file=$(find genome_index -name "*.bwt" | head -1)
  if [ -z "$bwt_file" ]; then
      echo "Error: No BWA index files found in test_bwa_index.tgz"
      exit 1
  fi
  
  # Log the extracted files
  echo "Extracted BWA index files:"
  ls -la genome_index/
  
  # Create versions file
  cat <<-END_VERSIONS > versions.yml
  "EXTRACT_BWA_INDEX":
      bwa: $(echo $(bwa 2>&1) | sed 's/^.*Version: //; s/Contact:.*$//')
      tar: $(tar --version | head -1 | sed 's/tar (GNU tar) //')
  END_VERSIONS

Command exit status:
  1

Command output:
  (empty)

Command error:
  WARNING: The requested image's platform (linux/amd64) does not match the detected host platform (linux/arm64/v8) and no specific platform was requested
  tar: invalid option -- 'z'
  BusyBox v1.22.1 (2014-05-23 01:24:27 UTC) multi-call binary.
  
  Usage: tar -[cxthvO] [-X FILE] [-T FILE] [-f TARFILE] [-C DIR] [FILE]...
  
  Create, extract, or list files from a tar file
  
  Operation:
  	c	Create
  	x	Extract
  	t	List
  	f	Name of TARFILE ('-' for stdin/out)
  	C	Change to DIR before operation
  	v	Verbose
  	O	Extract to stdout
  	h	Follow symlinks
  	exclude	File to exclude
  	X	File with names to exclude
  	T	File with names to include

Work dir:
  /Users/<USER>/projects/topology-tools/work/b4/88f3b040daae3082c80c7147316cb4

Container:
  quay.io/biocontainers/bwa:0.7.17--hed695b0_7

Tip: you can replicate the issue by changing to the process work dir and entering the command `bash .command.run`
Jun-09 08:07:55.460 [TaskFinalizer-3] DEBUG nextflow.Session - Session aborted -- Cause: Process `EXTRACT_BWA_INDEX (small_rcmc_r1.fq)` terminated with an error exit status (1)
Jun-09 08:07:55.462 [main] DEBUG nextflow.Session - Session await > all processes finished
Jun-09 08:07:55.471 [TaskFinalizer-3] ERROR nextflow.Nextflow - Pipeline execution stopped with the following message: WARNING: The requested image's platform (linux/amd64) does not match the detected host platform (linux/arm64/v8) and no specific platform was requested
tar: invalid option -- 'z'
BusyBox v1.22.1 (2014-05-23 01:24:27 UTC) multi-call binary.

Usage: tar -[cxthvO] [-X FILE] [-T FILE] [-f TARFILE] [-C DIR] [FILE]...

Create, extract, or list files from a tar file

Operation:
	c	Create
	x	Extract
	t	List
	f	Name of TARFILE ('-' for stdin/out)
	C	Change to DIR before operation
	v	Verbose
	O	Extract to stdout
	h	Follow symlinks
	exclude	File to exclude
	X	File with names to exclude
	T	File with names to include
Jun-09 08:07:55.473 [main] DEBUG nextflow.Session - Session await > all barriers passed
Jun-09 08:07:55.473 [Task monitor] DEBUG n.processor.TaskPollingMonitor - <<< barrier arrives (monitor: local) - terminating tasks monitor poll loop
Jun-09 08:07:55.475 [main] INFO  nextflow.Nextflow - 
    ========================================================================================
                            Pipeline Execution Summary
    ========================================================================================
    Completed at    : 2025-06-09T08:07:55.473916-04:00
    Duration        : 2m 17s
    Success         : false
    Work directory  : /Users/<USER>/projects/topology-tools/work
    Exit status     : 1
    Error message   : WARNING: The requested image's platform (linux/amd64) does not match the detected host platform (linux/arm64/v8) and no specific platform was requested
tar: invalid option -- 'z'
BusyBox v1.22.1 (2014-05-23 01:24:27 UTC) multi-call binary.

Usage: tar -[cxthvO] [-X FILE] [-T FILE] [-f TARFILE] [-C DIR] [FILE]...

Create, extract, or list files from a tar file

Operation:
	c	Create
	x	Extract
	t	List
	f	Name of TARFILE ('-' for stdin/out)
	C	Change to DIR before operation
	v	Verbose
	O	Extract to stdout
	h	Follow symlinks
	exclude	File to exclude
	X	File with names to exclude
	T	File with names to include
    ========================================================================================
    
Jun-09 08:07:55.475 [main] ERROR nextflow.Nextflow - 💥 Pipeline failed!
Jun-09 08:07:55.475 [main] ERROR nextflow.Nextflow - Check the error message above and workflow logs for details.
Jun-09 08:07:55.478 [main] DEBUG n.trace.WorkflowStatsObserver - Workflow completed > WorkflowStats[succeededCount=0; failedCount=3; ignoredCount=0; cachedCount=0; pendingCount=0; submittedCount=0; runningCount=0; retriesCount=2; abortedCount=0; succeedDuration=0ms; failedDuration=4m 33s; cachedDuration=0ms;loadCpus=0; loadMemory=0; peakRunning=1; peakCpus=2; peakMemory=6 GB; ]
Jun-09 08:07:55.478 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow completed -- saving trace file
Jun-09 08:07:55.480 [main] DEBUG nextflow.trace.ReportObserver - Workflow completed -- rendering execution report
Jun-09 08:07:56.396 [main] DEBUG nextflow.trace.TimelineObserver - Workflow completed -- rendering execution timeline
Jun-09 08:07:56.498 [main] WARN  nextflow.dag.GraphvizRenderer - Graphviz is required to render the execution DAG in the given format -- See http://www.graphviz.org for more info.
Jun-09 08:07:56.502 [main] DEBUG nextflow.cache.CacheDB - Closing CacheDB done
Jun-09 08:07:56.512 [main] DEBUG nextflow.script.ScriptRunner - > Execution complete -- Goodbye
