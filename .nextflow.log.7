Jun-09 08:52:55.942 [main] DEBUG nextflow.cli.Launcher - $> nextflow run nf/main.nf -profile test
Jun-09 08:52:56.003 [main] DEBUG nextflow.cli.CmdRun - N E X T F L O W  ~  version 25.04.3
Jun-09 08:52:56.024 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/Users/<USER>/.nextflow/plugins; core-plugins: nf-amazon@2.15.0,nf-azure@1.16.0,nf-cloudcache@0.4.3,nf-codecommit@0.2.3,nf-console@1.2.1,nf-google@1.21.0,nf-k8s@1.0.0,nf-tower@1.11.3,nf-wave@1.12.1
Jun-09 08:52:56.046 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
Jun-09 08:52:56.047 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
Jun-09 08:52:56.049 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.12.0 in 'deployment' mode
Jun-09 08:52:56.057 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
Jun-09 08:52:56.070 [main] DEBUG nextflow.config.ConfigBuilder - Found config base: /Users/<USER>/projects/topology-tools/nf/nextflow.config
Jun-09 08:52:56.072 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /Users/<USER>/projects/topology-tools/nf/nextflow.config
Jun-09 08:52:56.094 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /Users/<USER>/.nextflow/secrets/store.json
Jun-09 08:52:56.097 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@73877e19] - activable => nextflow.secret.LocalSecretsProvider@73877e19
Jun-09 08:52:56.101 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `test`
Jun-09 08:52:56.712 [main] DEBUG nextflow.config.ConfigBuilder - Available config profiles: [cluster, debug, test, local, docker, cloud, shifter, mamba, charliecloud, conda, singularity, arm, test_full, podman]
Jun-09 08:52:56.732 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declaration
Jun-09 08:52:56.742 [main] DEBUG nextflow.cli.CmdRun - Launching `nf/main.nf` [stupefied_elion] DSL2 - revision: cb60860841
Jun-09 08:52:56.743 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
Jun-09 08:52:56.743 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[]
Jun-09 08:52:56.786 [main] DEBUG nextflow.Session - Session UUID: 1471bb2b-3261-4328-aed8-a7d6db9291ca
Jun-09 08:52:56.786 [main] DEBUG nextflow.Session - Run name: stupefied_elion
Jun-09 08:52:56.786 [main] DEBUG nextflow.Session - Executor pool size: 10
Jun-09 08:52:56.790 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
Jun-09 08:52:56.792 [main] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'FileTransfer' minSize=10; maxSize=30; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jun-09 08:52:56.805 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 25.04.3 build 5949
  Created: 02-06-2025 20:56 UTC (16:56 EDT)
  System: Mac OS X 15.4.1
  Runtime: Groovy 4.0.26 on OpenJDK 64-Bit Server VM 23.0.2+7
  Encoding: UTF-8 (UTF-8)
  Process: <EMAIL> [127.0.0.1]
  CPUs: 10 - Mem: 64 GB (1.3 GB) - Swap: 7 GB (1.4 GB)
Jun-09 08:52:56.815 [main] DEBUG nextflow.Session - Work-dir: /Users/<USER>/projects/topology-tools/work [Mac OS X]
Jun-09 08:52:56.815 [main] DEBUG nextflow.Session - Script base path does not exist or is not a directory: /Users/<USER>/projects/topology-tools/nf/bin
Jun-09 08:52:56.822 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
Jun-09 08:52:56.828 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
Jun-09 08:52:56.843 [main] DEBUG nextflow.Session - Observer factory (v2): LinObserverFactory
Jun-09 08:52:56.869 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
Jun-09 08:52:56.875 [main] DEBUG nextflow.util.CustomThreadPool - Creating default thread pool > poolSize: 11; maxThreads: 1000
Jun-09 08:52:56.911 [main] DEBUG nextflow.Session - Session start
Jun-09 08:52:56.914 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow started -- trace file: /Users/<USER>/projects/topology-tools/test_results/pipeline_info/execution_trace_2025-06-09_08-52-56.txt
Jun-09 08:52:57.081 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
Jun-09 08:52:57.392 [main] INFO  nextflow.Nextflow - 
========================================================================================
                        Micro-C Pipeline - Nextflow DSL2
========================================================================================
Sample ID       : small-rcmc
FASTQ R1        : tests/small-region-capture-micro-c/small_rcmc_r1.fq.gz,tests/small-region-capture-micro-c/small_rcmc-extra-reads_r1.fq.gz
FASTQ R2        : tests/small-region-capture-micro-c/small_rcmc_r2.fq.gz,tests/small-region-capture-micro-c/small_rcmc-extra-reads_r2.fq.gz
BWA Index       : tests/small-region-capture-micro-c/test_bwa_index.tgz
Chrom Sizes     : tests/small-region-capture-micro-c/test.chrom.sizes
Output Dir      : ./test_results
Resolution      : 1000
BWA Cores       : 2
Min MAPQ        : 20
========================================================================================

Jun-09 08:52:57.486 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name EXTRACT_BWA_INDEX
Jun-09 08:52:57.488 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:EXTRACT_BWA_INDEX` matches process EXTRACT_BWA_INDEX
Jun-09 08:52:57.497 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:52:57.498 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:52:57.502 [main] DEBUG nextflow.executor.Executor - [warm up] executor > local
Jun-09 08:52:57.506 [main] DEBUG n.processor.LocalPollingMonitor - Creating local task monitor for executor 'local' > cpus=10; memory=64 GB; capacity=10; pollInterval=100ms; dumpInterval=5m
Jun-09 08:52:57.508 [main] DEBUG n.processor.TaskPollingMonitor - >>> barrier register (monitor: local)
Jun-09 08:52:57.521 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'EXTRACT_BWA_INDEX': maxForks=0; fair=false; array=0
Jun-09 08:52:57.545 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name BWA_ALIGN_PAIRTOOLS
Jun-09 08:52:57.545 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:BWA_ALIGN_PAIRTOOLS` matches process BWA_ALIGN_PAIRTOOLS
Jun-09 08:52:57.547 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:52:57.547 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:52:57.547 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'BWA_ALIGN_PAIRTOOLS': maxForks=0; fair=false; array=0
Jun-09 08:52:57.560 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name MERGE_DEDUP_SPLIT
Jun-09 08:52:57.561 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:MERGE_DEDUP_SPLIT` matches process MERGE_DEDUP_SPLIT
Jun-09 08:52:57.562 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:52:57.562 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:52:57.563 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'MERGE_DEDUP_SPLIT': maxForks=0; fair=false; array=0
Jun-09 08:52:57.567 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name COOLER_PROCESS
Jun-09 08:52:57.568 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:COOLER_PROCESS` matches process COOLER_PROCESS
Jun-09 08:52:57.569 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:52:57.569 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:52:57.570 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'COOLER_PROCESS': maxForks=0; fair=false; array=0
Jun-09 08:52:57.576 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name JUICER_HIC
Jun-09 08:52:57.576 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:JUICER_HIC` matches process JUICER_HIC
Jun-09 08:52:57.578 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:52:57.578 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:52:57.578 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'JUICER_HIC': maxForks=0; fair=false; array=0
Jun-09 08:52:57.583 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name QC_METRICS
Jun-09 08:52:57.584 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:QC_METRICS` matches process QC_METRICS
Jun-09 08:52:57.585 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:52:57.585 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:52:57.586 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'QC_METRICS': maxForks=0; fair=false; array=0
Jun-09 08:52:57.589 [main] DEBUG nextflow.Session - Workflow process names [dsl2]: MERGE_DEDUP_SPLIT, COOLER_PROCESS, EXTRACT_BWA_INDEX, JUICER_HIC, BWA_ALIGN_PAIRTOOLS, QC_METRICS
Jun-09 08:52:57.590 [main] DEBUG nextflow.Session - Igniting dataflow network (9)
Jun-09 08:52:57.595 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > EXTRACT_BWA_INDEX
Jun-09 08:52:57.595 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > BWA_ALIGN_PAIRTOOLS
Jun-09 08:52:57.595 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > MERGE_DEDUP_SPLIT
Jun-09 08:52:57.595 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > COOLER_PROCESS
Jun-09 08:52:57.595 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > JUICER_HIC
Jun-09 08:52:57.595 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > QC_METRICS
Jun-09 08:52:57.596 [main] DEBUG nextflow.script.ScriptRunner - Parsed script files:
  Script_43f01a504ba15b5e: /Users/<USER>/projects/topology-tools/nf/modules/merge_dedup_split.nf
  Script_6c968723c735dde5: /Users/<USER>/projects/topology-tools/nf/modules/qc_metrics.nf
  Script_a457b5df1e6a90ec: /Users/<USER>/projects/topology-tools/nf/modules/bwa_align_pairtools.nf
  Script_3c186acb8ea2eb2c: /Users/<USER>/projects/topology-tools/nf/modules/juicer_hic.nf
  Script_900e8f290a0d3a82: /Users/<USER>/projects/topology-tools/nf/modules/cooler_process.nf
  Script_59383c090845de85: /Users/<USER>/projects/topology-tools/nf/main.nf
  Script_f4e17a1e4235a98c: /Users/<USER>/projects/topology-tools/nf/modules/extract_bwa_index.nf
Jun-09 08:52:57.596 [main] DEBUG nextflow.script.ScriptRunner - > Awaiting termination 
Jun-09 08:52:57.596 [main] DEBUG nextflow.Session - Session await
Jun-09 08:52:57.691 [Task submitter] WARN  n.executor.BashWrapperBuilder - Task runtime metrics are not reported when using macOS without a container engine
Jun-09 08:52:57.701 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:52:57.702 [Task submitter] INFO  nextflow.Session - [00/38116f] Submitted process > EXTRACT_BWA_INDEX (small_rcmc-extra-reads_r1.fq)
Jun-09 08:52:57.710 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:52:57.710 [Task submitter] INFO  nextflow.Session - [61/c9c7ea] Submitted process > EXTRACT_BWA_INDEX (small_rcmc_r1.fq)
Jun-09 08:52:57.796 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 2; name: EXTRACT_BWA_INDEX (small_rcmc-extra-reads_r1.fq); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/00/38116fe410c9b54d9e6779593e18b9]
Jun-09 08:52:57.797 [Task monitor] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'TaskFinalizer' minSize=10; maxSize=30; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jun-09 08:52:57.802 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 1; name: EXTRACT_BWA_INDEX (small_rcmc_r1.fq); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/61/c9c7eaf02214d6a96c31f7e81cb637]
Jun-09 08:52:57.828 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:52:57.828 [Task submitter] INFO  nextflow.Session - [31/98fc7e] Submitted process > BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq)
Jun-09 08:52:57.832 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:52:57.832 [Task submitter] INFO  nextflow.Session - [b8/83ad99] Submitted process > BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq)
Jun-09 08:52:57.896 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 4; name: BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq); status: COMPLETED; exit: 1; error: -; workDir: /Users/<USER>/projects/topology-tools/work/31/98fc7e05dd336bbc6e7f6275c87a2c]
Jun-09 08:52:57.897 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 3; name: BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq); status: COMPLETED; exit: 1; error: -; workDir: /Users/<USER>/projects/topology-tools/work/b8/83ad9919eac4eb2207fcfcbaa9434d]
Jun-09 08:52:57.898 [TaskFinalizer-3] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq); work-dir=/Users/<USER>/projects/topology-tools/work/31/98fc7e05dd336bbc6e7f6275c87a2c
  error [nextflow.exception.ProcessFailedException]: Process `BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq)` terminated with an error exit status (1)
Jun-09 08:52:57.904 [TaskFinalizer-3] INFO  nextflow.processor.TaskProcessor - [31/98fc7e] NOTE: Process `BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq)` terminated with an error exit status (1) -- Execution is retried (1)
Jun-09 08:52:57.904 [TaskFinalizer-4] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq); work-dir=/Users/<USER>/projects/topology-tools/work/b8/83ad9919eac4eb2207fcfcbaa9434d
  error [nextflow.exception.ProcessFailedException]: Process `BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq)` terminated with an error exit status (1)
Jun-09 08:52:57.905 [TaskFinalizer-4] INFO  nextflow.processor.TaskProcessor - [b8/83ad99] NOTE: Process `BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq)` terminated with an error exit status (1) -- Execution is retried (1)
Jun-09 08:52:57.909 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:52:57.910 [Task submitter] INFO  nextflow.Session - [17/fde454] Re-submitted process > BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq)
Jun-09 08:52:57.914 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:52:57.914 [Task submitter] INFO  nextflow.Session - [1f/70cdd7] Re-submitted process > BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq)
Jun-09 08:52:57.961 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 5; name: BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq); status: COMPLETED; exit: 1; error: -; workDir: /Users/<USER>/projects/topology-tools/work/17/fde454de1a44e19f0fe6adb6a5bb8c]
Jun-09 08:52:57.962 [TaskFinalizer-5] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq); work-dir=/Users/<USER>/projects/topology-tools/work/17/fde454de1a44e19f0fe6adb6a5bb8c
  error [nextflow.exception.ProcessFailedException]: Process `BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq)` terminated with an error exit status (1)
Jun-09 08:52:57.963 [TaskFinalizer-5] INFO  nextflow.processor.TaskProcessor - [17/fde454] NOTE: Process `BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq)` terminated with an error exit status (1) -- Execution is retried (2)
Jun-09 08:52:57.963 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 6; name: BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq); status: COMPLETED; exit: 1; error: -; workDir: /Users/<USER>/projects/topology-tools/work/1f/70cdd7215417cea97bc128d4c09de9]
Jun-09 08:52:57.964 [TaskFinalizer-6] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq); work-dir=/Users/<USER>/projects/topology-tools/work/1f/70cdd7215417cea97bc128d4c09de9
  error [nextflow.exception.ProcessFailedException]: Process `BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq)` terminated with an error exit status (1)
Jun-09 08:52:57.964 [TaskFinalizer-6] INFO  nextflow.processor.TaskProcessor - [1f/70cdd7] NOTE: Process `BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq)` terminated with an error exit status (1) -- Execution is retried (2)
Jun-09 08:52:57.968 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:52:57.968 [Task submitter] INFO  nextflow.Session - [04/f2bf76] Re-submitted process > BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq)
Jun-09 08:52:57.972 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:52:57.972 [Task submitter] INFO  nextflow.Session - [6c/8e2c59] Re-submitted process > BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq)
Jun-09 08:52:58.023 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 7; name: BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq); status: COMPLETED; exit: 1; error: -; workDir: /Users/<USER>/projects/topology-tools/work/04/f2bf767fb74a7cde22affa32574d3e]
Jun-09 08:52:58.024 [TaskFinalizer-7] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq); work-dir=/Users/<USER>/projects/topology-tools/work/04/f2bf767fb74a7cde22affa32574d3e
  error [nextflow.exception.ProcessFailedException]: Process `BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq)` terminated with an error exit status (1)
Jun-09 08:52:58.025 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 8; name: BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq); status: COMPLETED; exit: 1; error: -; workDir: /Users/<USER>/projects/topology-tools/work/6c/8e2c594a29918b95d2fd371e597c0c]
Jun-09 08:52:58.041 [TaskFinalizer-7] ERROR nextflow.processor.TaskProcessor - Error executing process > 'BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq)'

Caused by:
  Process `BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq)` terminated with an error exit status (1)


Command executed:

  # Find the reference genome file
  bwt_file=$(find genome_index -name "*.bwt" | head -1)
  if [ -z "$bwt_file" ]; then
      echo "Error: No BWA index files found in genome_index"
      exit 1
  fi
  
  # Get the reference genome prefix (remove .bwt extension)
  genome_index_fa=${bwt_file%.bwt}
  
  echo "Using BWA index: $genome_index_fa"
  echo "Processing FASTQ files: small_rcmc-extra-reads_r1.fq.gz, small_rcmc-extra-reads_r2.fq.gz"
  echo "Output file: small_rcmc-extra-reads_r1.fq.pairsam.gz"
  
  # Run BWA alignment piped to pairtools
  bwa mem -5SP -T0 -t2 $genome_index_fa small_rcmc-extra-reads_r1.fq.gz small_rcmc-extra-reads_r2.fq.gz | \
  pairtools parse \
      --min-mapq 20 \
      --walks-policy 5unique \
      --max-inter-align-gap 30 \
      --add-columns pos5,pos3,dist_to_5,dist_to_3,read_len \
      --nproc-in 2 \
      --nproc-out 2 \
      --chroms-path test.chrom.sizes | \
  pairtools sort \
      --nproc 2 \
      -o small_rcmc-extra-reads_r1.fq.pairsam.gz
  
  # Verify output file was created
  if [ ! -f "small_rcmc-extra-reads_r1.fq.pairsam.gz" ]; then
      echo "Error: Output file small_rcmc-extra-reads_r1.fq.pairsam.gz was not created"
      exit 1
  fi
  
  echo "Successfully created small_rcmc-extra-reads_r1.fq.pairsam.gz"
  echo "File size: $(du -h small_rcmc-extra-reads_r1.fq.pairsam.gz)"
  
  # Create versions file
  cat <<-END_VERSIONS > versions.yml
  "BWA_ALIGN_PAIRTOOLS":
      bwa: $(echo $(bwa 2>&1) | sed 's/^.*Version: //; s/Contact:.*$//')
      pairtools: $(pairtools --version 2>&1 | sed 's/pairtools, version //')
      samtools: $(echo $(samtools --version 2>&1) | sed 's/^.*samtools //; s/Using.*$//')
  END_VERSIONS

Command exit status:
  1

Command output:
  Error: No BWA index files found in genome_index

Command error:
  Error: No BWA index files found in genome_index

Work dir:
  /Users/<USER>/projects/topology-tools/work/04/f2bf767fb74a7cde22affa32574d3e

Tip: when you have fixed the problem you can continue the execution adding the option `-resume` to the run command line
Jun-09 08:52:58.042 [TaskFinalizer-8] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq); work-dir=/Users/<USER>/projects/topology-tools/work/6c/8e2c594a29918b95d2fd371e597c0c
  error [nextflow.exception.ProcessFailedException]: Process `BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq)` terminated with an error exit status (1)
Jun-09 08:52:58.044 [TaskFinalizer-7] DEBUG nextflow.Session - Session aborted -- Cause: Process `BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq)` terminated with an error exit status (1)
Jun-09 08:52:58.054 [TaskFinalizer-7] ERROR nextflow.Nextflow - Pipeline execution stopped with the following message: Error: No BWA index files found in genome_index
Jun-09 08:52:58.055 [main] DEBUG nextflow.Session - Session await > all processes finished
Jun-09 08:52:58.055 [main] DEBUG nextflow.Session - Session await > all barriers passed
Jun-09 08:52:58.055 [Task monitor] DEBUG n.processor.TaskPollingMonitor - <<< barrier arrives (monitor: local) - terminating tasks monitor poll loop
Jun-09 08:52:58.056 [TaskFinalizer-1] ERROR nextflow.processor.PublishDir - Failed to publish file: /Users/<USER>/projects/topology-tools/work/00/38116fe410c9b54d9e6779593e18b9/genome_index; to: /Users/<USER>/projects/topology-tools/test_results/align/[:]/genome_index/genome_index [symlink] -- See log file for details
dev.failsafe.FailsafeException: java.lang.InterruptedException: sleep interrupted
	at dev.failsafe.FailsafeExecutor.call(FailsafeExecutor.java:444)
	at dev.failsafe.FailsafeExecutor.get(FailsafeExecutor.java:129)
	at nextflow.processor.PublishDir.retryableProcessFile(PublishDir.groovy:416)
	at nextflow.processor.PublishDir.safeProcessFile(PublishDir.groovy:387)
	at nextflow.processor.PublishDir.apply1(PublishDir.groovy:359)
	at nextflow.processor.PublishDir.apply0(PublishDir.groovy:266)
	at nextflow.processor.PublishDir.apply(PublishDir.groovy:324)
	at org.codehaus.groovy.vmplugin.v8.IndyInterface.fromCache(IndyInterface.java:321)
	at nextflow.processor.TaskProcessor.publishOutputs0(TaskProcessor.groovy:1420)
	at nextflow.processor.TaskProcessor.publishOutputs(TaskProcessor.groovy:1395)
	at org.codehaus.groovy.vmplugin.v8.IndyInterface.fromCache(IndyInterface.java:321)
	at nextflow.processor.TaskProcessor.finalizeTask0(TaskProcessor.groovy:2437)
	at org.codehaus.groovy.vmplugin.v8.IndyInterface.fromCache(IndyInterface.java:321)
	at nextflow.processor.TaskProcessor.finalizeTask(TaskProcessor.groovy:2408)
	at nextflow.processor.TaskPollingMonitor.finalizeTask(TaskPollingMonitor.groovy:696)
	at nextflow.processor.TaskPollingMonitor.safeFinalizeTask(TaskPollingMonitor.groovy:686)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:343)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:328)
	at groovy.lang.MetaClassImpl.doInvokeMethod(MetaClassImpl.java:1333)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1088)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1007)
	at org.codehaus.groovy.runtime.InvokerHelper.invokePogoMethod(InvokerHelper.java:645)
	at org.codehaus.groovy.runtime.InvokerHelper.invokeMethod(InvokerHelper.java:628)
	at org.codehaus.groovy.runtime.InvokerHelper.invokeMethodSafe(InvokerHelper.java:82)
	at nextflow.processor.TaskPollingMonitor$_checkTaskStatus_lambda8.doCall(TaskPollingMonitor.groovy:676)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1575)
Caused by: java.lang.InterruptedException: sleep interrupted
	at java.base/java.lang.Thread.sleepNanos0(Native Method)
	at java.base/java.lang.Thread.sleepNanos(Thread.java:496)
	at java.base/java.lang.Thread.sleep(Thread.java:527)
	at dev.failsafe.internal.RetryPolicyExecutor.lambda$apply$0(RetryPolicyExecutor.java:90)
	at dev.failsafe.SyncExecutionImpl.executeSync(SyncExecutionImpl.java:176)
	at dev.failsafe.FailsafeExecutor.call(FailsafeExecutor.java:437)
	... 31 common frames omitted
Jun-09 08:52:58.058 [main] INFO  nextflow.Nextflow - 
========================================================================================
                        Pipeline Execution Summary
========================================================================================
Completed at    : 2025-06-09T08:52:58.055843-04:00
Duration        : 1.2s
Success         : false
Work directory  : /Users/<USER>/projects/topology-tools/work
Exit status     : 1
Error message   : Error: No BWA index files found in genome_index
========================================================================================

Jun-09 08:52:58.058 [main] ERROR nextflow.Nextflow - 💥 Pipeline failed!
Jun-09 08:52:58.058 [main] ERROR nextflow.Nextflow - Check the error message above and workflow logs for details.
Jun-09 08:52:58.061 [main] DEBUG n.trace.WorkflowStatsObserver - Workflow completed > WorkflowStats[succeededCount=2; failedCount=6; ignoredCount=0; cachedCount=0; pendingCount=0; submittedCount=0; runningCount=0; retriesCount=4; abortedCount=0; succeedDuration=232ms; failedDuration=32ms; cachedDuration=0ms;loadCpus=0; loadMemory=0; peakRunning=3; peakCpus=6; peakMemory=16 GB; ]
Jun-09 08:52:58.062 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow completed -- saving trace file
Jun-09 08:52:58.063 [main] DEBUG nextflow.trace.ReportObserver - Workflow completed -- rendering execution report
Jun-09 08:52:58.977 [main] DEBUG nextflow.trace.TimelineObserver - Workflow completed -- rendering execution timeline
Jun-09 08:52:59.081 [main] WARN  nextflow.dag.GraphvizRenderer - Graphviz is required to render the execution DAG in the given format -- See http://www.graphviz.org for more info.
Jun-09 08:52:59.086 [main] DEBUG nextflow.cache.CacheDB - Closing CacheDB done
Jun-09 08:52:59.097 [main] DEBUG nextflow.script.ScriptRunner - > Execution complete -- Goodbye
