Jun-09 08:10:52.093 [main] DEBUG nextflow.cli.Launcher - $> nextflow run nf/main.nf -profile test -stub-run
Jun-09 08:10:52.155 [main] DEBUG nextflow.cli.CmdRun - N E X T F L O W  ~  version 25.04.3
Jun-09 08:10:52.186 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/Users/<USER>/.nextflow/plugins; core-plugins: nf-amazon@2.15.0,nf-azure@1.16.0,nf-cloudcache@0.4.3,nf-codecommit@0.2.3,nf-console@1.2.1,nf-google@1.21.0,nf-k8s@1.0.0,nf-tower@1.11.3,nf-wave@1.12.1
Jun-09 08:10:52.219 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
Jun-09 08:10:52.220 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
Jun-09 08:10:52.223 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.12.0 in 'deployment' mode
Jun-09 08:10:52.233 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
Jun-09 08:10:52.253 [main] DEBUG nextflow.config.ConfigBuilder - Found config base: /Users/<USER>/projects/topology-tools/nf/nextflow.config
Jun-09 08:10:52.256 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /Users/<USER>/projects/topology-tools/nf/nextflow.config
Jun-09 08:10:52.290 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /Users/<USER>/.nextflow/secrets/store.json
Jun-09 08:10:52.293 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@7cedfa63] - activable => nextflow.secret.LocalSecretsProvider@7cedfa63
Jun-09 08:10:52.297 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `test`
Jun-09 08:10:53.082 [main] DEBUG nextflow.config.ConfigBuilder - Available config profiles: [cluster, debug, test, local, docker, cloud, shifter, mamba, charliecloud, conda, singularity, arm, test_full, podman]
Jun-09 08:10:53.104 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declaration
Jun-09 08:10:53.117 [main] DEBUG nextflow.cli.CmdRun - Launching `nf/main.nf` [gigantic_hilbert] DSL2 - revision: 26f924d8d5
Jun-09 08:10:53.118 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
Jun-09 08:10:53.118 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[]
Jun-09 08:10:53.156 [main] DEBUG nextflow.Session - Session UUID: d2b97f08-f4c9-44fe-a2b5-fb0d93326e9f
Jun-09 08:10:53.156 [main] DEBUG nextflow.Session - Run name: gigantic_hilbert
Jun-09 08:10:53.156 [main] DEBUG nextflow.Session - Executor pool size: 10
Jun-09 08:10:53.161 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
Jun-09 08:10:53.167 [main] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'FileTransfer' minSize=10; maxSize=30; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jun-09 08:10:53.186 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 25.04.3 build 5949
  Created: 02-06-2025 20:56 UTC (16:56 EDT)
  System: Mac OS X 15.4.1
  Runtime: Groovy 4.0.26 on OpenJDK 64-Bit Server VM 23.0.2+7
  Encoding: UTF-8 (UTF-8)
  Process: <EMAIL> [127.0.0.1]
  CPUs: 10 - Mem: 64 GB (1.9 GB) - Swap: 7 GB (1.4 GB)
Jun-09 08:10:53.195 [main] DEBUG nextflow.Session - Work-dir: /Users/<USER>/projects/topology-tools/work [Mac OS X]
Jun-09 08:10:53.196 [main] DEBUG nextflow.Session - Script base path does not exist or is not a directory: /Users/<USER>/projects/topology-tools/nf/bin
Jun-09 08:10:53.205 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
Jun-09 08:10:53.211 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
Jun-09 08:10:53.231 [main] DEBUG nextflow.Session - Observer factory (v2): LinObserverFactory
Jun-09 08:10:53.261 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
Jun-09 08:10:53.269 [main] DEBUG nextflow.util.CustomThreadPool - Creating default thread pool > poolSize: 11; maxThreads: 1000
Jun-09 08:10:53.310 [main] DEBUG nextflow.Session - Session start
Jun-09 08:10:53.314 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow started -- trace file: /Users/<USER>/projects/topology-tools/test_results/pipeline_info/execution_trace_2025-06-09_08-10-53.txt
Jun-09 08:10:53.586 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
Jun-09 08:10:53.964 [main] INFO  nextflow.Nextflow - 
========================================================================================
                        Micro-C Pipeline - Nextflow DSL2
========================================================================================
Sample ID       : test-sample
FASTQ R1        : tests/small-region-capture-micro-c/small_rcmc_r1.fq.gz
FASTQ R2        : tests/small-region-capture-micro-c/small_rcmc_r2.fq.gz
BWA Index       : tests/small-region-capture-micro-c/test_bwa_index.tgz
Chrom Sizes     : tests/small-region-capture-micro-c/test.chrom.sizes
Output Dir      : ./test_results
Resolution      : 1000
BWA Cores       : 2
Min MAPQ        : 20
========================================================================================

Jun-09 08:10:54.071 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name EXTRACT_BWA_INDEX
Jun-09 08:10:54.073 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:EXTRACT_BWA_INDEX` matches process EXTRACT_BWA_INDEX
Jun-09 08:10:54.084 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:10:54.085 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:10:54.089 [main] DEBUG nextflow.executor.Executor - [warm up] executor > local
Jun-09 08:10:54.094 [main] DEBUG n.processor.LocalPollingMonitor - Creating local task monitor for executor 'local' > cpus=10; memory=64 GB; capacity=10; pollInterval=100ms; dumpInterval=5m
Jun-09 08:10:54.096 [main] DEBUG n.processor.TaskPollingMonitor - >>> barrier register (monitor: local)
Jun-09 08:10:54.112 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'EXTRACT_BWA_INDEX': maxForks=0; fair=false; array=0
Jun-09 08:10:54.144 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name BWA_ALIGN_PAIRTOOLS
Jun-09 08:10:54.144 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:BWA_ALIGN_PAIRTOOLS` matches process BWA_ALIGN_PAIRTOOLS
Jun-09 08:10:54.147 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:10:54.147 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:10:54.148 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'BWA_ALIGN_PAIRTOOLS': maxForks=0; fair=false; array=0
Jun-09 08:10:54.162 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name MERGE_DEDUP_SPLIT
Jun-09 08:10:54.163 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:MERGE_DEDUP_SPLIT` matches process MERGE_DEDUP_SPLIT
Jun-09 08:10:54.165 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:10:54.165 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:10:54.166 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'MERGE_DEDUP_SPLIT': maxForks=0; fair=false; array=0
Jun-09 08:10:54.176 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name COOLER_PROCESS
Jun-09 08:10:54.176 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:COOLER_PROCESS` matches process COOLER_PROCESS
Jun-09 08:10:54.178 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:10:54.178 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:10:54.178 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'COOLER_PROCESS': maxForks=0; fair=false; array=0
Jun-09 08:10:54.187 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name JUICER_HIC
Jun-09 08:10:54.188 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:JUICER_HIC` matches process JUICER_HIC
Jun-09 08:10:54.189 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:10:54.189 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:10:54.190 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'JUICER_HIC': maxForks=0; fair=false; array=0
Jun-09 08:10:54.196 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name QC_METRICS
Jun-09 08:10:54.197 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:QC_METRICS` matches process QC_METRICS
Jun-09 08:10:54.198 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:10:54.198 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:10:54.199 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'QC_METRICS': maxForks=0; fair=false; array=0
Jun-09 08:10:54.201 [main] DEBUG nextflow.Session - Workflow process names [dsl2]: MERGE_DEDUP_SPLIT, COOLER_PROCESS, EXTRACT_BWA_INDEX, JUICER_HIC, BWA_ALIGN_PAIRTOOLS, QC_METRICS
Jun-09 08:10:54.204 [main] DEBUG nextflow.Session - Igniting dataflow network (10)
Jun-09 08:10:54.210 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > EXTRACT_BWA_INDEX
Jun-09 08:10:54.211 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > BWA_ALIGN_PAIRTOOLS
Jun-09 08:10:54.211 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > MERGE_DEDUP_SPLIT
Jun-09 08:10:54.211 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > COOLER_PROCESS
Jun-09 08:10:54.211 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > JUICER_HIC
Jun-09 08:10:54.211 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > QC_METRICS
Jun-09 08:10:54.211 [main] DEBUG nextflow.script.ScriptRunner - Parsed script files:
  Script_43f01a504ba15b5e: /Users/<USER>/projects/topology-tools/nf/modules/merge_dedup_split.nf
  Script_6c968723c735dde5: /Users/<USER>/projects/topology-tools/nf/modules/qc_metrics.nf
  Script_3c186acb8ea2eb2c: /Users/<USER>/projects/topology-tools/nf/modules/juicer_hic.nf
  Script_a457b5df1e6a90ec: /Users/<USER>/projects/topology-tools/nf/modules/bwa_align_pairtools.nf
  Script_900e8f290a0d3a82: /Users/<USER>/projects/topology-tools/nf/modules/cooler_process.nf
  Script_3cb8aa18b03099d1: /Users/<USER>/projects/topology-tools/nf/main.nf
  Script_f4e17a1e4235a98c: /Users/<USER>/projects/topology-tools/nf/modules/extract_bwa_index.nf
Jun-09 08:10:54.211 [main] DEBUG nextflow.script.ScriptRunner - > Awaiting termination 
Jun-09 08:10:54.211 [main] DEBUG nextflow.Session - Session await
Jun-09 08:10:54.309 [Task submitter] WARN  n.executor.BashWrapperBuilder - Task runtime metrics are not reported when using macOS without a container engine
Jun-09 08:10:54.329 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:10:54.332 [Task submitter] INFO  nextflow.Session - [97/e8bf18] Submitted process > EXTRACT_BWA_INDEX (small_rcmc_r1.fq)
Jun-09 08:10:54.408 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 1; name: EXTRACT_BWA_INDEX (small_rcmc_r1.fq); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/97/e8bf18146ac89a130d531530286b08]
Jun-09 08:10:54.409 [Task monitor] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'TaskFinalizer' minSize=10; maxSize=30; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jun-09 08:10:54.440 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:10:54.440 [Task submitter] INFO  nextflow.Session - [f5/86986b] Submitted process > BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq)
Jun-09 08:10:54.510 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 2; name: BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/f5/86986bc527816019a07cac5390771c]
Jun-09 08:10:54.529 [Actor Thread 5] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=MERGE_DEDUP_SPLIT; work-dir=null
  error [nextflow.exception.ProcessUnrecoverableException]: Not a valid path value: 'small_rcmc_r1.fq'
Jun-09 08:10:54.537 [Actor Thread 5] ERROR nextflow.processor.TaskProcessor - Error executing process > 'MERGE_DEDUP_SPLIT'

Caused by:
  Not a valid path value: 'small_rcmc_r1.fq'



Tip: view the complete command output by changing to the process work dir and entering the command `cat .command.out`
Jun-09 08:10:54.538 [Actor Thread 5] DEBUG nextflow.Session - Session aborted -- Cause: Not a valid path value: 'small_rcmc_r1.fq'
Jun-09 08:10:54.545 [Actor Thread 5] DEBUG nextflow.Session - The following nodes are still active:
[process] MERGE_DEDUP_SPLIT
  status=ACTIVE
  port 0: (value) bound ; channel: -
  port 1: (cntrl) -     ; channel: $

[process] COOLER_PROCESS
  status=ACTIVE
  port 0: (value) OPEN  ; channel: -
  port 1: (value) bound ; channel: chrom_sizes
  port 2: (cntrl) -     ; channel: $

[process] JUICER_HIC
  status=ACTIVE
  port 0: (value) OPEN  ; channel: -
  port 1: (value) bound ; channel: chrom_sizes
  port 2: (cntrl) -     ; channel: $

[process] QC_METRICS
  status=ACTIVE
  port 0: (value) OPEN  ; channel: -
  port 1: (cntrl) -     ; channel: $

Jun-09 08:10:54.558 [Actor Thread 5] ERROR nextflow.Nextflow - Pipeline execution stopped with the following message: null
Jun-09 08:10:54.559 [main] DEBUG nextflow.Session - Session await > all processes finished
Jun-09 08:10:54.559 [main] DEBUG nextflow.Session - Session await > all barriers passed
Jun-09 08:10:54.559 [Task monitor] DEBUG n.processor.TaskPollingMonitor - <<< barrier arrives (monitor: local) - terminating tasks monitor poll loop
Jun-09 08:10:54.560 [Actor Thread 10] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=MERGE_DEDUP_SPLIT; work-dir=null
  error [java.lang.InterruptedException]: java.lang.InterruptedException
Jun-09 08:10:54.562 [main] INFO  nextflow.Nextflow - 
========================================================================================
                        Pipeline Execution Summary
========================================================================================
Completed at    : 2025-06-09T08:10:54.560438-04:00
Duration        : 1.3s
Success         : false
Work directory  : /Users/<USER>/projects/topology-tools/work
Exit status     : null
Error message   : None
========================================================================================

Jun-09 08:10:54.562 [main] ERROR nextflow.Nextflow - 💥 Pipeline failed!
Jun-09 08:10:54.562 [main] ERROR nextflow.Nextflow - Check the error message above and workflow logs for details.
Jun-09 08:10:54.565 [main] DEBUG n.trace.WorkflowStatsObserver - Workflow completed > WorkflowStats[succeededCount=2; failedCount=0; ignoredCount=0; cachedCount=0; pendingCount=0; submittedCount=0; runningCount=0; retriesCount=0; abortedCount=0; succeedDuration=90ms; failedDuration=0ms; cachedDuration=0ms;loadCpus=0; loadMemory=0; peakRunning=1; peakCpus=2; peakMemory=6 GB; ]
Jun-09 08:10:54.566 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow completed -- saving trace file
Jun-09 08:10:54.567 [main] DEBUG nextflow.trace.ReportObserver - Workflow completed -- rendering execution report
Jun-09 08:10:55.479 [main] DEBUG nextflow.trace.TimelineObserver - Workflow completed -- rendering execution timeline
Jun-09 08:10:55.590 [main] WARN  nextflow.dag.GraphvizRenderer - Graphviz is required to render the execution DAG in the given format -- See http://www.graphviz.org for more info.
Jun-09 08:10:55.594 [main] DEBUG nextflow.cache.CacheDB - Closing CacheDB done
Jun-09 08:10:55.609 [main] DEBUG nextflow.script.ScriptRunner - > Execution complete -- Goodbye
