Using BWA index: genome_index/test.fa.gz
Processing FASTQ files: small_rcmc_r1.fq.gz, small_rcmc_r2.fq.gz
Output file: small_rcmc_r1.fq.pairsam.gz
[M::bwa_idx_load_from_disk] read 0 ALT contigs
[M::process] read 198020 sequences (20000020 bp)...
[M::process] read 1980 sequences (199980 bp)...
[M::mem_pestat] # candidate unique pairs for (FF, FR, RF, RR): (1891, 14875, 1903, 1878)
[M::mem_pestat] analyzing insert size distribution for orientation FF...
[M::mem_pestat] (25, 50, 75) percentile: (661, 1643, 3509)
[M::mem_pestat] low and high boundaries for computing mean and std.dev: (1, 9205)
[M::mem_pestat] mean and std.dev: (2398.58, 2268.57)
[M::mem_pestat] low and high boundaries for proper pairs: (1, 12053)
[M::mem_pestat] analyzing insert size distribution for orientation FR...
[M::mem_pestat] (25, 50, 75) percentile: (148, 186, 249)
[M::mem_pestat] low and high boundaries for computing mean and std.dev: (1, 451)
[M::mem_pestat] mean and std.dev: (184.91, 61.50)
[M::mem_pestat] low and high boundaries for proper pairs: (1, 552)
[M::mem_pestat] analyzing insert size distribution for orientation RF...
[M::mem_pestat] (25, 50, 75) percentile: (577, 1624, 3433)
[M::mem_pestat] low and high boundaries for computing mean and std.dev: (1, 9145)
[M::mem_pestat] mean and std.dev: (2272.35, 2193.87)
[M::mem_pestat] low and high boundaries for proper pairs: (1, 12001)
[M::mem_pestat] analyzing insert size distribution for orientation RR...
[M::mem_pestat] (25, 50, 75) percentile: (651, 1660, 3413)
[M::mem_pestat] low and high boundaries for computing mean and std.dev: (1, 8937)
[M::mem_pestat] mean and std.dev: (2305.14, 2127.36)
[M::mem_pestat] low and high boundaries for proper pairs: (1, 11699)
[M::mem_process_seqs] Processed 198020 reads in 11.428 CPU sec, 5.822 real sec
[M::mem_pestat] # candidate unique pairs for (FF, FR, RF, RR): (11, 155, 20, 14)
[M::mem_pestat] analyzing insert size distribution for orientation FF...
[M::mem_pestat] (25, 50, 75) percentile: (371, 688, 5392)
[M::mem_pestat] low and high boundaries for computing mean and std.dev: (1, 15434)
[M::mem_pestat] mean and std.dev: (2728.00, 3002.34)
[M::mem_pestat] low and high boundaries for proper pairs: (1, 20455)
[M::mem_pestat] analyzing insert size distribution for orientation FR...
[M::mem_pestat] (25, 50, 75) percentile: (154, 186, 242)
[M::mem_pestat] low and high boundaries for computing mean and std.dev: (1, 418)
[M::mem_pestat] mean and std.dev: (186.68, 58.37)
[M::mem_pestat] low and high boundaries for proper pairs: (1, 506)
[M::mem_pestat] analyzing insert size distribution for orientation RF...
[M::mem_pestat] (25, 50, 75) percentile: (860, 1586, 3037)
[M::mem_pestat] low and high boundaries for computing mean and std.dev: (1, 7391)
[M::mem_pestat] mean and std.dev: (1623.50, 1118.47)
[M::mem_pestat] low and high boundaries for proper pairs: (1, 9568)
[M::mem_pestat] analyzing insert size distribution for orientation RR...
[M::mem_pestat] (25, 50, 75) percentile: (535, 2120, 3455)
[M::mem_pestat] low and high boundaries for computing mean and std.dev: (1, 9295)
[M::mem_pestat] mean and std.dev: (2299.71, 1950.25)
[M::mem_pestat] low and high boundaries for proper pairs: (1, 12215)
[M::mem_process_seqs] Processed 1980 reads in 0.120 CPU sec, 0.059 real sec
[main] Version: 0.7.19-r1273
[main] CMD: bwa mem -5SP -T0 -t2 genome_index/test.fa.gz small_rcmc_r1.fq.gz small_rcmc_r2.fq.gz
[main] Real time: 7.619 sec; CPU: 11.798 sec
Successfully created small_rcmc_r1.fq.pairsam.gz
File size: 18M	small_rcmc_r1.fq.pairsam.gz
