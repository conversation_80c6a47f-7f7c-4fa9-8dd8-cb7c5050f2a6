Unable to find image 'quay.io/biocontainers/bwa:0.7.17--hed695b0_7' locally
0.7.17--hed695b0_7: Pulling from biocontainers/bwa
a3ed95caeb02: Already exists
77c6c00e8b61: Already exists
3aaade50789a: Already exists
00cf8b9f3d2a: Already exists
7ff999a2256f: Already exists
d2ba336f2e44: Already exists
dfda3e01f2b6: Already exists
a3ed95caeb02: Already exists
10c3bb32200b: Already exists
78f9edb1a534: Pulling fs layer
78f9edb1a534: Verifying Checksum
78f9edb1a534: Download complete
78f9edb1a534: Pull complete
Digest: sha256:c3a708bea7947a44288e675fd9791c7aaf0c97dba0710addba336ed193821f8a
Status: Downloaded newer image for quay.io/biocontainers/bwa:0.7.17--hed695b0_7
WARNING: The requested image's platform (linux/amd64) does not match the detected host platform (linux/arm64/v8) and no specific platform was requested
tar: invalid option -- 'z'
BusyBox v1.22.1 (2014-05-23 01:24:27 UTC) multi-call binary.

Usage: tar -[cxthvO] [-X FILE] [-T FILE] [-f TARFILE] [-C DIR] [FILE]...

Create, extract, or list files from a tar file

Operation:
	c	Create
	x	Extract
	t	List
	f	Name of TARFILE ('-' for stdin/out)
	C	Change to DIR before operation
	v	Verbose
	O	Extract to stdout
	h	Follow symlinks
	exclude	File to exclude
	X	File with names to exclude
	T	File with names to include

