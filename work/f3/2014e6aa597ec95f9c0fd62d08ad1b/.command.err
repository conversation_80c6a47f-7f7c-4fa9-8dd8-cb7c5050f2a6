/Users/<USER>/projects/topology-tools/work/f3/2014e6aa597ec95f9c0fd62d08ad1b/.command.sh: line 18: pairtools: command not found
/Users/<USER>/projects/topology-tools/work/f3/2014e6aa597ec95f9c0fd62d08ad1b/.command.sh: line 26: pairtools: command not found
[M::bwa_idx_load_from_disk] read 0 ALT contigs
[M::process] read 200 sequences (20200 bp)...
[M::mem_pestat] # candidate unique pairs for (FF, FR, RF, RR): (3, 17, 3, 2)
[M::mem_pestat] skip orientation FF as there are not enough pairs
[M::mem_pestat] analyzing insert size distribution for orientation FR...
[M::mem_pestat] (25, 50, 75) percentile: (190, 210, 1082)
[M::mem_pestat] low and high boundaries for computing mean and std.dev: (1, 2866)
[M::mem_pestat] mean and std.dev: (351.53, 335.61)
[M::mem_pestat] low and high boundaries for proper pairs: (1, 3758)
[M::mem_pestat] skip orientation RF as there are not enough pairs
[M::mem_pestat] skip orientation RR as there are not enough pairs
[M::mem_process_seqs] Processed 200 reads in 0.012 CPU sec, 0.006 real sec
[fputs] Broken pipe
