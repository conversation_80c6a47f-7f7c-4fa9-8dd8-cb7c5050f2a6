Using BWA index: genome_index/test.fa.gz
Processing FASTQ files: small_rcmc-extra-reads_r1.fq.gz, small_rcmc-extra-reads_r2.fq.gz
Output file: small_rcmc-extra-reads_r1.fq.pairsam.gz
[M::bwa_idx_load_from_disk] read 0 ALT contigs
[M::process] read 200 sequences (20200 bp)...
[M::mem_pestat] # candidate unique pairs for (FF, FR, RF, RR): (3, 17, 3, 2)
[M::mem_pestat] skip orientation FF as there are not enough pairs
[M::mem_pestat] analyzing insert size distribution for orientation FR...
[M::mem_pestat] (25, 50, 75) percentile: (190, 210, 1082)
[M::mem_pestat] low and high boundaries for computing mean and std.dev: (1, 2866)
[M::mem_pestat] mean and std.dev: (351.53, 335.61)
[M::mem_pestat] low and high boundaries for proper pairs: (1, 3758)
[M::mem_pestat] skip orientation RF as there are not enough pairs
[M::mem_pestat] skip orientation RR as there are not enough pairs
[M::mem_process_seqs] Processed 200 reads in 0.012 CPU sec, 0.006 real sec
[main] Version: 0.7.19-r1273
[main] CMD: bwa mem -5SP -T0 -t2 genome_index/test.fa.gz small_rcmc-extra-reads_r1.fq.gz small_rcmc-extra-reads_r2.fq.gz
[main] Real time: 1.861 sec; CPU: 0.016 sec
Successfully created small_rcmc-extra-reads_r1.fq.pairsam.gz
File size: 20K	small_rcmc-extra-reads_r1.fq.pairsam.gz
