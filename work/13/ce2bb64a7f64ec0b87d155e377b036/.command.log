Extracting QC metrics for sample small-rcmc
Input stats file: small-rcmc.stats.txt (0)
Contents of stats file:
total	100100
total_unmapped	54689
total_single_sided_mapped	22741
total_mapped	22670
total_dups	561
total_nodups	22109
cis	22109
trans	0
pair_types/MM	5462
pair_types/NM	13725
pair_types/NN	35502
pair_types/NU	10076
pair_types/NR	5574
pair_types/MU	4827
pair_types/MR	2264
pair_types/UU	15103
pair_types/RU	3579
pair_types/UR	3427
pair_types/DD	561
cis_1kb+	14500

Extracting QC metrics...
Total reads: 100100
Mapped reads: 22670
No-dups reads: 22109
Cis 1kb+ reads: 14500
Cis 10kb+ reads: 8379
Created QC file: small-rcmc_qc.txt
100100
22670
22109
14500
8379
Created QC JSON summary: small-rcmc_qc_summary.json
{
    "sample_id": "small-rcmc",
    "qc_metrics": {
        "reads_total": "100100",
        "reads_mapped": "22670",
        "reads_nodups": "22109",
        "reads_cis_1kb": "14500",
        "reads_cis_10kb": "8379"
    },
    "percentages": {
        "mapped_percent": "22.64",
        "nodups_percent": "22.08",
        "cis_1kb_percent": "14.48",
        "cis_10kb_percent": "8.37"
    },
    "quality_flags": {
        "sufficient_reads": false,
        "good_mapping": false,
        "low_duplication": false
    }
}
QC metrics extraction completed successfully
