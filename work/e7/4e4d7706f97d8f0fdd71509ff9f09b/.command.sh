#!/bin/bash -euo pipefail
echo "100000" > test-sample_qc.txt
    echo "80000" >> test-sample_qc.txt
    echo "75000" >> test-sample_qc.txt
    echo "50000" >> test-sample_qc.txt
    echo "30000" >> test-sample_qc.txt
    
    cat > test-sample_qc_summary.json <<EOF
{
    "sample_id": "test-sample",
    "qc_metrics": {
        "reads_total": "100000",
        "reads_mapped": "80000",
        "reads_nodups": "75000",
        "reads_cis_1kb": "50000",
        "reads_cis_10kb": "30000"
    }
}
EOF
    
    cat <<-END_VERSIONS > versions.yml
    "QC_METRICS":
        grep: 3.7
        cut: 8.32
        bc: 1.07.1
    END_VERSIONS
