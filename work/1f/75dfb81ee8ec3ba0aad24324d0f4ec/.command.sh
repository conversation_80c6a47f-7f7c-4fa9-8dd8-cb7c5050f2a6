#!/bin/bash -euo pipefail
# Find the reference genome file (follow symlinks with -L)
bwt_file=$(find -L genome_index -name "*.bwt" | head -1)
if [ -z "$bwt_file" ]; then
    echo "Error: No BWA index files found in genome_index"
    exit 1
fi

# Get the reference genome prefix (remove .bwt extension)
genome_index_fa=${bwt_file%.bwt}

echo "Using BWA index: $genome_index_fa"
echo "Processing FASTQ files: small_rcmc-extra-reads_r1.fq.gz, small_rcmc-extra-reads_r2.fq.gz"
echo "Output file: small_rcmc-extra-reads_r1.fq.pairsam.gz"

# Run BWA alignment piped to pairtools
bwa mem -5SP -T0 -t2 $genome_index_fa small_rcmc-extra-reads_r1.fq.gz small_rcmc-extra-reads_r2.fq.gz | \
pairtools parse \
    --min-mapq 20 \
    --walks-policy 5unique \
    --max-inter-align-gap 30 \
    --add-columns pos5,pos3,dist_to_5,dist_to_3,read_len \
    --nproc-in 2 \
    --nproc-out 2 \
    --chroms-path test.chrom.sizes | \
pairtools sort \
    --nproc 2 \
    -o small_rcmc-extra-reads_r1.fq.pairsam.gz

# Verify output file was created
if [ ! -f "small_rcmc-extra-reads_r1.fq.pairsam.gz" ]; then
    echo "Error: Output file small_rcmc-extra-reads_r1.fq.pairsam.gz was not created"
    exit 1
fi

echo "Successfully created small_rcmc-extra-reads_r1.fq.pairsam.gz"
echo "File size: $(du -h small_rcmc-extra-reads_r1.fq.pairsam.gz)"

# Create versions file
cat <<-END_VERSIONS > versions.yml
"BWA_ALIGN_PAIRTOOLS":
    bwa: $(echo $(bwa 2>&1) | sed 's/^.*Version: //; s/Contact:.*$//')
    pairtools: $(pairtools --version 2>&1 | sed 's/pairtools, version //')
    samtools: $(echo $(samtools --version 2>&1) | sed 's/^.*samtools //; s/Using.*$//')
END_VERSIONS
