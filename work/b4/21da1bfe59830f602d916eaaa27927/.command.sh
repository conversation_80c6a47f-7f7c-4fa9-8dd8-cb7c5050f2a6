#!/bin/bash -euo pipefail
echo "Processing cooler files for sample small-rcmc"
echo "Input mapped pairs: small-rcmc.mapped.pairs ($(du -h small-rcmc.mapped.pairs | cut -f1))"
echo "Chromosome sizes: test.chrom.sizes"
echo "Resolution: 1000 bp"

# Generate .cool file
echo "Step 1: Generating .cool file..."
cooler cload pairs \
    -c1 2 -p1 3 -c2 4 -p2 5 \
    test.chrom.sizes:1000 \
    small-rcmc.mapped.pairs \
    small-rcmc.cool

if [ ! -f "small-rcmc.cool" ]; then
    echo "Error: Failed to create small-rcmc.cool"
    exit 1
fi
echo "Created small-rcmc.cool ($(du -h small-rcmc.cool | cut -f1))"

# Generate raw mcool file (multi-resolution)
echo "Step 2: Generating raw mcool file..."
cooler zoomify \
    --resolutions 1000N \
    -o small-rcmc.raw.mcool \
    -p 2 \
    small-rcmc.cool

if [ ! -f "small-rcmc.raw.mcool" ]; then
    echo "Error: Failed to create small-rcmc.raw.mcool"
    exit 1
fi
echo "Created small-rcmc.raw.mcool ($(du -h small-rcmc.raw.mcool | cut -f1))"

# Generate balanced mcool file (with matrix balancing)
echo "Step 3: Generating balanced mcool file..."
cooler zoomify \
    --resolutions 1000N \
    -o small-rcmc.balanced.mcool \
    -p 2 \
    --balance \
    --balance-args '--nproc 2' \
    small-rcmc.cool

if [ ! -f "small-rcmc.balanced.mcool" ]; then
    echo "Error: Failed to create small-rcmc.balanced.mcool"
    exit 1
fi
echo "Created small-rcmc.balanced.mcool ($(du -h small-rcmc.balanced.mcool | cut -f1))"

# Show summary
echo "Cooler processing completed successfully:"
echo "  Cool file: small-rcmc.cool ($(du -h small-rcmc.cool | cut -f1))"
echo "  Raw mcool: small-rcmc.raw.mcool ($(du -h small-rcmc.raw.mcool | cut -f1))"
echo "  Balanced mcool: small-rcmc.balanced.mcool ($(du -h small-rcmc.balanced.mcool | cut -f1))"

# Create versions file
cat <<-END_VERSIONS > versions.yml
"COOLER_PROCESS":
    cooler: $(cooler --version 2>&1 | sed 's/cooler, version //')
END_VERSIONS
