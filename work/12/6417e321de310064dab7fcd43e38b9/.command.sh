#!/bin/bash -euo pipefail
echo "Extracting QC metrics for sample small-rcmc"
    echo "Input stats file: small-rcmc.stats.txt ($(du -h small-rcmc.stats.txt | cut -f1))"
    
    # Check if stats file exists and is not empty
    if [ ! -s "small-rcmc.stats.txt" ]; then
        echo "Error: Stats file small-rcmc.stats.txt is empty or does not exist"
        exit 1
    fi
    
    echo "Contents of stats file:"
    head -20 small-rcmc.stats.txt
    echo ""
    
    # Extract QC metrics in the same order as Python pipeline
    echo "Extracting QC metrics..."
    
    # Extract total reads
    total_reads=$(cat small-rcmc.stats.txt | grep -w "total" | cut -f2 || echo "0")
    echo "Total reads: $total_reads"
    
    # Extract mapped reads
    mapped_reads=$(cat small-rcmc.stats.txt | grep -w "total_mapped" | cut -f2 || echo "0")
    echo "Mapped reads: $mapped_reads"
    
    # Extract deduplicated reads
    nodups_reads=$(cat small-rcmc.stats.txt | grep -w "total_nodups" | cut -f2 || echo "0")
    echo "No-dups reads: $nodups_reads"
    
    # Extract cis reads (1kb+)
    cis_1kb_reads=$(cat small-rcmc.stats.txt | grep -w "cis_1kb+" | cut -f2 || echo "0")
    echo "Cis 1kb+ reads: $cis_1kb_reads"
    
    # Extract cis reads (10kb+)
    cis_10kb_reads=$(cat small-rcmc.stats.txt | grep -w "cis_10kb+" | cut -f2 || echo "0")
    echo "Cis 10kb+ reads: $cis_10kb_reads"
    
    # Write QC metrics to text file (same format as Python pipeline)
    cat > small-rcmc_qc.txt <<EOF
$total_reads
$mapped_reads
$nodups_reads
$cis_1kb_reads
$cis_10kb_reads
EOF
    
    echo "Created QC file: small-rcmc_qc.txt"
    cat small-rcmc_qc.txt
    
    # Calculate percentages for JSON summary
    if [ "$total_reads" -gt 0 ]; then
        mapped_pct=$(echo "scale=2; $mapped_reads * 100 / $total_reads" | bc -l 2>/dev/null || echo "0")
        nodups_pct=$(echo "scale=2; $nodups_reads * 100 / $total_reads" | bc -l 2>/dev/null || echo "0")
        cis_1kb_pct=$(echo "scale=2; $cis_1kb_reads * 100 / $total_reads" | bc -l 2>/dev/null || echo "0")
        cis_10kb_pct=$(echo "scale=2; $cis_10kb_reads * 100 / $total_reads" | bc -l 2>/dev/null || echo "0")
    else
        mapped_pct="0"
        nodups_pct="0"
        cis_1kb_pct="0"
        cis_10kb_pct="0"
    fi
    
    # Create JSON summary
    cat > small-rcmc_qc_summary.json <<EOF
{
    "sample_id": "small-rcmc",
    "qc_metrics": {
        "reads_total": "$total_reads",
        "reads_mapped": "$mapped_reads",
        "reads_nodups": "$nodups_reads",
        "reads_cis_1kb": "$cis_1kb_reads",
        "reads_cis_10kb": "$cis_10kb_reads"
    },
    "percentages": {
        "mapped_percent": "$mapped_pct",
        "nodups_percent": "$nodups_pct",
        "cis_1kb_percent": "$cis_1kb_pct",
        "cis_10kb_percent": "$cis_10kb_pct"
    },
    "quality_flags": {
        "sufficient_reads": $([ "$total_reads" -gt 1000000 ] && echo "true" || echo "false"),
        "good_mapping": $([ "$mapped_pct" != "0" ] && [ $(echo "$mapped_pct > 50" | bc -l 2>/dev/null || echo 0) -eq 1 ] && echo "true" || echo "false"),
        "low_duplication": $([ "$nodups_pct" != "0" ] && [ $(echo "$nodups_pct > 70" | bc -l 2>/dev/null || echo 0) -eq 1 ] && echo "true" || echo "false")
    }
}
EOF
    
    echo "Created QC JSON summary: small-rcmc_qc_summary.json"
    cat small-rcmc_qc_summary.json
    
    echo "QC metrics extraction completed successfully"
    
    # Create versions file
    cat <<-END_VERSIONS > versions.yml
    "QC_METRICS":
        grep: $(grep --version | head -1 | sed 's/grep (GNU grep) //')
        cut: $(cut --version | head -1 | sed 's/cut (GNU coreutils) //')
        bc: $(bc --version | head -1 | sed 's/bc //')
    END_VERSIONS
