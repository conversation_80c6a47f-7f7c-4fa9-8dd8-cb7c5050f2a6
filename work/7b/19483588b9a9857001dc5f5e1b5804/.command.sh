#!/bin/bash -euo pipefail
echo "Processing 2 pairsam files for sample small-rcmc"
echo "Input files:"
for file in small_rcmc-extra-reads_r1.fq.pairsam.gz small_rcmc_r1.fq.pairsam.gz; do
    echo "  $file ($(du -h $file | cut -f1))"
done

echo "Starting merge, dedup, and split process..."

# Merge, deduplicate, and split pairs
pairtools merge \
    --nproc 2 \
    small_rcmc-extra-reads_r1.fq.pairsam.gz small_rcmc_r1.fq.pairsam.gz | \
pairtools dedup \
    --nproc-in 1 \
    --nproc-out 2 \
    --mark-dups \
    --output-stats small-rcmc.stats.txt | \
pairtools split \
    --nproc-in 1 \
    --nproc-out 2 \
    --output-pairs small-rcmc.mapped.pairs \
    --output-sam - | \
samtools view -bS -@2 | \
samtools sort -@2 -o small-rcmc.bam

# Index the BAM file
samtools index small-rcmc.bam

# Verify output files were created
for file in small-rcmc.mapped.pairs small-rcmc.stats.txt small-rcmc.bam small-rcmc.bam.bai; do
    if [ ! -f "$file" ]; then
        echo "Error: Output file $file was not created"
        exit 1
    fi
    echo "Created $file ($(du -h $file | cut -f1))"
done

# Show some statistics
echo "Statistics summary:"
echo "  Mapped pairs: $(wc -l < small-rcmc.mapped.pairs | sed 's/^[[:space:]]*//')"
echo "  BAM file size: $(du -h small-rcmc.bam | cut -f1)"

# Create versions file
cat <<-END_VERSIONS > versions.yml
"MERGE_DEDUP_SPLIT":
    pairtools: $(pairtools --version 2>&1 | sed 's/pairtools, version //')
    samtools: $(echo $(samtools --version 2>&1) | sed 's/^.*samtools //; s/Using.*$//')
END_VERSIONS
