Processing 2 pairsam files for sample small-rcmc
Input files:
  small_rcmc-extra-reads_r1.fq.pairsam.gz (0)
  small_rcmc_r1.fq.pairsam.gz (0)
Starting merge, dedup, and split process...
WARNING:py.warnings:/Users/<USER>/miniforge3/envs/microc-pipeline/lib/python3.9/site-packages/pairtools/lib/stats.py:410: RuntimeWarning: invalid value encountered in divide
  np.abs(dist_freqs_by_strands[strands] - avg_freq_all_strands)

[bam_sort_core] merging from 0 files and 2 in-memory blocks...
Created small-rcmc.mapped.pairs (4.1M)
Created small-rcmc.stats.txt (12K)
Created small-rcmc.bam (3.8M)
Created small-rcmc.bam.bai (4.0K)
Statistics summary:
  Mapped pairs: 22129
  BAM file size: 3.8M
