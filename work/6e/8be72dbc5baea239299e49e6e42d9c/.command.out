Processing Juicer HiC file for sample small-rcmc
Input mapped pairs: small-rcmc.mapped.pairs (0)
Chromosome sizes: test.chrom.sizes
Step 1: Converting pairs to Juicer format...
Created small-rcmc.juicer.pairs (2.4M)
Step 2: Looking for Juicer tools JAR file...
Warning: Juicer tools JAR not found. Attempting to download...
Step 3: Generating HiC file with Juicer tools...
Using JAR: juicer_tools/juicer_tools.jar
Memory: 6g
Threads: 2
WARNING: sun.reflect.Reflection.getCallerClass is not supported. This will impact performance.
WARN [2025-06-09T09:04:16,612]  [Globals.java:138] [main]  Development mode is enabled
Using 2 CPU thread(s) for primary task
Using 10 CPU thread(s) for secondary task
Not including fragment map
Start preprocess
Writing header
Writing body
Warning: Juicer HiC generation failed (this is expected with some data formats)
Error details saved to juicer_error.log
Pipeline will continue - cooler files provide equivalent functionality
