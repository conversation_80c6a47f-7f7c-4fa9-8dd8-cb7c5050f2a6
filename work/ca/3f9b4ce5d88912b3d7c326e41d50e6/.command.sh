#!/bin/bash -euo pipefail
# Create genome index directory
mkdir -p genome_index

# Extract BWA index (compatible with BusyBox tar)
if tar --help 2>&1 | grep -q "GNU tar"; then
    # GNU tar supports -z flag
    tar zxvf test_bwa_index.tgz -C genome_index
else
    # BusyBox tar doesn't support -z, use gzip -dc + tar
    gzip -dc test_bwa_index.tgz | tar xvf - -C genome_index
fi

# Find the reference FASTA file (should have .bwt extension companion)
bwt_file=$(find genome_index -name "*.bwt" | head -1)
if [ -z "$bwt_file" ]; then
    echo "Error: No BWA index files found in test_bwa_index.tgz"
    exit 1
fi

# Log the extracted files
echo "Extracted BWA index files:"
ls -la genome_index/

# Create versions file
cat <<-END_VERSIONS > versions.yml
"EXTRACT_BWA_INDEX":
    bwa: $(echo $(bwa 2>&1) | sed 's/^.*Version: //; s/Contact:.*$//')
    tar: $(tar --version 2>/dev/null | head -1 | sed 's/tar (GNU tar) //' || echo "BusyBox tar")
END_VERSIONS
