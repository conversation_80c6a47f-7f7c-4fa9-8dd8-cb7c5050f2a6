{"sample_id": "small-rcmc-benchmark", "start_time": "2025-06-06T23:04:38.832804", "end_time": "2025-06-06T23:04:55.584581", "total_time_seconds": 16.751770973205566, "total_time_minutes": 0.27919618288675946, "input_size_gb": 0.009806429035961628, "throughput_gb_per_hour": 2.107427601889328, "initial_memory_gb": 0.0954132080078125, "final_memory_gb": 0.0954437255859375, "peak_memory_gb": 0.0954437255859375, "return_code": 0, "success": true, "num_cores": 4, "resolution": 1000, "timing_breakdown": {}, "stderr": "2025-06-06 23:04:38,866 - INFO - Starting Micro-C pipeline for sample small-rcmc-benchmark\n2025-06-06 23:04:38,867 - INFO - Total fastq size: 0 GB\n2025-06-06 23:04:38,867 - INFO - Processing FASTQ pair 1/2\n2025-06-06 23:04:38,867 - INFO - Starting Extract reference index\n2025-06-06 23:04:38,867 - INFO - Command: tar zxvf tests/small-region-capture-micro-c/test_bwa_index.tgz -C benchmark_results/small-rcmc-benchmark_output/align/chunk_0/genome_index\n2025-06-06 23:04:38,892 - INFO - Completed Extract reference index in 0.02 seconds\n2025-06-06 23:04:38,892 - INFO - Starting Find bwt file\n2025-06-06 23:04:38,892 - INFO - Command: find benchmark_results/small-rcmc-benchmark_output/align/chunk_0/genome_index -name '*.bwt'\n2025-06-06 23:04:38,901 - INFO - Completed Find bwt file in 0.01 seconds\n2025-06-06 23:04:38,901 - INFO - Starting Align and process small_rcmc_r1.fq.gz\n2025-06-06 23:04:38,901 - INFO - Command: \n    bwa mem -5SP -T0 -t4 benchmark_results/small-rcmc-benchmark_output/align/chunk_0/genome_index/test.fa.gz tests/small-region-capture-micro-c/small_rcmc_r1.fq.gz tests/small-region-capture-micro-c/small_rcmc_r2.fq.gz |     pairtools parse --min-mapq 20 --walks-policy 5unique     --max-inter-align-gap 30 --add-columns pos5,pos3,dist_to_5,dist_to_3,read_len     --nproc-in 4 --nproc-out 4 --chroms-path tests/small-region-capture-micro-c/test.chrom.sizes |     pairtools sort --nproc 4 -o benchmark_results/small-rcmc-benchmark_output/align/chunk_0/small_rcmc_r1.fq.gz.pairsam.gz\n    \n2025-06-06 23:04:44,693 - INFO - Completed Align and process small_rcmc_r1.fq.gz in 5.79 seconds\n2025-06-06 23:04:44,694 - INFO - Processing FASTQ pair 2/2\n2025-06-06 23:04:44,694 - INFO - Starting Extract reference index\n2025-06-06 23:04:44,694 - INFO - Command: tar zxvf tests/small-region-capture-micro-c/test_bwa_index.tgz -C benchmark_results/small-rcmc-benchmark_output/align/chunk_1/genome_index\n2025-06-06 23:04:44,719 - INFO - Completed Extract reference index in 0.03 seconds\n2025-06-06 23:04:44,719 - INFO - Starting Find bwt file\n2025-06-06 23:04:44,719 - INFO - Command: find benchmark_results/small-rcmc-benchmark_output/align/chunk_1/genome_index -name '*.bwt'\n2025-06-06 23:04:44,726 - INFO - Completed Find bwt file in 0.01 seconds\n2025-06-06 23:04:44,726 - INFO - Starting Align and process small_rcmc-extra-reads_r1.fq.gz\n2025-06-06 23:04:44,726 - INFO - Command: \n    bwa mem -5SP -T0 -t4 benchmark_results/small-rcmc-benchmark_output/align/chunk_1/genome_index/test.fa.gz tests/small-region-capture-micro-c/small_rcmc-extra-reads_r1.fq.gz tests/small-region-capture-micro-c/small_rcmc-extra-reads_r2.fq.gz |     pairtools parse --min-mapq 20 --walks-policy 5unique     --max-inter-align-gap 30 --add-columns pos5,pos3,dist_to_5,dist_to_3,read_len     --nproc-in 4 --nproc-out 4 --chroms-path tests/small-region-capture-micro-c/test.chrom.sizes |     pairtools sort --nproc 4 -o benchmark_results/small-rcmc-benchmark_output/align/chunk_1/small_rcmc-extra-reads_r1.fq.gz.pairsam.gz\n    \n2025-06-06 23:04:45,894 - INFO - Completed Align and process small_rcmc-extra-reads_r1.fq.gz in 1.17 seconds\n2025-06-06 23:04:45,895 - INFO - Starting Merge, dedup, and split pairs\n2025-06-06 23:04:45,895 - INFO - Command: \n    pairtools merge --nproc 12 benchmark_results/small-rcmc-benchmark_output/align/chunk_0/small_rcmc_r1.fq.gz.pairsam.gz benchmark_results/small-rcmc-benchmark_output/align/chunk_1/small_rcmc-extra-reads_r1.fq.gz.pairsam.gz |     pairtools dedup --nproc-in 2 --nproc-out 8 --mark-dups --output-stats benchmark_results/small-rcmc-benchmark_output/merge/small-rcmc-benchmark.stats.txt |     pairtools split --nproc-in 2 --nproc-out 8 --output-pairs benchmark_results/small-rcmc-benchmark_output/merge/small-rcmc-benchmark.mapped.pairs --output-sam - |     samtools view -bS -@6 |     samtools sort -@6 -o benchmark_results/small-rcmc-benchmark_output/merge/small-rcmc-benchmark.bam\n    \n2025-06-06 23:04:48,775 - INFO - Completed Merge, dedup, and split pairs in 2.88 seconds\n2025-06-06 23:04:48,775 - INFO - Starting Index BAM\n2025-06-06 23:04:48,775 - INFO - Command: samtools index benchmark_results/small-rcmc-benchmark_output/merge/small-rcmc-benchmark.bam\n2025-06-06 23:04:48,802 - INFO - Completed Index BAM in 0.03 seconds\n2025-06-06 23:04:48,802 - INFO - Starting Generate cooler file\n2025-06-06 23:04:48,802 - INFO - Command: cooler cload pairs -c1 2 -p1 3 -c2 4 -p2 5 tests/small-region-capture-micro-c/test.chrom.sizes:1000 benchmark_results/small-rcmc-benchmark_output/merge/small-rcmc-benchmark.mapped.pairs benchmark_results/small-rcmc-benchmark_output/cooler/small-rcmc-benchmark.cool\n2025-06-06 23:04:50,258 - INFO - Completed Generate cooler file in 1.46 seconds\n2025-06-06 23:04:50,258 - INFO - Starting Generate raw mcool file\n2025-06-06 23:04:50,258 - INFO - Command: cooler zoomify --resolutions 1000N -o benchmark_results/small-rcmc-benchmark_output/cooler/small-rcmc-benchmark.raw.mcool -p 4 benchmark_results/small-rcmc-benchmark_output/cooler/small-rcmc-benchmark.cool\n2025-06-06 23:04:51,599 - INFO - Completed Generate raw mcool file in 1.34 seconds\n2025-06-06 23:04:51,599 - INFO - Starting Generate balanced mcool file\n2025-06-06 23:04:51,599 - INFO - Command: cooler zoomify --resolutions 1000N -o benchmark_results/small-rcmc-benchmark_output/cooler/small-rcmc-benchmark.balanced.mcool -p 4 --balance --balance-args '--nproc 4' benchmark_results/small-rcmc-benchmark_output/cooler/small-rcmc-benchmark.cool\n2025-06-06 23:04:54,864 - INFO - Completed Generate balanced mcool file in 3.26 seconds\n2025-06-06 23:04:54,865 - INFO - Starting Convert pairs to Juicer format\n2025-06-06 23:04:54,865 - INFO - Command: \n    awk 'BEGIN{OFS=\"\\t\"} /^#/{print} !/^#/{print $1,$2,$3,$4,$5,$6,$7,$8}' benchmark_results/small-rcmc-benchmark_output/merge/small-rcmc-benchmark.mapped.pairs > benchmark_results/small-rcmc-benchmark_output/hic/small-rcmc-benchmark.juicer.pairs\n    \n2025-06-06 23:04:55,014 - INFO - Completed Convert pairs to Juicer format in 0.15 seconds\n2025-06-06 23:04:55,014 - INFO - Starting Generate Juicer HiC file\n2025-06-06 23:04:55,014 - INFO - Command: \n    java -Xmx120g -Djava.awt.headless=true -jar /Users/<USER>/projects/topology-tools/juicer_tools/juicer_tools.jar pre         --threads 4         benchmark_results/small-rcmc-benchmark_output/hic/small-rcmc-benchmark.juicer.pairs         benchmark_results/small-rcmc-benchmark_output/hic/small-rcmc-benchmark.hic         tests/small-region-capture-micro-c/test.chrom.sizes\n    \n2025-06-06 23:04:55,559 - ERROR - Generate Juicer HiC file failed with return code 56\n2025-06-06 23:04:55,559 - ERROR - STDERR: No mndIndex provided\nUsing single threaded preprocessor\njava.lang.ArrayIndexOutOfBoundsException: Index 3 out of bounds for length 2\n\tat juicebox.tools.utils.original.mnditerator.ComplexLineParser.generateBasicPair(ComplexLineParser.java:56)\n\tat juicebox.tools.utils.original.mnditerator.MNDFileParser.parseDCICFormat(MNDFileParser.java:118)\n\tat juicebox.tools.utils.original.mnditerator.MNDFileParser.parse(MNDFileParser.java:83)\n\tat juicebox.tools.utils.original.mnditerator.GenericPairIterator.advance(GenericPairIterator.java:56)\n\tat juicebox.tools.utils.original.mnditerator.GenericPairIterator.next(GenericPairIterator.java:46)\n\tat juicebox.tools.utils.original.Preprocessor.computeWholeGenomeMatrix(Preprocessor.java:603)\n\tat juicebox.tools.utils.original.Preprocessor.writeBody(Preprocessor.java:690)\n\tat juicebox.tools.utils.original.Preprocessor.preprocess(Preprocessor.java:452)\n\tat juicebox.tools.clt.old.PreProcessing.run(PreProcessing.java:176)\n\tat juicebox.tools.HiCTools.main(HiCTools.java:97)\n\n2025-06-06 23:04:55,559 - WARNING - Failed to generate HiC file: Generate Juicer HiC file failed\n2025-06-06 23:04:55,559 - INFO - Starting Extract QC metrics\n2025-06-06 23:04:55,559 - INFO - Command: \n    cat benchmark_results/small-rcmc-benchmark_output/merge/small-rcmc-benchmark.stats.txt | grep -w \"total\" | cut -f2 > benchmark_results/small-rcmc-benchmark_output/qc/small-rcmc-benchmark_qc.txt\n    cat benchmark_results/small-rcmc-benchmark_output/merge/small-rcmc-benchmark.stats.txt | grep -w \"total_mapped\" | cut -f2 >> benchmark_results/small-rcmc-benchmark_output/qc/small-rcmc-benchmark_qc.txt\n    cat benchmark_results/small-rcmc-benchmark_output/merge/small-rcmc-benchmark.stats.txt | grep -w \"total_nodups\" | cut -f2 >> benchmark_results/small-rcmc-benchmark_output/qc/small-rcmc-benchmark_qc.txt\n    cat benchmark_results/small-rcmc-benchmark_output/merge/small-rcmc-benchmark.stats.txt | grep -w \"cis_1kb+\" | cut -f2 >> benchmark_results/small-rcmc-benchmark_output/qc/small-rcmc-benchmark_qc.txt\n    cat benchmark_results/small-rcmc-benchmark_output/merge/small-rcmc-benchmark.stats.txt | grep -w \"cis_10kb+\" | cut -f2 >> benchmark_results/small-rcmc-benchmark_output/qc/small-rcmc-benchmark_qc.txt\n    \n2025-06-06 23:04:55,580 - INFO - Completed Extract QC metrics in 0.02 seconds\n2025-06-06 23:04:55,580 - INFO - QC Results: {'reads_total': '100100', 'reads_mapped': '22670', 'reads_nodups': '22109', 'reads_cis_1kb': '14500', 'reads_cis_10kb': '8379'}\n2025-06-06 23:04:55,580 - INFO - Pipeline completed successfully\n2025-06-06 23:04:55,580 - INFO - Output files:\n2025-06-06 23:04:55,580 - INFO -   Mapped pairs: benchmark_results/small-rcmc-benchmark_output/merge/small-rcmc-benchmark.mapped.pairs\n2025-06-06 23:04:55,580 - INFO -   BAM file: benchmark_results/small-rcmc-benchmark_output/merge/small-rcmc-benchmark.bam\n2025-06-06 23:04:55,580 - INFO -   HiC file: Failed to generate\n2025-06-06 23:04:55,581 - INFO -   Raw mcool file: benchmark_results/small-rcmc-benchmark_output/cooler/small-rcmc-benchmark.raw.mcool\n2025-06-06 23:04:55,581 - INFO -   Balanced mcool file: benchmark_results/small-rcmc-benchmark_output/cooler/small-rcmc-benchmark.balanced.mcool\n2025-06-06 23:04:55,581 - INFO -   QC stats: {'reads_total': '100100', 'reads_mapped': '22670', 'reads_nodups': '22109', 'reads_cis_1kb': '14500', 'reads_cis_10kb': '8379'}\n"}