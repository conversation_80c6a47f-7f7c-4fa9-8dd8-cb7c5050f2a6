{"microc.sample_id": "small-rcmc", "microc.fastq_r1": ["tests/small-region-capture-micro-c/small_rcmc_r1.fq.gz", "tests/small-region-capture-micro-c/small_rcmc-extra-reads_r1.fq.gz"], "microc.fastq_r2": ["tests/small-region-capture-micro-c/small_rcmc_r2.fq.gz", "tests/small-region-capture-micro-c/small_rcmc-extra-reads_r2.fq.gz"], "microc.reference_bwa_idx": "tests/small-region-capture-micro-c/test_bwa_index.tgz", "microc.chrom_sizes": "tests/small-region-capture-micro-c/test.chrom.sizes", "microc.cooler.resolution": "1000", "microc.microc_align.bwa_cores": "2", "microc.num_reads_per_chunk": 50000, "microc.resource_monitor_script": "tests/monitor_v3.sh", "microc.top_monitor_script": "tests/monitor_top.sh"}