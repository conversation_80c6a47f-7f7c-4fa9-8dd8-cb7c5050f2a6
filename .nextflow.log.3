Jun-09 08:46:29.564 [main] DEBUG nextflow.cli.Launcher - $> nextflow run nf/main.nf -profile test -stub-run
Jun-09 08:46:29.620 [main] DEBUG nextflow.cli.CmdRun - N E X T F L O W  ~  version 25.04.3
Jun-09 08:46:29.643 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/Users/<USER>/.nextflow/plugins; core-plugins: nf-amazon@2.15.0,nf-azure@1.16.0,nf-cloudcache@0.4.3,nf-codecommit@0.2.3,nf-console@1.2.1,nf-google@1.21.0,nf-k8s@1.0.0,nf-tower@1.11.3,nf-wave@1.12.1
Jun-09 08:46:29.665 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
Jun-09 08:46:29.666 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
Jun-09 08:46:29.668 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.12.0 in 'deployment' mode
Jun-09 08:46:29.675 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
Jun-09 08:46:29.688 [main] DEBUG nextflow.config.ConfigBuilder - Found config base: /Users/<USER>/projects/topology-tools/nf/nextflow.config
Jun-09 08:46:29.690 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /Users/<USER>/projects/topology-tools/nf/nextflow.config
Jun-09 08:46:29.715 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /Users/<USER>/.nextflow/secrets/store.json
Jun-09 08:46:29.717 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@7cedfa63] - activable => nextflow.secret.LocalSecretsProvider@7cedfa63
Jun-09 08:46:29.721 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `test`
Jun-09 08:46:30.309 [main] DEBUG nextflow.config.ConfigBuilder - Available config profiles: [cluster, debug, test, local, docker, cloud, shifter, mamba, charliecloud, conda, singularity, arm, test_full, podman]
Jun-09 08:46:30.330 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declaration
Jun-09 08:46:30.343 [main] DEBUG nextflow.cli.CmdRun - Launching `nf/main.nf` [dreamy_moriondo] DSL2 - revision: ddca790c99
Jun-09 08:46:30.344 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
Jun-09 08:46:30.344 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[]
Jun-09 08:46:30.378 [main] DEBUG nextflow.Session - Session UUID: fa6cfdc5-**************-debda2857fb2
Jun-09 08:46:30.378 [main] DEBUG nextflow.Session - Run name: dreamy_moriondo
Jun-09 08:46:30.378 [main] DEBUG nextflow.Session - Executor pool size: 10
Jun-09 08:46:30.383 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
Jun-09 08:46:30.386 [main] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'FileTransfer' minSize=10; maxSize=30; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jun-09 08:46:30.401 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 25.04.3 build 5949
  Created: 02-06-2025 20:56 UTC (16:56 EDT)
  System: Mac OS X 15.4.1
  Runtime: Groovy 4.0.26 on OpenJDK 64-Bit Server VM 23.0.2+7
  Encoding: UTF-8 (UTF-8)
  Process: <EMAIL> [127.0.0.1]
  CPUs: 10 - Mem: 64 GB (247.5 MB) - Swap: 7 GB (1.4 GB)
Jun-09 08:46:30.410 [main] DEBUG nextflow.Session - Work-dir: /Users/<USER>/projects/topology-tools/work [Mac OS X]
Jun-09 08:46:30.410 [main] DEBUG nextflow.Session - Script base path does not exist or is not a directory: /Users/<USER>/projects/topology-tools/nf/bin
Jun-09 08:46:30.417 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
Jun-09 08:46:30.422 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
Jun-09 08:46:30.438 [main] DEBUG nextflow.Session - Observer factory (v2): LinObserverFactory
Jun-09 08:46:30.462 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
Jun-09 08:46:30.469 [main] DEBUG nextflow.util.CustomThreadPool - Creating default thread pool > poolSize: 11; maxThreads: 1000
Jun-09 08:46:30.506 [main] DEBUG nextflow.Session - Session start
Jun-09 08:46:30.509 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow started -- trace file: /Users/<USER>/projects/topology-tools/test_results/pipeline_info/execution_trace_2025-06-09_08-46-30.txt
Jun-09 08:46:30.675 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
Jun-09 08:46:31.007 [main] INFO  nextflow.Nextflow - 
========================================================================================
                        Micro-C Pipeline - Nextflow DSL2
========================================================================================
Sample ID       : small-rcmc
FASTQ R1        : tests/small-region-capture-micro-c/small_rcmc_r1.fq.gz,tests/small-region-capture-micro-c/small_rcmc-extra-reads_r1.fq.gz
FASTQ R2        : tests/small-region-capture-micro-c/small_rcmc_r2.fq.gz,tests/small-region-capture-micro-c/small_rcmc-extra-reads_r2.fq.gz
BWA Index       : tests/small-region-capture-micro-c/test_bwa_index.tgz
Chrom Sizes     : tests/small-region-capture-micro-c/test.chrom.sizes
Output Dir      : ./test_results
Resolution      : 1000
BWA Cores       : 2
Min MAPQ        : 20
========================================================================================

Jun-09 08:46:31.101 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name EXTRACT_BWA_INDEX
Jun-09 08:46:31.103 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:EXTRACT_BWA_INDEX` matches process EXTRACT_BWA_INDEX
Jun-09 08:46:31.112 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:46:31.113 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:46:31.117 [main] DEBUG nextflow.executor.Executor - [warm up] executor > local
Jun-09 08:46:31.120 [main] DEBUG n.processor.LocalPollingMonitor - Creating local task monitor for executor 'local' > cpus=10; memory=64 GB; capacity=10; pollInterval=100ms; dumpInterval=5m
Jun-09 08:46:31.122 [main] DEBUG n.processor.TaskPollingMonitor - >>> barrier register (monitor: local)
Jun-09 08:46:31.134 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'EXTRACT_BWA_INDEX': maxForks=0; fair=false; array=0
Jun-09 08:46:31.158 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name BWA_ALIGN_PAIRTOOLS
Jun-09 08:46:31.159 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:BWA_ALIGN_PAIRTOOLS` matches process BWA_ALIGN_PAIRTOOLS
Jun-09 08:46:31.160 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:46:31.160 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:46:31.161 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'BWA_ALIGN_PAIRTOOLS': maxForks=0; fair=false; array=0
Jun-09 08:46:31.172 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name MERGE_DEDUP_SPLIT
Jun-09 08:46:31.173 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:MERGE_DEDUP_SPLIT` matches process MERGE_DEDUP_SPLIT
Jun-09 08:46:31.174 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:46:31.174 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:46:31.175 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'MERGE_DEDUP_SPLIT': maxForks=0; fair=false; array=0
Jun-09 08:46:31.181 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name COOLER_PROCESS
Jun-09 08:46:31.182 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:COOLER_PROCESS` matches process COOLER_PROCESS
Jun-09 08:46:31.184 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:46:31.184 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:46:31.184 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'COOLER_PROCESS': maxForks=0; fair=false; array=0
Jun-09 08:46:31.192 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name JUICER_HIC
Jun-09 08:46:31.192 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:JUICER_HIC` matches process JUICER_HIC
Jun-09 08:46:31.194 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:46:31.194 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:46:31.194 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'JUICER_HIC': maxForks=0; fair=false; array=0
Jun-09 08:46:31.199 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name QC_METRICS
Jun-09 08:46:31.200 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:QC_METRICS` matches process QC_METRICS
Jun-09 08:46:31.201 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:46:31.201 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:46:31.201 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'QC_METRICS': maxForks=0; fair=false; array=0
Jun-09 08:46:31.204 [main] DEBUG nextflow.Session - Workflow process names [dsl2]: MERGE_DEDUP_SPLIT, COOLER_PROCESS, EXTRACT_BWA_INDEX, JUICER_HIC, BWA_ALIGN_PAIRTOOLS, QC_METRICS
Jun-09 08:46:31.205 [main] DEBUG nextflow.Session - Igniting dataflow network (10)
Jun-09 08:46:31.210 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > EXTRACT_BWA_INDEX
Jun-09 08:46:31.210 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > BWA_ALIGN_PAIRTOOLS
Jun-09 08:46:31.210 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > MERGE_DEDUP_SPLIT
Jun-09 08:46:31.210 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > COOLER_PROCESS
Jun-09 08:46:31.210 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > JUICER_HIC
Jun-09 08:46:31.210 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > QC_METRICS
Jun-09 08:46:31.210 [main] DEBUG nextflow.script.ScriptRunner - Parsed script files:
  Script_43f01a504ba15b5e: /Users/<USER>/projects/topology-tools/nf/modules/merge_dedup_split.nf
  Script_bc6798ae2f6f04bf: /Users/<USER>/projects/topology-tools/nf/main.nf
  Script_6c968723c735dde5: /Users/<USER>/projects/topology-tools/nf/modules/qc_metrics.nf
  Script_3c186acb8ea2eb2c: /Users/<USER>/projects/topology-tools/nf/modules/juicer_hic.nf
  Script_a457b5df1e6a90ec: /Users/<USER>/projects/topology-tools/nf/modules/bwa_align_pairtools.nf
  Script_900e8f290a0d3a82: /Users/<USER>/projects/topology-tools/nf/modules/cooler_process.nf
  Script_f4e17a1e4235a98c: /Users/<USER>/projects/topology-tools/nf/modules/extract_bwa_index.nf
Jun-09 08:46:31.210 [main] DEBUG nextflow.script.ScriptRunner - > Awaiting termination 
Jun-09 08:46:31.211 [main] DEBUG nextflow.Session - Session await
Jun-09 08:46:31.313 [Task submitter] WARN  n.executor.BashWrapperBuilder - Task runtime metrics are not reported when using macOS without a container engine
Jun-09 08:46:31.322 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:46:31.323 [Task submitter] INFO  nextflow.Session - [e3/7990cb] Submitted process > EXTRACT_BWA_INDEX (small_rcmc-extra-reads_r1.fq)
Jun-09 08:46:31.330 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:46:31.330 [Task submitter] INFO  nextflow.Session - [e5/dd3d3a] Submitted process > EXTRACT_BWA_INDEX (small_rcmc_r1.fq)
Jun-09 08:46:31.368 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:46:31.368 [Task submitter] INFO  nextflow.Session - [81/5f1b9b] Submitted process > EXTRACT_BWA_INDEX (small_rcmc_r1.fq)
Jun-09 08:46:31.373 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:46:31.374 [Task submitter] INFO  nextflow.Session - [5f/de9e5f] Submitted process > EXTRACT_BWA_INDEX (small_rcmc-extra-reads_r1.fq)
Jun-09 08:46:31.395 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 4; name: EXTRACT_BWA_INDEX (small_rcmc-extra-reads_r1.fq); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/e3/7990cbf3386e85072b0d3f32a8eb5e]
Jun-09 08:46:31.395 [Task monitor] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'TaskFinalizer' minSize=10; maxSize=30; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jun-09 08:46:31.427 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:46:31.428 [Task submitter] INFO  nextflow.Session - [29/b607c1] Submitted process > BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq)
Jun-09 08:46:31.438 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 2; name: EXTRACT_BWA_INDEX (small_rcmc_r1.fq); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/e5/dd3d3aaaf8d9f7c61d6bc2c6a47484]
Jun-09 08:46:31.439 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 1; name: EXTRACT_BWA_INDEX (small_rcmc_r1.fq); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/81/5f1b9b68837309e43d4b7feb0bd591]
Jun-09 08:46:31.445 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 3; name: EXTRACT_BWA_INDEX (small_rcmc-extra-reads_r1.fq); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/5f/de9e5fc21196be0f2a1346e0b06d08]
Jun-09 08:46:31.452 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:46:31.452 [Task submitter] INFO  nextflow.Session - [96/1006c1] Submitted process > BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq)
Jun-09 08:46:31.456 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:46:31.456 [Task submitter] INFO  nextflow.Session - [4f/386e24] Submitted process > BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq)
Jun-09 08:46:31.460 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:46:31.461 [Task submitter] INFO  nextflow.Session - [56/c822c2] Submitted process > BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq)
Jun-09 08:46:31.501 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 5; name: BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/29/b607c10f2c6e8213a8bc26d5517e03]
Jun-09 08:46:31.524 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 6; name: BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/96/1006c129ab5c6439f8fc6eccd37152]
Jun-09 08:46:31.529 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 7; name: BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/4f/386e24ce25b9d2649d93a383868a6c]
Jun-09 08:46:31.531 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 8; name: BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/56/c822c2d24677777ca4a1ee69643bb7]
Jun-09 08:46:31.539 [Actor Thread 5] DEBUG nextflow.processor.TaskProcessor - Process MERGE_DEDUP_SPLIT > collision check staging file names: [small_rcmc_r1.fq.pairsam.gz:2, small_rcmc-extra-reads_r1.fq.pairsam.gz:2]
Jun-09 08:46:31.542 [Actor Thread 5] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=MERGE_DEDUP_SPLIT; work-dir=null
  error [nextflow.exception.ProcessUnrecoverableException]: Process `MERGE_DEDUP_SPLIT` input file name collision -- There are multiple input files for each of the following file names: small_rcmc_r1.fq.pairsam.gz, small_rcmc-extra-reads_r1.fq.pairsam.gz
Jun-09 08:46:31.548 [Actor Thread 5] ERROR nextflow.processor.TaskProcessor - Error executing process > 'MERGE_DEDUP_SPLIT'

Caused by:
  Process `MERGE_DEDUP_SPLIT` input file name collision -- There are multiple input files for each of the following file names: small_rcmc_r1.fq.pairsam.gz, small_rcmc-extra-reads_r1.fq.pairsam.gz



Tip: when you have fixed the problem you can continue the execution adding the option `-resume` to the run command line
Jun-09 08:46:31.549 [Actor Thread 5] DEBUG nextflow.Session - Session aborted -- Cause: Process `MERGE_DEDUP_SPLIT` input file name collision -- There are multiple input files for each of the following file names: small_rcmc_r1.fq.pairsam.gz, small_rcmc-extra-reads_r1.fq.pairsam.gz
Jun-09 08:46:31.568 [Actor Thread 5] DEBUG nextflow.Session - The following nodes are still active:
[process] MERGE_DEDUP_SPLIT
  status=ACTIVE
  port 0: (value) bound ; channel: -
  port 1: (cntrl) -     ; channel: $

[process] COOLER_PROCESS
  status=ACTIVE
  port 0: (value) OPEN  ; channel: -
  port 1: (value) bound ; channel: chrom_sizes
  port 2: (cntrl) -     ; channel: $

[process] JUICER_HIC
  status=ACTIVE
  port 0: (value) OPEN  ; channel: -
  port 1: (value) bound ; channel: chrom_sizes
  port 2: (cntrl) -     ; channel: $

[process] QC_METRICS
  status=ACTIVE
  port 0: (value) OPEN  ; channel: -
  port 1: (cntrl) -     ; channel: $

Jun-09 08:46:31.571 [Actor Thread 5] ERROR nextflow.Nextflow - Pipeline execution stopped with the following message: null
Jun-09 08:46:31.572 [main] DEBUG nextflow.Session - Session await > all processes finished
Jun-09 08:46:31.572 [main] DEBUG nextflow.Session - Session await > all barriers passed
Jun-09 08:46:31.572 [Task monitor] DEBUG n.processor.TaskPollingMonitor - <<< barrier arrives (monitor: local) - terminating tasks monitor poll loop
Jun-09 08:46:31.573 [Actor Thread 6] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=MERGE_DEDUP_SPLIT; work-dir=null
  error [java.lang.InterruptedException]: java.lang.InterruptedException
Jun-09 08:46:31.574 [TaskFinalizer-3] ERROR nextflow.processor.PublishDir - Failed to publish file: /Users/<USER>/projects/topology-tools/work/81/5f1b9b68837309e43d4b7feb0bd591/genome_index; to: /Users/<USER>/projects/topology-tools/test_results/align/[:]/genome_index/genome_index [symlink] -- See log file for details
dev.failsafe.FailsafeException: java.lang.InterruptedException: sleep interrupted
	at dev.failsafe.FailsafeExecutor.call(FailsafeExecutor.java:444)
	at dev.failsafe.FailsafeExecutor.get(FailsafeExecutor.java:129)
	at nextflow.processor.PublishDir.retryableProcessFile(PublishDir.groovy:416)
	at nextflow.processor.PublishDir.safeProcessFile(PublishDir.groovy:387)
	at nextflow.processor.PublishDir.apply1(PublishDir.groovy:359)
	at nextflow.processor.PublishDir.apply0(PublishDir.groovy:266)
	at nextflow.processor.PublishDir.apply(PublishDir.groovy:324)
	at org.codehaus.groovy.vmplugin.v8.IndyInterface.fromCache(IndyInterface.java:321)
	at nextflow.processor.TaskProcessor.publishOutputs0(TaskProcessor.groovy:1420)
	at nextflow.processor.TaskProcessor.publishOutputs(TaskProcessor.groovy:1395)
	at org.codehaus.groovy.vmplugin.v8.IndyInterface.fromCache(IndyInterface.java:321)
	at nextflow.processor.TaskProcessor.finalizeTask0(TaskProcessor.groovy:2437)
	at org.codehaus.groovy.vmplugin.v8.IndyInterface.fromCache(IndyInterface.java:321)
	at nextflow.processor.TaskProcessor.finalizeTask(TaskProcessor.groovy:2408)
	at nextflow.processor.TaskPollingMonitor.finalizeTask(TaskPollingMonitor.groovy:696)
	at nextflow.processor.TaskPollingMonitor.safeFinalizeTask(TaskPollingMonitor.groovy:686)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:343)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:328)
	at groovy.lang.MetaClassImpl.doInvokeMethod(MetaClassImpl.java:1333)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1088)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1007)
	at org.codehaus.groovy.runtime.InvokerHelper.invokePogoMethod(InvokerHelper.java:645)
	at org.codehaus.groovy.runtime.InvokerHelper.invokeMethod(InvokerHelper.java:628)
	at org.codehaus.groovy.runtime.InvokerHelper.invokeMethodSafe(InvokerHelper.java:82)
	at nextflow.processor.TaskPollingMonitor$_checkTaskStatus_lambda8.doCall(TaskPollingMonitor.groovy:676)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1575)
Caused by: java.lang.InterruptedException: sleep interrupted
	at java.base/java.lang.Thread.sleepNanos0(Native Method)
	at java.base/java.lang.Thread.sleepNanos(Thread.java:496)
	at java.base/java.lang.Thread.sleep(Thread.java:527)
	at dev.failsafe.internal.RetryPolicyExecutor.lambda$apply$0(RetryPolicyExecutor.java:90)
	at dev.failsafe.SyncExecutionImpl.executeSync(SyncExecutionImpl.java:176)
	at dev.failsafe.FailsafeExecutor.call(FailsafeExecutor.java:437)
	... 31 common frames omitted
Jun-09 08:46:31.575 [main] INFO  nextflow.Nextflow - 
========================================================================================
                        Pipeline Execution Summary
========================================================================================
Completed at    : 2025-06-09T08:46:31.573692-04:00
Duration        : 1.1s
Success         : false
Work directory  : /Users/<USER>/projects/topology-tools/work
Exit status     : null
Error message   : None
========================================================================================

Jun-09 08:46:31.575 [main] ERROR nextflow.Nextflow - 💥 Pipeline failed!
Jun-09 08:46:31.576 [main] ERROR nextflow.Nextflow - Check the error message above and workflow logs for details.
Jun-09 08:46:31.584 [main] DEBUG n.trace.WorkflowStatsObserver - Workflow completed > WorkflowStats[succeededCount=8; failedCount=0; ignoredCount=0; cachedCount=0; pendingCount=0; submittedCount=0; runningCount=0; retriesCount=0; abortedCount=0; succeedDuration=380ms; failedDuration=0ms; cachedDuration=0ms;loadCpus=0; loadMemory=0; peakRunning=5; peakCpus=10; peakMemory=28 GB; ]
Jun-09 08:46:31.586 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow completed -- saving trace file
Jun-09 08:46:31.604 [main] DEBUG nextflow.trace.ReportObserver - Workflow completed -- rendering execution report
Jun-09 08:46:32.509 [main] DEBUG nextflow.trace.TimelineObserver - Workflow completed -- rendering execution timeline
Jun-09 08:46:32.612 [main] WARN  nextflow.dag.GraphvizRenderer - Graphviz is required to render the execution DAG in the given format -- See http://www.graphviz.org for more info.
Jun-09 08:46:32.615 [main] DEBUG nextflow.cache.CacheDB - Closing CacheDB done
Jun-09 08:46:32.626 [main] DEBUG nextflow.script.ScriptRunner - > Execution complete -- Goodbye
