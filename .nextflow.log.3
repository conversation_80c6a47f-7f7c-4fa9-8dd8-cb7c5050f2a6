Jun-09 09:01:32.216 [main] DEBUG nextflow.cli.Launcher - $> nextflow run nf/main.nf -profile test
Jun-09 09:01:32.282 [main] INFO  nextflow.cli.CmdRun - N E X T F L O W  ~  version 23.10.1
Jun-09 09:01:32.295 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/Users/<USER>/.nextflow/plugins; core-plugins: nf-amazon@2.1.4,nf-azure@1.3.3,nf-cloudcache@0.3.0,nf-codecommit@0.1.5,nf-console@1.0.6,nf-ga4gh@1.1.0,nf-google@1.8.3,nf-tower@1.6.3,nf-wave@1.0.1
Jun-09 09:01:32.302 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
Jun-09 09:01:32.302 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
Jun-09 09:01:32.306 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.4.1 in 'deployment' mode
Jun-09 09:01:32.314 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
Jun-09 09:01:32.323 [main] DEBUG nextflow.config.ConfigBuilder - Found config base: /Users/<USER>/projects/topology-tools/nf/nextflow.config
Jun-09 09:01:32.324 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /Users/<USER>/projects/topology-tools/nf/nextflow.config
Jun-09 09:01:32.339 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `test`
Jun-09 09:01:33.015 [main] DEBUG nextflow.config.ConfigBuilder - Available config profiles: [cluster, debug, test, local, docker, cloud, shifter, mamba, charliecloud, conda, singularity, arm, test_full, podman]
Jun-09 09:01:33.041 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declararion
Jun-09 09:01:33.056 [main] INFO  nextflow.cli.CmdRun - Launching `nf/main.nf` [exotic_neumann] DSL2 - revision: cb60860841
Jun-09 09:01:33.056 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
Jun-09 09:01:33.056 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[]
Jun-09 09:01:33.062 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /Users/<USER>/.nextflow/secrets/store.json
Jun-09 09:01:33.064 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@52ea0269] - activable => nextflow.secret.LocalSecretsProvider@52ea0269
Jun-09 09:01:33.105 [main] DEBUG nextflow.Session - Session UUID: 3a7eb0ac-5976-4dd3-b7b7-8997bb2fb690
Jun-09 09:01:33.105 [main] DEBUG nextflow.Session - Run name: exotic_neumann
Jun-09 09:01:33.106 [main] DEBUG nextflow.Session - Executor pool size: 10
Jun-09 09:01:33.111 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
Jun-09 09:01:33.114 [main] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'FileTransfer' minSize=10; maxSize=30; workQueue=LinkedBlockingQueue[10000]; allowCoreThreadTimeout=false
Jun-09 09:01:33.147 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 23.10.1 build 5891
  Created: 12-01-2024 22:01 UTC (17:01 EDT)
  System: Mac OS X 15.4.1
  Runtime: Groovy 3.0.19 on OpenJDK 64-Bit Server VM 11.0.26+4-LTS
  Encoding: UTF-8 (UTF-8)
  Process: <EMAIL> [127.0.0.1]
  CPUs: 10 - Mem: 64 GB (430.3 MB) - Swap: 7 GB (1.4 GB)
Jun-09 09:01:33.158 [main] DEBUG nextflow.Session - Work-dir: /Users/<USER>/projects/topology-tools/work [Mac OS X]
Jun-09 09:01:33.158 [main] DEBUG nextflow.Session - Script base path does not exist or is not a directory: /Users/<USER>/projects/topology-tools/nf/bin
Jun-09 09:01:33.166 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
Jun-09 09:01:33.173 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
Jun-09 09:01:33.200 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
Jun-09 09:01:33.207 [main] DEBUG nextflow.util.CustomThreadPool - Creating default thread pool > poolSize: 11; maxThreads: 1000
Jun-09 09:01:33.248 [main] DEBUG nextflow.Session - Session start
Jun-09 09:01:33.251 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow started -- trace file: /Users/<USER>/projects/topology-tools/test_results/pipeline_info/execution_trace_2025-06-09_09-01-33.txt
Jun-09 09:01:33.467 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
Jun-09 09:01:33.808 [main] INFO  nextflow.Nextflow - 
========================================================================================
                        Micro-C Pipeline - Nextflow DSL2
========================================================================================
Sample ID       : small-rcmc
FASTQ R1        : tests/small-region-capture-micro-c/small_rcmc_r1.fq.gz,tests/small-region-capture-micro-c/small_rcmc-extra-reads_r1.fq.gz
FASTQ R2        : tests/small-region-capture-micro-c/small_rcmc_r2.fq.gz,tests/small-region-capture-micro-c/small_rcmc-extra-reads_r2.fq.gz
BWA Index       : tests/small-region-capture-micro-c/test_bwa_index.tgz
Chrom Sizes     : tests/small-region-capture-micro-c/test.chrom.sizes
Output Dir      : ./test_results
Resolution      : 1000
BWA Cores       : 2
Min MAPQ        : 20
========================================================================================

Jun-09 09:01:33.905 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name EXTRACT_BWA_INDEX
Jun-09 09:01:33.906 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:EXTRACT_BWA_INDEX` matches process EXTRACT_BWA_INDEX
Jun-09 09:01:33.909 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 09:01:33.909 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 09:01:33.914 [main] DEBUG nextflow.executor.Executor - [warm up] executor > local
Jun-09 09:01:33.917 [main] DEBUG n.processor.LocalPollingMonitor - Creating local task monitor for executor 'local' > cpus=10; memory=64 GB; capacity=10; pollInterval=100ms; dumpInterval=5m
Jun-09 09:01:33.919 [main] DEBUG n.processor.TaskPollingMonitor - >>> barrier register (monitor: local)
Jun-09 09:01:33.961 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name BWA_ALIGN_PAIRTOOLS
Jun-09 09:01:33.963 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:BWA_ALIGN_PAIRTOOLS` matches process BWA_ALIGN_PAIRTOOLS
Jun-09 09:01:33.964 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 09:01:33.964 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 09:01:33.977 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name MERGE_DEDUP_SPLIT
Jun-09 09:01:33.977 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:MERGE_DEDUP_SPLIT` matches process MERGE_DEDUP_SPLIT
Jun-09 09:01:33.977 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 09:01:33.977 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 09:01:33.982 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name COOLER_PROCESS
Jun-09 09:01:33.982 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:COOLER_PROCESS` matches process COOLER_PROCESS
Jun-09 09:01:33.983 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 09:01:33.983 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 09:01:33.989 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name JUICER_HIC
Jun-09 09:01:33.989 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:JUICER_HIC` matches process JUICER_HIC
Jun-09 09:01:33.989 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 09:01:33.989 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 09:01:33.993 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name QC_METRICS
Jun-09 09:01:33.993 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:QC_METRICS` matches process QC_METRICS
Jun-09 09:01:33.994 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 09:01:33.994 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 09:01:33.996 [main] DEBUG nextflow.Session - Workflow process names [dsl2]: MERGE_DEDUP_SPLIT, COOLER_PROCESS, EXTRACT_BWA_INDEX, JUICER_HIC, BWA_ALIGN_PAIRTOOLS, QC_METRICS
Jun-09 09:01:33.997 [main] DEBUG nextflow.Session - Igniting dataflow network (9)
Jun-09 09:01:34.015 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > EXTRACT_BWA_INDEX
Jun-09 09:01:34.016 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > BWA_ALIGN_PAIRTOOLS
Jun-09 09:01:34.019 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > MERGE_DEDUP_SPLIT
Jun-09 09:01:34.019 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > COOLER_PROCESS
Jun-09 09:01:34.019 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > JUICER_HIC
Jun-09 09:01:34.019 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > QC_METRICS
Jun-09 09:01:34.020 [main] DEBUG nextflow.script.ScriptRunner - Parsed script files:
  Script_43f01a504ba15b5e: /Users/<USER>/projects/topology-tools/nf/./modules/merge_dedup_split.nf
  Script_6c968723c735dde5: /Users/<USER>/projects/topology-tools/nf/./modules/qc_metrics.nf
  Script_3c186acb8ea2eb2c: /Users/<USER>/projects/topology-tools/nf/./modules/juicer_hic.nf
  Script_900e8f290a0d3a82: /Users/<USER>/projects/topology-tools/nf/./modules/cooler_process.nf
  Script_59383c090845de85: /Users/<USER>/projects/topology-tools/nf/main.nf
  Script_f4e17a1e4235a98c: /Users/<USER>/projects/topology-tools/nf/./modules/extract_bwa_index.nf
  Script_e05012147db5f27a: /Users/<USER>/projects/topology-tools/nf/./modules/bwa_align_pairtools.nf
Jun-09 09:01:34.020 [main] DEBUG nextflow.script.ScriptRunner - > Awaiting termination 
Jun-09 09:01:34.020 [main] DEBUG nextflow.Session - Session await
Jun-09 09:01:34.122 [Task submitter] WARN  n.executor.BashWrapperBuilder - Task runtime metrics are not reported when using macOS without a container engine
Jun-09 09:01:34.129 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 09:01:34.131 [Task submitter] INFO  nextflow.Session - [2a/f20f26] Submitted process > EXTRACT_BWA_INDEX (small_rcmc-extra-reads_r1.fq)
Jun-09 09:01:34.136 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 09:01:34.136 [Task submitter] INFO  nextflow.Session - [28/524e7f] Submitted process > EXTRACT_BWA_INDEX (small_rcmc_r1.fq)
Jun-09 09:01:34.219 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 2; name: EXTRACT_BWA_INDEX (small_rcmc-extra-reads_r1.fq); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/2a/f20f268149949ad0a6f98263d5a2dd]
Jun-09 09:01:34.255 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 1; name: EXTRACT_BWA_INDEX (small_rcmc_r1.fq); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/28/524e7ff59689434163d69a241aba92]
Jun-09 09:01:34.257 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 09:01:34.257 [Task submitter] INFO  nextflow.Session - [63/c3613a] Submitted process > BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq)
Jun-09 09:01:34.265 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 09:01:34.265 [Task submitter] INFO  nextflow.Session - [e1/4f8cfa] Submitted process > BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq)
Jun-09 09:01:36.708 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 4; name: BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/e1/4f8cfa26931b7674e3d703f53b49e3]
Jun-09 09:01:43.647 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 3; name: BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/63/c3613afb80962d538f58b507bad311]
Jun-09 09:01:43.659 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 09:01:43.659 [Task submitter] INFO  nextflow.Session - [66/d824a2] Submitted process > MERGE_DEDUP_SPLIT (small-rcmc)
Jun-09 09:01:47.539 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 5; name: MERGE_DEDUP_SPLIT (small-rcmc); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/66/d824a225e061c504c812e58c154a4b]
Jun-09 09:01:47.549 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 09:01:47.549 [Task submitter] INFO  nextflow.Session - [da/1973d0] Submitted process > QC_METRICS (small-rcmc)
Jun-09 09:01:47.555 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 09:01:47.556 [Task submitter] INFO  nextflow.Session - [74/5ea2ad] Submitted process > JUICER_HIC (small-rcmc)
Jun-09 09:01:47.558 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 09:01:47.558 [Task submitter] INFO  nextflow.Session - [12/e5d609] Submitted process > COOLER_PROCESS (small-rcmc)
Jun-09 09:01:47.647 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 7; name: QC_METRICS (small-rcmc); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/da/1973d0190bf0fe5d415d75d2288151]
Jun-09 09:01:49.748 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 6; name: JUICER_HIC (small-rcmc); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/74/5ea2ad5afb194f00c293e284dd7065]
Jun-09 09:01:49.750 [Task monitor] DEBUG nextflow.processor.TaskProcessor - Process `JUICER_HIC (small-rcmc)` is unable to find [UnixPath]: `/Users/<USER>/projects/topology-tools/work/74/5ea2ad5afb194f00c293e284dd7065/versions.yml` (pattern: `versions.yml`)
Jun-09 09:01:49.751 [Task monitor] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=JUICER_HIC (small-rcmc); work-dir=/Users/<USER>/projects/topology-tools/work/74/5ea2ad5afb194f00c293e284dd7065
  error [nextflow.exception.MissingFileException]: Missing output file(s) `versions.yml` expected by process `JUICER_HIC (small-rcmc)`
Jun-09 09:01:49.753 [Task monitor] INFO  nextflow.processor.TaskProcessor - [74/5ea2ad] NOTE: Missing output file(s) `versions.yml` expected by process `JUICER_HIC (small-rcmc)` -- Error is ignored
Jun-09 09:01:53.738 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 8; name: COOLER_PROCESS (small-rcmc); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/12/e5d609f6584ed6de1a94d667d628bc]
Jun-09 09:01:53.741 [main] DEBUG nextflow.Session - Session await > all processes finished
Jun-09 09:01:53.742 [Task monitor] DEBUG n.processor.TaskPollingMonitor - <<< barrier arrives (monitor: local) - terminating tasks monitor poll loop
Jun-09 09:01:53.742 [main] DEBUG nextflow.Session - Session await > all barriers passed
Jun-09 09:01:53.743 [main] INFO  nextflow.Nextflow - 
========================================================================================
                        Pipeline Execution Summary
========================================================================================
Completed at    : 2025-06-09T09:01:53.742622-04:00
Duration        : 20.6s
Success         : true
Work directory  : /Users/<USER>/projects/topology-tools/work
Exit status     : 0
Error message   : None
========================================================================================

Jun-09 09:01:53.744 [main] INFO  nextflow.Nextflow - 🎉 Pipeline completed successfully!
Jun-09 09:01:53.744 [main] INFO  nextflow.Nextflow - 📁 Results are in: ./test_results
Jun-09 09:01:53.746 [main] DEBUG n.trace.WorkflowStatsObserver - Workflow completed > WorkflowStats[succeededCount=7; failedCount=1; ignoredCount=1; cachedCount=0; pendingCount=0; submittedCount=0; runningCount=0; retriesCount=0; abortedCount=0; succeedDuration=43.8s; failedDuration=4.3s; cachedDuration=0ms;loadCpus=0; loadMemory=0; peakRunning=3; peakCpus=6; peakMemory=16 GB; ]
Jun-09 09:01:53.746 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow completed -- saving trace file
Jun-09 09:01:53.747 [main] DEBUG nextflow.trace.ReportObserver - Workflow completed -- rendering execution report
Jun-09 09:01:54.641 [main] DEBUG nextflow.trace.TimelineObserver - Workflow completed -- rendering execution timeline
Jun-09 09:01:54.745 [main] WARN  nextflow.dag.GraphvizRenderer - Graphviz is required to render the execution DAG in the given format -- See http://www.graphviz.org for more info.
Jun-09 09:01:54.796 [main] DEBUG nextflow.cache.CacheDB - Closing CacheDB done
Jun-09 09:01:54.806 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'FileTransfer' shutdown completed (hard=false)
Jun-09 09:01:54.807 [main] DEBUG nextflow.script.ScriptRunner - > Execution complete -- Goodbye
