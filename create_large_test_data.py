#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create larger synthetic test datasets by concatenating multiple copies
of the small test data files for performance testing.
"""

import os
import shutil
import gzip
import argparse
from pathlib import Path

def concatenate_fastq_files(input_files, output_file, num_copies=20):
    """Concatenate multiple copies of FASTQ files"""
    print(f"Creating {output_file} with {num_copies} copies of each input file...")
    
    with gzip.open(output_file, 'wt') as outf:
        for copy_num in range(num_copies):
            for input_file in input_files:
                print(f"  Processing copy {copy_num + 1}, file {input_file}")
                with gzip.open(input_file, 'rt') as inf:
                    for line_num, line in enumerate(inf):
                        if line.startswith('@'):
                            # Modify read ID to make it unique across copies
                            parts = line.strip().split()
                            read_id = parts[0] + f"_copy{copy_num}"
                            if len(parts) > 1:
                                line = read_id + " " + " ".join(parts[1:]) + "\n"
                            else:
                                line = read_id + "\n"
                        outf.write(line)

def create_large_test_dataset(base_dir, output_dir, num_copies=20):
    """Create large test dataset"""
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Input files
    small_files_r1 = [
        os.path.join(base_dir, "small_rcmc_r1.fq.gz"),
        os.path.join(base_dir, "small_rcmc-extra-reads_r1.fq.gz")
    ]
    
    small_files_r2 = [
        os.path.join(base_dir, "small_rcmc_r2.fq.gz"),
        os.path.join(base_dir, "small_rcmc-extra-reads_r2.fq.gz")
    ]
    
    # Output files
    large_r1 = os.path.join(output_dir, f"large_rcmc_{num_copies}x_r1.fq.gz")
    large_r2 = os.path.join(output_dir, f"large_rcmc_{num_copies}x_r2.fq.gz")
    
    # Create concatenated files
    concatenate_fastq_files(small_files_r1, large_r1, num_copies)
    concatenate_fastq_files(small_files_r2, large_r2, num_copies)
    
    # Copy other necessary files
    for filename in ["test_bwa_index.tgz", "test.chrom.sizes", "small_rcmc.json"]:
        src = os.path.join(base_dir, filename)
        dst = os.path.join(output_dir, filename)
        if os.path.exists(src):
            shutil.copy2(src, dst)
            print(f"Copied {filename}")
    
    # Get file sizes
    r1_size = os.path.getsize(large_r1) / (1024**3)  # GB
    r2_size = os.path.getsize(large_r2) / (1024**3)  # GB
    
    print(f"\nLarge test dataset created:")
    print(f"  R1 file: {large_r1} ({r1_size:.2f} GB)")
    print(f"  R2 file: {large_r2} ({r2_size:.2f} GB)")
    print(f"  Total size: {r1_size + r2_size:.2f} GB")
    
    return large_r1, large_r2

def main():
    parser = argparse.ArgumentParser(description="Create large test datasets")
    parser.add_argument("--base_dir", default="tests/small-region-capture-micro-c",
                       help="Directory containing small test files")
    parser.add_argument("--output_dir", default="tests/large-test-data",
                       help="Output directory for large test files")
    parser.add_argument("--num_copies", type=int, default=20,
                       help="Number of copies to concatenate")
    
    args = parser.parse_args()
    
    create_large_test_dataset(args.base_dir, args.output_dir, args.num_copies)

if __name__ == "__main__":
    main()
