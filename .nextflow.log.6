Jun-09 08:48:08.406 [main] DEBUG nextflow.cli.Launcher - $> nextflow run nf/main.nf -profile test -stub-run
Jun-09 08:48:08.466 [main] DEBUG nextflow.cli.CmdRun - N E X T F L O W  ~  version 25.04.3
Jun-09 08:48:08.491 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/Users/<USER>/.nextflow/plugins; core-plugins: nf-amazon@2.15.0,nf-azure@1.16.0,nf-cloudcache@0.4.3,nf-codecommit@0.2.3,nf-console@1.2.1,nf-google@1.21.0,nf-k8s@1.0.0,nf-tower@1.11.3,nf-wave@1.12.1
Jun-09 08:48:08.517 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
Jun-09 08:48:08.518 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
Jun-09 08:48:08.520 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.12.0 in 'deployment' mode
Jun-09 08:48:08.530 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
Jun-09 08:48:08.545 [main] DEBUG nextflow.config.ConfigBuilder - Found config base: /Users/<USER>/projects/topology-tools/nf/nextflow.config
Jun-09 08:48:08.548 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /Users/<USER>/projects/topology-tools/nf/nextflow.config
Jun-09 08:48:08.578 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /Users/<USER>/.nextflow/secrets/store.json
Jun-09 08:48:08.581 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@7cedfa63] - activable => nextflow.secret.LocalSecretsProvider@7cedfa63
Jun-09 08:48:08.585 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `test`
Jun-09 08:48:09.196 [main] DEBUG nextflow.config.ConfigBuilder - Available config profiles: [cluster, debug, test, local, docker, cloud, shifter, mamba, charliecloud, conda, singularity, arm, test_full, podman]
Jun-09 08:48:09.216 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declaration
Jun-09 08:48:09.228 [main] DEBUG nextflow.cli.CmdRun - Launching `nf/main.nf` [nostalgic_ekeblad] DSL2 - revision: cb60860841
Jun-09 08:48:09.229 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
Jun-09 08:48:09.229 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[]
Jun-09 08:48:09.265 [main] DEBUG nextflow.Session - Session UUID: e5aaa852-a770-4009-a442-be1302948275
Jun-09 08:48:09.265 [main] DEBUG nextflow.Session - Run name: nostalgic_ekeblad
Jun-09 08:48:09.265 [main] DEBUG nextflow.Session - Executor pool size: 10
Jun-09 08:48:09.270 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
Jun-09 08:48:09.273 [main] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'FileTransfer' minSize=10; maxSize=30; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jun-09 08:48:09.289 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 25.04.3 build 5949
  Created: 02-06-2025 20:56 UTC (16:56 EDT)
  System: Mac OS X 15.4.1
  Runtime: Groovy 4.0.26 on OpenJDK 64-Bit Server VM 23.0.2+7
  Encoding: UTF-8 (UTF-8)
  Process: <EMAIL> [127.0.0.1]
  CPUs: 10 - Mem: 64 GB (1009.4 MB) - Swap: 7 GB (1.4 GB)
Jun-09 08:48:09.299 [main] DEBUG nextflow.Session - Work-dir: /Users/<USER>/projects/topology-tools/work [Mac OS X]
Jun-09 08:48:09.299 [main] DEBUG nextflow.Session - Script base path does not exist or is not a directory: /Users/<USER>/projects/topology-tools/nf/bin
Jun-09 08:48:09.307 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
Jun-09 08:48:09.312 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
Jun-09 08:48:09.329 [main] DEBUG nextflow.Session - Observer factory (v2): LinObserverFactory
Jun-09 08:48:09.354 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
Jun-09 08:48:09.363 [main] DEBUG nextflow.util.CustomThreadPool - Creating default thread pool > poolSize: 11; maxThreads: 1000
Jun-09 08:48:09.403 [main] DEBUG nextflow.Session - Session start
Jun-09 08:48:09.406 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow started -- trace file: /Users/<USER>/projects/topology-tools/test_results/pipeline_info/execution_trace_2025-06-09_08-48-09.txt
Jun-09 08:48:09.580 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
Jun-09 08:48:09.896 [main] INFO  nextflow.Nextflow - 
========================================================================================
                        Micro-C Pipeline - Nextflow DSL2
========================================================================================
Sample ID       : small-rcmc
FASTQ R1        : tests/small-region-capture-micro-c/small_rcmc_r1.fq.gz,tests/small-region-capture-micro-c/small_rcmc-extra-reads_r1.fq.gz
FASTQ R2        : tests/small-region-capture-micro-c/small_rcmc_r2.fq.gz,tests/small-region-capture-micro-c/small_rcmc-extra-reads_r2.fq.gz
BWA Index       : tests/small-region-capture-micro-c/test_bwa_index.tgz
Chrom Sizes     : tests/small-region-capture-micro-c/test.chrom.sizes
Output Dir      : ./test_results
Resolution      : 1000
BWA Cores       : 2
Min MAPQ        : 20
========================================================================================

Jun-09 08:48:09.992 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name EXTRACT_BWA_INDEX
Jun-09 08:48:09.994 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:EXTRACT_BWA_INDEX` matches process EXTRACT_BWA_INDEX
Jun-09 08:48:10.004 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:48:10.004 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:48:10.008 [main] DEBUG nextflow.executor.Executor - [warm up] executor > local
Jun-09 08:48:10.012 [main] DEBUG n.processor.LocalPollingMonitor - Creating local task monitor for executor 'local' > cpus=10; memory=64 GB; capacity=10; pollInterval=100ms; dumpInterval=5m
Jun-09 08:48:10.014 [main] DEBUG n.processor.TaskPollingMonitor - >>> barrier register (monitor: local)
Jun-09 08:48:10.029 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'EXTRACT_BWA_INDEX': maxForks=0; fair=false; array=0
Jun-09 08:48:10.054 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name BWA_ALIGN_PAIRTOOLS
Jun-09 08:48:10.054 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:BWA_ALIGN_PAIRTOOLS` matches process BWA_ALIGN_PAIRTOOLS
Jun-09 08:48:10.055 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:48:10.056 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:48:10.056 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'BWA_ALIGN_PAIRTOOLS': maxForks=0; fair=false; array=0
Jun-09 08:48:10.069 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name MERGE_DEDUP_SPLIT
Jun-09 08:48:10.069 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:MERGE_DEDUP_SPLIT` matches process MERGE_DEDUP_SPLIT
Jun-09 08:48:10.071 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:48:10.071 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:48:10.071 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'MERGE_DEDUP_SPLIT': maxForks=0; fair=false; array=0
Jun-09 08:48:10.077 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name COOLER_PROCESS
Jun-09 08:48:10.077 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:COOLER_PROCESS` matches process COOLER_PROCESS
Jun-09 08:48:10.079 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:48:10.079 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:48:10.079 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'COOLER_PROCESS': maxForks=0; fair=false; array=0
Jun-09 08:48:10.087 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name JUICER_HIC
Jun-09 08:48:10.087 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:JUICER_HIC` matches process JUICER_HIC
Jun-09 08:48:10.088 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:48:10.088 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:48:10.089 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'JUICER_HIC': maxForks=0; fair=false; array=0
Jun-09 08:48:10.094 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name QC_METRICS
Jun-09 08:48:10.094 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:QC_METRICS` matches process QC_METRICS
Jun-09 08:48:10.095 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:48:10.095 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:48:10.096 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'QC_METRICS': maxForks=0; fair=false; array=0
Jun-09 08:48:10.098 [main] DEBUG nextflow.Session - Workflow process names [dsl2]: MERGE_DEDUP_SPLIT, COOLER_PROCESS, EXTRACT_BWA_INDEX, JUICER_HIC, BWA_ALIGN_PAIRTOOLS, QC_METRICS
Jun-09 08:48:10.099 [main] DEBUG nextflow.Session - Igniting dataflow network (9)
Jun-09 08:48:10.104 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > EXTRACT_BWA_INDEX
Jun-09 08:48:10.104 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > BWA_ALIGN_PAIRTOOLS
Jun-09 08:48:10.104 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > MERGE_DEDUP_SPLIT
Jun-09 08:48:10.104 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > COOLER_PROCESS
Jun-09 08:48:10.104 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > JUICER_HIC
Jun-09 08:48:10.104 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > QC_METRICS
Jun-09 08:48:10.105 [main] DEBUG nextflow.script.ScriptRunner - Parsed script files:
  Script_43f01a504ba15b5e: /Users/<USER>/projects/topology-tools/nf/modules/merge_dedup_split.nf
  Script_6c968723c735dde5: /Users/<USER>/projects/topology-tools/nf/modules/qc_metrics.nf
  Script_a457b5df1e6a90ec: /Users/<USER>/projects/topology-tools/nf/modules/bwa_align_pairtools.nf
  Script_3c186acb8ea2eb2c: /Users/<USER>/projects/topology-tools/nf/modules/juicer_hic.nf
  Script_900e8f290a0d3a82: /Users/<USER>/projects/topology-tools/nf/modules/cooler_process.nf
  Script_59383c090845de85: /Users/<USER>/projects/topology-tools/nf/main.nf
  Script_f4e17a1e4235a98c: /Users/<USER>/projects/topology-tools/nf/modules/extract_bwa_index.nf
Jun-09 08:48:10.105 [main] DEBUG nextflow.script.ScriptRunner - > Awaiting termination 
Jun-09 08:48:10.105 [main] DEBUG nextflow.Session - Session await
Jun-09 08:48:10.200 [Task submitter] WARN  n.executor.BashWrapperBuilder - Task runtime metrics are not reported when using macOS without a container engine
Jun-09 08:48:10.210 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:48:10.212 [Task submitter] INFO  nextflow.Session - [c5/3ba4ca] Submitted process > EXTRACT_BWA_INDEX (small_rcmc-extra-reads_r1.fq)
Jun-09 08:48:10.218 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:48:10.219 [Task submitter] INFO  nextflow.Session - [c5/9f78e7] Submitted process > EXTRACT_BWA_INDEX (small_rcmc_r1.fq)
Jun-09 08:48:10.305 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 2; name: EXTRACT_BWA_INDEX (small_rcmc-extra-reads_r1.fq); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/c5/3ba4ca4258b90a8bc4556e14c0d620]
Jun-09 08:48:10.306 [Task monitor] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'TaskFinalizer' minSize=10; maxSize=30; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jun-09 08:48:10.311 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 1; name: EXTRACT_BWA_INDEX (small_rcmc_r1.fq); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/c5/9f78e7827bcf69597e8aca036ff1fe]
Jun-09 08:48:10.337 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:48:10.338 [Task submitter] INFO  nextflow.Session - [2b/c26573] Submitted process > BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq)
Jun-09 08:48:10.341 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:48:10.342 [Task submitter] INFO  nextflow.Session - [f0/1aa16f] Submitted process > BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq)
Jun-09 08:48:10.405 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 3; name: BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/2b/c265732582bc64420995d6d1bb2f96]
Jun-09 08:48:10.406 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 4; name: BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/f0/1aa16f7328545ba8a5877dc9c3ec03]
Jun-09 08:48:10.431 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:48:10.432 [Task submitter] INFO  nextflow.Session - [d1/131582] Submitted process > MERGE_DEDUP_SPLIT (small-rcmc)
Jun-09 08:48:10.491 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 5; name: MERGE_DEDUP_SPLIT (small-rcmc); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/d1/1315827748002ab3ba5188645fe71e]
Jun-09 08:48:10.507 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:48:10.507 [Task submitter] INFO  nextflow.Session - [0f/e829cd] Submitted process > QC_METRICS (small-rcmc)
Jun-09 08:48:10.509 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:48:10.510 [Task submitter] INFO  nextflow.Session - [15/0c04dd] Submitted process > JUICER_HIC (small-rcmc)
Jun-09 08:48:10.513 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:48:10.513 [Task submitter] INFO  nextflow.Session - [4e/6d54e8] Submitted process > COOLER_PROCESS (small-rcmc)
Jun-09 08:48:10.554 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 8; name: QC_METRICS (small-rcmc); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/0f/e829cd6d02ce3afdf32c3cad6738a8]
Jun-09 08:48:10.565 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 6; name: JUICER_HIC (small-rcmc); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/15/0c04dd6270bce8c28dc424b7d6cc07]
Jun-09 08:48:10.569 [TaskFinalizer-7] DEBUG nextflow.processor.TaskProcessor - Process `JUICER_HIC (small-rcmc)` is unable to find [UnixPath]: `/Users/<USER>/projects/topology-tools/work/15/0c04dd6270bce8c28dc424b7d6cc07/juicer_error.log` (pattern: `juicer_error.log`)
Jun-09 08:48:10.570 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 7; name: COOLER_PROCESS (small-rcmc); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/4e/6d54e8ae082a47ca1e3dd9676fdf5b]
Jun-09 08:48:10.656 [TaskFinalizer-7] DEBUG nextflow.processor.TaskProcessor - Process JUICER_HIC > Skipping output binding because one or more optional files are missing: fileoutparam<3>
Jun-09 08:48:10.673 [TaskFinalizer-1] DEBUG nextflow.processor.PublishDir - Failed to publish file: /Users/<USER>/projects/topology-tools/work/c5/3ba4ca4258b90a8bc4556e14c0d620/genome_index; to: /Users/<USER>/projects/topology-tools/test_results/align/[:]/genome_index/genome_index [symlink] -- attempt: 1; reason: /Users/<USER>/projects/topology-tools/test_results/align/[:]/genome_index/genome_index
Jun-09 08:48:10.674 [main] DEBUG nextflow.Session - Session await > all processes finished
Jun-09 08:48:10.675 [Task monitor] DEBUG n.processor.TaskPollingMonitor - <<< barrier arrives (monitor: local) - terminating tasks monitor poll loop
Jun-09 08:48:10.676 [main] DEBUG nextflow.Session - Session await > all barriers passed
Jun-09 08:48:10.678 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'TaskFinalizer' shutdown completed (hard=false)
Jun-09 08:48:10.680 [main] INFO  nextflow.Nextflow - 
========================================================================================
                        Pipeline Execution Summary
========================================================================================
Completed at    : 2025-06-09T08:48:10.678909-04:00
Duration        : 1.3s
Success         : true
Work directory  : /Users/<USER>/projects/topology-tools/work
Exit status     : 0
Error message   : None
========================================================================================

Jun-09 08:48:10.680 [main] INFO  nextflow.Nextflow - 🎉 Pipeline completed successfully!
Jun-09 08:48:10.680 [main] INFO  nextflow.Nextflow - 📁 Results are in: ./test_results
Jun-09 08:48:10.683 [main] DEBUG n.trace.WorkflowStatsObserver - Workflow completed > WorkflowStats[succeededCount=8; failedCount=0; ignoredCount=0; cachedCount=0; pendingCount=0; submittedCount=0; runningCount=0; retriesCount=0; abortedCount=0; succeedDuration=398ms; failedDuration=0ms; cachedDuration=0ms;loadCpus=0; loadMemory=0; peakRunning=4; peakCpus=8; peakMemory=20 GB; ]
Jun-09 08:48:10.684 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow completed -- saving trace file
Jun-09 08:48:10.685 [main] DEBUG nextflow.trace.ReportObserver - Workflow completed -- rendering execution report
Jun-09 08:48:11.555 [main] DEBUG nextflow.trace.TimelineObserver - Workflow completed -- rendering execution timeline
Jun-09 08:48:11.656 [main] WARN  nextflow.dag.GraphvizRenderer - Graphviz is required to render the execution DAG in the given format -- See http://www.graphviz.org for more info.
Jun-09 08:48:11.708 [main] DEBUG nextflow.cache.CacheDB - Closing CacheDB done
Jun-09 08:48:11.718 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'FileTransfer' shutdown completed (hard=false)
Jun-09 08:48:11.718 [main] DEBUG nextflow.script.ScriptRunner - > Execution complete -- Goodbye
