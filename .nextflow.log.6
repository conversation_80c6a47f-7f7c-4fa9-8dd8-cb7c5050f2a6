Jun-09 08:11:32.953 [main] DEBUG nextflow.cli.Launcher - $> nextflow run nf/main.nf -profile test -stub-run
Jun-09 08:11:33.018 [main] DEBUG nextflow.cli.CmdRun - N E X T F L O W  ~  version 25.04.3
Jun-09 08:11:33.046 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/Users/<USER>/.nextflow/plugins; core-plugins: nf-amazon@2.15.0,nf-azure@1.16.0,nf-cloudcache@0.4.3,nf-codecommit@0.2.3,nf-console@1.2.1,nf-google@1.21.0,nf-k8s@1.0.0,nf-tower@1.11.3,nf-wave@1.12.1
Jun-09 08:11:33.076 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
Jun-09 08:11:33.077 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
Jun-09 08:11:33.080 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.12.0 in 'deployment' mode
Jun-09 08:11:33.090 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
Jun-09 08:11:33.110 [main] DEBUG nextflow.config.ConfigBuilder - Found config base: /Users/<USER>/projects/topology-tools/nf/nextflow.config
Jun-09 08:11:33.113 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /Users/<USER>/projects/topology-tools/nf/nextflow.config
Jun-09 08:11:33.141 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /Users/<USER>/.nextflow/secrets/store.json
Jun-09 08:11:33.144 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@7cedfa63] - activable => nextflow.secret.LocalSecretsProvider@7cedfa63
Jun-09 08:11:33.149 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `test`
Jun-09 08:11:33.806 [main] DEBUG nextflow.config.ConfigBuilder - Available config profiles: [cluster, debug, test, local, docker, cloud, shifter, mamba, charliecloud, conda, singularity, arm, test_full, podman]
Jun-09 08:11:33.827 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declaration
Jun-09 08:11:33.838 [main] DEBUG nextflow.cli.CmdRun - Launching `nf/main.nf` [big_meitner] DSL2 - revision: e10359e183
Jun-09 08:11:33.840 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
Jun-09 08:11:33.840 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[]
Jun-09 08:11:33.878 [main] DEBUG nextflow.Session - Session UUID: f4c47009-5ade-4c68-934e-29fa5d8019fc
Jun-09 08:11:33.878 [main] DEBUG nextflow.Session - Run name: big_meitner
Jun-09 08:11:33.878 [main] DEBUG nextflow.Session - Executor pool size: 10
Jun-09 08:11:33.884 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
Jun-09 08:11:33.888 [main] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'FileTransfer' minSize=10; maxSize=30; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jun-09 08:11:33.905 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 25.04.3 build 5949
  Created: 02-06-2025 20:56 UTC (16:56 EDT)
  System: Mac OS X 15.4.1
  Runtime: Groovy 4.0.26 on OpenJDK 64-Bit Server VM 23.0.2+7
  Encoding: UTF-8 (UTF-8)
  Process: <EMAIL> [127.0.0.1]
  CPUs: 10 - Mem: 64 GB (984.6 MB) - Swap: 7 GB (1.4 GB)
Jun-09 08:11:33.915 [main] DEBUG nextflow.Session - Work-dir: /Users/<USER>/projects/topology-tools/work [Mac OS X]
Jun-09 08:11:33.916 [main] DEBUG nextflow.Session - Script base path does not exist or is not a directory: /Users/<USER>/projects/topology-tools/nf/bin
Jun-09 08:11:33.923 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
Jun-09 08:11:33.929 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
Jun-09 08:11:33.949 [main] DEBUG nextflow.Session - Observer factory (v2): LinObserverFactory
Jun-09 08:11:33.978 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
Jun-09 08:11:33.985 [main] DEBUG nextflow.util.CustomThreadPool - Creating default thread pool > poolSize: 11; maxThreads: 1000
Jun-09 08:11:34.024 [main] DEBUG nextflow.Session - Session start
Jun-09 08:11:34.027 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow started -- trace file: /Users/<USER>/projects/topology-tools/test_results/pipeline_info/execution_trace_2025-06-09_08-11-33.txt
Jun-09 08:11:34.194 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
Jun-09 08:11:34.545 [main] INFO  nextflow.Nextflow - 
========================================================================================
                        Micro-C Pipeline - Nextflow DSL2
========================================================================================
Sample ID       : test-sample
FASTQ R1        : tests/small-region-capture-micro-c/small_rcmc_r1.fq.gz
FASTQ R2        : tests/small-region-capture-micro-c/small_rcmc_r2.fq.gz
BWA Index       : tests/small-region-capture-micro-c/test_bwa_index.tgz
Chrom Sizes     : tests/small-region-capture-micro-c/test.chrom.sizes
Output Dir      : ./test_results
Resolution      : 1000
BWA Cores       : 2
Min MAPQ        : 20
========================================================================================

Jun-09 08:11:34.654 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name EXTRACT_BWA_INDEX
Jun-09 08:11:34.656 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:EXTRACT_BWA_INDEX` matches process EXTRACT_BWA_INDEX
Jun-09 08:11:34.666 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:11:34.666 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:11:34.671 [main] DEBUG nextflow.executor.Executor - [warm up] executor > local
Jun-09 08:11:34.676 [main] DEBUG n.processor.LocalPollingMonitor - Creating local task monitor for executor 'local' > cpus=10; memory=64 GB; capacity=10; pollInterval=100ms; dumpInterval=5m
Jun-09 08:11:34.678 [main] DEBUG n.processor.TaskPollingMonitor - >>> barrier register (monitor: local)
Jun-09 08:11:34.692 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'EXTRACT_BWA_INDEX': maxForks=0; fair=false; array=0
Jun-09 08:11:34.718 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name BWA_ALIGN_PAIRTOOLS
Jun-09 08:11:34.719 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:BWA_ALIGN_PAIRTOOLS` matches process BWA_ALIGN_PAIRTOOLS
Jun-09 08:11:34.720 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:11:34.720 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:11:34.721 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'BWA_ALIGN_PAIRTOOLS': maxForks=0; fair=false; array=0
Jun-09 08:11:34.733 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name MERGE_DEDUP_SPLIT
Jun-09 08:11:34.733 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:MERGE_DEDUP_SPLIT` matches process MERGE_DEDUP_SPLIT
Jun-09 08:11:34.735 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:11:34.735 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:11:34.736 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'MERGE_DEDUP_SPLIT': maxForks=0; fair=false; array=0
Jun-09 08:11:34.744 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name COOLER_PROCESS
Jun-09 08:11:34.744 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:COOLER_PROCESS` matches process COOLER_PROCESS
Jun-09 08:11:34.745 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:11:34.745 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:11:34.746 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'COOLER_PROCESS': maxForks=0; fair=false; array=0
Jun-09 08:11:34.755 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name JUICER_HIC
Jun-09 08:11:34.755 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:JUICER_HIC` matches process JUICER_HIC
Jun-09 08:11:34.757 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:11:34.757 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:11:34.758 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'JUICER_HIC': maxForks=0; fair=false; array=0
Jun-09 08:11:34.763 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name QC_METRICS
Jun-09 08:11:34.764 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:QC_METRICS` matches process QC_METRICS
Jun-09 08:11:34.765 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:11:34.765 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:11:34.766 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'QC_METRICS': maxForks=0; fair=false; array=0
Jun-09 08:11:34.769 [main] DEBUG nextflow.Session - Workflow process names [dsl2]: MERGE_DEDUP_SPLIT, COOLER_PROCESS, EXTRACT_BWA_INDEX, JUICER_HIC, BWA_ALIGN_PAIRTOOLS, QC_METRICS
Jun-09 08:11:34.771 [main] DEBUG nextflow.Session - Igniting dataflow network (10)
Jun-09 08:11:34.779 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > EXTRACT_BWA_INDEX
Jun-09 08:11:34.779 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > BWA_ALIGN_PAIRTOOLS
Jun-09 08:11:34.779 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > MERGE_DEDUP_SPLIT
Jun-09 08:11:34.779 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > COOLER_PROCESS
Jun-09 08:11:34.779 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > JUICER_HIC
Jun-09 08:11:34.779 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > QC_METRICS
Jun-09 08:11:34.779 [main] DEBUG nextflow.script.ScriptRunner - Parsed script files:
  Script_43f01a504ba15b5e: /Users/<USER>/projects/topology-tools/nf/modules/merge_dedup_split.nf
  Script_6c968723c735dde5: /Users/<USER>/projects/topology-tools/nf/modules/qc_metrics.nf
  Script_fb09b2496743dbcc: /Users/<USER>/projects/topology-tools/nf/main.nf
  Script_a457b5df1e6a90ec: /Users/<USER>/projects/topology-tools/nf/modules/bwa_align_pairtools.nf
  Script_3c186acb8ea2eb2c: /Users/<USER>/projects/topology-tools/nf/modules/juicer_hic.nf
  Script_900e8f290a0d3a82: /Users/<USER>/projects/topology-tools/nf/modules/cooler_process.nf
  Script_f4e17a1e4235a98c: /Users/<USER>/projects/topology-tools/nf/modules/extract_bwa_index.nf
Jun-09 08:11:34.780 [main] DEBUG nextflow.script.ScriptRunner - > Awaiting termination 
Jun-09 08:11:34.780 [main] DEBUG nextflow.Session - Session await
Jun-09 08:11:34.886 [Task submitter] WARN  n.executor.BashWrapperBuilder - Task runtime metrics are not reported when using macOS without a container engine
Jun-09 08:11:34.904 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:11:34.928 [Task submitter] INFO  nextflow.Session - [cd/d2f379] Submitted process > EXTRACT_BWA_INDEX (small_rcmc_r1.fq)
Jun-09 08:11:35.002 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 1; name: EXTRACT_BWA_INDEX (small_rcmc_r1.fq); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/cd/d2f37986a870fdf928add5a68bd83e]
Jun-09 08:11:35.003 [Task monitor] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'TaskFinalizer' minSize=10; maxSize=30; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jun-09 08:11:35.035 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:11:35.036 [Task submitter] INFO  nextflow.Session - [7a/acfdb9] Submitted process > BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq)
Jun-09 08:11:35.115 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 2; name: BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/7a/acfdb990c541b88061fb7bb8bb5d1a]
Jun-09 08:11:35.152 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:11:35.154 [Task submitter] INFO  nextflow.Session - [2a/1dc794] Submitted process > MERGE_DEDUP_SPLIT (test-sample)
Jun-09 08:11:35.221 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 3; name: MERGE_DEDUP_SPLIT (test-sample); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/2a/1dc7941f6db20b66c1f1f65175ff55]
Jun-09 08:11:35.238 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:11:35.238 [Task submitter] INFO  nextflow.Session - [e7/4e4d77] Submitted process > QC_METRICS (test-sample)
Jun-09 08:11:35.243 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:11:35.243 [Task submitter] INFO  nextflow.Session - [d5/3ff07a] Submitted process > JUICER_HIC (test-sample)
Jun-09 08:11:35.247 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:11:35.247 [Task submitter] INFO  nextflow.Session - [05/800042] Submitted process > COOLER_PROCESS (test-sample)
Jun-09 08:11:35.302 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 6; name: QC_METRICS (test-sample); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/e7/4e4d7706f97d8f0fdd71509ff9f09b]
Jun-09 08:11:35.319 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 4; name: JUICER_HIC (test-sample); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/d5/3ff07afe86be1e8140b9b2472bc54d]
Jun-09 08:11:35.323 [TaskFinalizer-5] DEBUG nextflow.processor.TaskProcessor - Process `JUICER_HIC (test-sample)` is unable to find [UnixPath]: `/Users/<USER>/projects/topology-tools/work/d5/3ff07afe86be1e8140b9b2472bc54d/juicer_error.log` (pattern: `juicer_error.log`)
Jun-09 08:11:35.329 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 5; name: COOLER_PROCESS (test-sample); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/05/8000421691fe4c165335b6ab2e7241]
Jun-09 08:11:35.401 [TaskFinalizer-5] DEBUG nextflow.processor.TaskProcessor - Process JUICER_HIC > Skipping output binding because one or more optional files are missing: fileoutparam<3>
Jun-09 08:11:35.403 [main] DEBUG nextflow.Session - Session await > all processes finished
Jun-09 08:11:35.429 [Task monitor] DEBUG n.processor.TaskPollingMonitor - <<< barrier arrives (monitor: local) - terminating tasks monitor poll loop
Jun-09 08:11:35.430 [main] DEBUG nextflow.Session - Session await > all barriers passed
Jun-09 08:11:35.432 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'TaskFinalizer' shutdown completed (hard=false)
Jun-09 08:11:35.434 [main] INFO  nextflow.Nextflow - 
========================================================================================
                        Pipeline Execution Summary
========================================================================================
Completed at    : 2025-06-09T08:11:35.432729-04:00
Duration        : 1.5s
Success         : true
Work directory  : /Users/<USER>/projects/topology-tools/work
Exit status     : 0
Error message   : None
========================================================================================

Jun-09 08:11:35.434 [main] INFO  nextflow.Nextflow - 🎉 Pipeline completed successfully!
Jun-09 08:11:35.434 [main] INFO  nextflow.Nextflow - 📁 Results are in: ./test_results
Jun-09 08:11:35.437 [main] DEBUG n.trace.WorkflowStatsObserver - Workflow completed > WorkflowStats[succeededCount=6; failedCount=0; ignoredCount=0; cachedCount=0; pendingCount=0; submittedCount=0; runningCount=0; retriesCount=0; abortedCount=0; succeedDuration=278ms; failedDuration=0ms; cachedDuration=0ms;loadCpus=0; loadMemory=0; peakRunning=3; peakCpus=6; peakMemory=16 GB; ]
Jun-09 08:11:35.437 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow completed -- saving trace file
Jun-09 08:11:35.438 [main] DEBUG nextflow.trace.ReportObserver - Workflow completed -- rendering execution report
Jun-09 08:11:36.358 [main] DEBUG nextflow.trace.TimelineObserver - Workflow completed -- rendering execution timeline
Jun-09 08:11:36.462 [main] WARN  nextflow.dag.GraphvizRenderer - Graphviz is required to render the execution DAG in the given format -- See http://www.graphviz.org for more info.
Jun-09 08:11:36.536 [main] DEBUG nextflow.cache.CacheDB - Closing CacheDB done
Jun-09 08:11:36.547 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'FileTransfer' shutdown completed (hard=false)
Jun-09 08:11:36.547 [main] DEBUG nextflow.script.ScriptRunner - > Execution complete -- Goodbye
