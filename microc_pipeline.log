2025-06-06 22:56:16,006 - INFO - Starting Micro-C pipeline for sample small-rcmc
2025-06-06 22:56:16,006 - INFO - Total fastq size: 0 GB
2025-06-06 22:56:16,006 - INFO - Processing FASTQ pair 1/2
2025-06-06 22:56:16,006 - INFO - Starting Extract reference index
2025-06-06 22:56:16,006 - INFO - Command: tar zxvf tests/small-region-capture-micro-c/test_bwa_index.tgz -C test_pipeline_output/align/chunk_0/genome_index
2025-06-06 22:56:16,037 - INFO - Completed Extract reference index in 0.03 seconds
2025-06-06 22:56:16,038 - INFO - Starting Find bwt file
2025-06-06 22:56:16,038 - INFO - Command: find test_pipeline_output/align/chunk_0/genome_index -name '*.bwt'
2025-06-06 22:56:16,046 - INFO - Completed Find bwt file in 0.01 seconds
2025-06-06 22:56:16,046 - INFO - Starting Align and process small_rcmc_r1.fq.gz
2025-06-06 22:56:16,046 - INFO - Command: 
    bwa mem -5SP -T0 -t2 test_pipeline_output/align/chunk_0/genome_index/test.fa.gz tests/small-region-capture-micro-c/small_rcmc_r1.fq.gz tests/small-region-capture-micro-c/small_rcmc_r2.fq.gz |     pairtools parse --min-mapq 20 --walks-policy 5unique     --max-inter-align-gap 30 --add-columns pos5,pos3,dist_to_5,dist_to_3,read_len     --nproc-in 2 --nproc-out 2 --chroms-path tests/small-region-capture-micro-c/test.chrom.sizes |     pairtools sort --nproc 2 -o test_pipeline_output/align/chunk_0/small_rcmc_r1.fq.gz.pairsam.gz
    
2025-06-06 22:56:58,464 - INFO - Completed Align and process small_rcmc_r1.fq.gz in 42.42 seconds
2025-06-06 22:56:58,464 - INFO - Processing FASTQ pair 2/2
2025-06-06 22:56:58,464 - INFO - Starting Extract reference index
2025-06-06 22:56:58,464 - INFO - Command: tar zxvf tests/small-region-capture-micro-c/test_bwa_index.tgz -C test_pipeline_output/align/chunk_1/genome_index
2025-06-06 22:56:58,492 - INFO - Completed Extract reference index in 0.03 seconds
2025-06-06 22:56:58,493 - INFO - Starting Find bwt file
2025-06-06 22:56:58,493 - INFO - Command: find test_pipeline_output/align/chunk_1/genome_index -name '*.bwt'
2025-06-06 22:56:58,500 - INFO - Completed Find bwt file in 0.01 seconds
2025-06-06 22:56:58,500 - INFO - Starting Align and process small_rcmc-extra-reads_r1.fq.gz
2025-06-06 22:56:58,500 - INFO - Command: 
    bwa mem -5SP -T0 -t2 test_pipeline_output/align/chunk_1/genome_index/test.fa.gz tests/small-region-capture-micro-c/small_rcmc-extra-reads_r1.fq.gz tests/small-region-capture-micro-c/small_rcmc-extra-reads_r2.fq.gz |     pairtools parse --min-mapq 20 --walks-policy 5unique     --max-inter-align-gap 30 --add-columns pos5,pos3,dist_to_5,dist_to_3,read_len     --nproc-in 2 --nproc-out 2 --chroms-path tests/small-region-capture-micro-c/test.chrom.sizes |     pairtools sort --nproc 2 -o test_pipeline_output/align/chunk_1/small_rcmc-extra-reads_r1.fq.gz.pairsam.gz
    
2025-06-06 22:56:59,794 - INFO - Completed Align and process small_rcmc-extra-reads_r1.fq.gz in 1.29 seconds
2025-06-06 22:56:59,794 - INFO - Starting Merge, dedup, and split pairs
2025-06-06 22:56:59,794 - INFO - Command: 
    pairtools merge --nproc 12 test_pipeline_output/align/chunk_0/small_rcmc_r1.fq.gz.pairsam.gz test_pipeline_output/align/chunk_1/small_rcmc-extra-reads_r1.fq.gz.pairsam.gz |     pairtools dedup --nproc-in 2 --nproc-out 8 --mark-dups --output-stats test_pipeline_output/merge/small-rcmc.stats.txt |     pairtools split --nproc-in 2 --nproc-out 8 --output-pairs test_pipeline_output/merge/small-rcmc.mapped.pairs --output-sam - |     samtools view -bS -@6 |     samtools sort -@6 -o test_pipeline_output/merge/small-rcmc.bam
    
2025-06-06 22:57:02,818 - INFO - Completed Merge, dedup, and split pairs in 3.02 seconds
2025-06-06 22:57:02,818 - INFO - Starting Index BAM
2025-06-06 22:57:02,818 - INFO - Command: samtools index test_pipeline_output/merge/small-rcmc.bam
2025-06-06 22:57:02,845 - INFO - Completed Index BAM in 0.03 seconds
2025-06-06 22:57:02,846 - INFO - Starting Generate Juicer HiC file
2025-06-06 22:57:02,846 - INFO - Command: 
    java -Xmx120g -Djava.awt.headless=true -jar /Users/<USER>/projects/topology-tools/juicer_tools/juicer_tools.jar pre         --threads 2         test_pipeline_output/merge/small-rcmc.mapped.pairs         test_pipeline_output/hic/small-rcmc.hic         tests/small-region-capture-micro-c/test.chrom.sizes
    
2025-06-06 22:57:05,526 - ERROR - Generate Juicer HiC file failed with return code 56
2025-06-06 22:57:05,526 - ERROR - STDERR: No mndIndex provided
Using single threaded preprocessor
java.lang.ArrayIndexOutOfBoundsException: Index 3 out of bounds for length 2
	at juicebox.tools.utils.original.mnditerator.ComplexLineParser.generateBasicPair(ComplexLineParser.java:56)
	at juicebox.tools.utils.original.mnditerator.MNDFileParser.parseDCICFormat(MNDFileParser.java:118)
	at juicebox.tools.utils.original.mnditerator.MNDFileParser.parse(MNDFileParser.java:83)
	at juicebox.tools.utils.original.mnditerator.GenericPairIterator.advance(GenericPairIterator.java:56)
	at juicebox.tools.utils.original.mnditerator.GenericPairIterator.next(GenericPairIterator.java:46)
	at juicebox.tools.utils.original.Preprocessor.computeWholeGenomeMatrix(Preprocessor.java:603)
	at juicebox.tools.utils.original.Preprocessor.writeBody(Preprocessor.java:690)
	at juicebox.tools.utils.original.Preprocessor.preprocess(Preprocessor.java:452)
	at juicebox.tools.clt.old.PreProcessing.run(PreProcessing.java:176)
	at juicebox.tools.HiCTools.main(HiCTools.java:97)

2025-06-06 22:57:40,656 - INFO - Starting Micro-C pipeline for sample small-rcmc
2025-06-06 22:57:40,656 - INFO - Total fastq size: 0 GB
2025-06-06 22:57:40,656 - INFO - Processing FASTQ pair 1/2
2025-06-06 22:57:40,656 - INFO - Starting Extract reference index
2025-06-06 22:57:40,656 - INFO - Command: tar zxvf tests/small-region-capture-micro-c/test_bwa_index.tgz -C test_pipeline_output/align/chunk_0/genome_index
2025-06-06 22:57:40,685 - INFO - Completed Extract reference index in 0.03 seconds
2025-06-06 22:57:40,685 - INFO - Starting Find bwt file
2025-06-06 22:57:40,685 - INFO - Command: find test_pipeline_output/align/chunk_0/genome_index -name '*.bwt'
2025-06-06 22:57:40,692 - INFO - Completed Find bwt file in 0.01 seconds
2025-06-06 22:57:40,692 - INFO - Starting Align and process small_rcmc_r1.fq.gz
2025-06-06 22:57:40,692 - INFO - Command: 
    bwa mem -5SP -T0 -t2 test_pipeline_output/align/chunk_0/genome_index/test.fa.gz tests/small-region-capture-micro-c/small_rcmc_r1.fq.gz tests/small-region-capture-micro-c/small_rcmc_r2.fq.gz |     pairtools parse --min-mapq 20 --walks-policy 5unique     --max-inter-align-gap 30 --add-columns pos5,pos3,dist_to_5,dist_to_3,read_len     --nproc-in 2 --nproc-out 2 --chroms-path tests/small-region-capture-micro-c/test.chrom.sizes |     pairtools sort --nproc 2 -o test_pipeline_output/align/chunk_0/small_rcmc_r1.fq.gz.pairsam.gz
    
2025-06-06 22:57:49,373 - INFO - Completed Align and process small_rcmc_r1.fq.gz in 8.68 seconds
2025-06-06 22:57:49,373 - INFO - Processing FASTQ pair 2/2
2025-06-06 22:57:49,373 - INFO - Starting Extract reference index
2025-06-06 22:57:49,373 - INFO - Command: tar zxvf tests/small-region-capture-micro-c/test_bwa_index.tgz -C test_pipeline_output/align/chunk_1/genome_index
2025-06-06 22:57:49,426 - INFO - Completed Extract reference index in 0.05 seconds
2025-06-06 22:57:49,427 - INFO - Starting Find bwt file
2025-06-06 22:57:49,427 - INFO - Command: find test_pipeline_output/align/chunk_1/genome_index -name '*.bwt'
2025-06-06 22:57:49,434 - INFO - Completed Find bwt file in 0.01 seconds
2025-06-06 22:57:49,434 - INFO - Starting Align and process small_rcmc-extra-reads_r1.fq.gz
2025-06-06 22:57:49,434 - INFO - Command: 
    bwa mem -5SP -T0 -t2 test_pipeline_output/align/chunk_1/genome_index/test.fa.gz tests/small-region-capture-micro-c/small_rcmc-extra-reads_r1.fq.gz tests/small-region-capture-micro-c/small_rcmc-extra-reads_r2.fq.gz |     pairtools parse --min-mapq 20 --walks-policy 5unique     --max-inter-align-gap 30 --add-columns pos5,pos3,dist_to_5,dist_to_3,read_len     --nproc-in 2 --nproc-out 2 --chroms-path tests/small-region-capture-micro-c/test.chrom.sizes |     pairtools sort --nproc 2 -o test_pipeline_output/align/chunk_1/small_rcmc-extra-reads_r1.fq.gz.pairsam.gz
    
2025-06-06 22:57:50,596 - INFO - Completed Align and process small_rcmc-extra-reads_r1.fq.gz in 1.16 seconds
2025-06-06 22:57:50,596 - INFO - Starting Merge, dedup, and split pairs
2025-06-06 22:57:50,596 - INFO - Command: 
    pairtools merge --nproc 12 test_pipeline_output/align/chunk_0/small_rcmc_r1.fq.gz.pairsam.gz test_pipeline_output/align/chunk_1/small_rcmc-extra-reads_r1.fq.gz.pairsam.gz |     pairtools dedup --nproc-in 2 --nproc-out 8 --mark-dups --output-stats test_pipeline_output/merge/small-rcmc.stats.txt |     pairtools split --nproc-in 2 --nproc-out 8 --output-pairs test_pipeline_output/merge/small-rcmc.mapped.pairs --output-sam - |     samtools view -bS -@6 |     samtools sort -@6 -o test_pipeline_output/merge/small-rcmc.bam
    
2025-06-06 22:57:53,456 - INFO - Completed Merge, dedup, and split pairs in 2.86 seconds
2025-06-06 22:57:53,456 - INFO - Starting Index BAM
2025-06-06 22:57:53,456 - INFO - Command: samtools index test_pipeline_output/merge/small-rcmc.bam
2025-06-06 22:57:53,483 - INFO - Completed Index BAM in 0.03 seconds
2025-06-06 22:57:53,483 - INFO - Starting Convert pairs to Juicer format
2025-06-06 22:57:53,483 - INFO - Command: 
    awk 'BEGIN{OFS="\t"} /^#/{print} !/^#/{print $1,$2,$3,$4,$5,$6,$7,$8}' test_pipeline_output/merge/small-rcmc.mapped.pairs > test_pipeline_output/hic/small-rcmc.juicer.pairs
    
2025-06-06 22:57:53,671 - INFO - Completed Convert pairs to Juicer format in 0.19 seconds
2025-06-06 22:57:53,671 - INFO - Starting Generate Juicer HiC file
2025-06-06 22:57:53,671 - INFO - Command: 
    java -Xmx120g -Djava.awt.headless=true -jar /Users/<USER>/projects/topology-tools/juicer_tools/juicer_tools.jar pre         --threads 2         test_pipeline_output/hic/small-rcmc.juicer.pairs         test_pipeline_output/hic/small-rcmc.hic         tests/small-region-capture-micro-c/test.chrom.sizes
    
2025-06-06 22:57:54,107 - ERROR - Generate Juicer HiC file failed with return code 56
2025-06-06 22:57:54,107 - ERROR - STDERR: No mndIndex provided
Using single threaded preprocessor
java.lang.ArrayIndexOutOfBoundsException: Index 3 out of bounds for length 2
	at juicebox.tools.utils.original.mnditerator.ComplexLineParser.generateBasicPair(ComplexLineParser.java:56)
	at juicebox.tools.utils.original.mnditerator.MNDFileParser.parseDCICFormat(MNDFileParser.java:118)
	at juicebox.tools.utils.original.mnditerator.MNDFileParser.parse(MNDFileParser.java:83)
	at juicebox.tools.utils.original.mnditerator.GenericPairIterator.advance(GenericPairIterator.java:56)
	at juicebox.tools.utils.original.mnditerator.GenericPairIterator.next(GenericPairIterator.java:46)
	at juicebox.tools.utils.original.Preprocessor.computeWholeGenomeMatrix(Preprocessor.java:603)
	at juicebox.tools.utils.original.Preprocessor.writeBody(Preprocessor.java:690)
	at juicebox.tools.utils.original.Preprocessor.preprocess(Preprocessor.java:452)
	at juicebox.tools.clt.old.PreProcessing.run(PreProcessing.java:176)
	at juicebox.tools.HiCTools.main(HiCTools.java:97)

2025-06-06 22:58:35,935 - INFO - Starting Micro-C pipeline for sample small-rcmc
2025-06-06 22:58:35,935 - INFO - Total fastq size: 0 GB
2025-06-06 22:58:35,935 - INFO - Processing FASTQ pair 1/2
2025-06-06 22:58:35,935 - INFO - Starting Extract reference index
2025-06-06 22:58:35,935 - INFO - Command: tar zxvf tests/small-region-capture-micro-c/test_bwa_index.tgz -C test_pipeline_output/align/chunk_0/genome_index
2025-06-06 22:58:35,968 - INFO - Completed Extract reference index in 0.03 seconds
2025-06-06 22:58:35,969 - INFO - Starting Find bwt file
2025-06-06 22:58:35,969 - INFO - Command: find test_pipeline_output/align/chunk_0/genome_index -name '*.bwt'
2025-06-06 22:58:35,977 - INFO - Completed Find bwt file in 0.01 seconds
2025-06-06 22:58:35,978 - INFO - Starting Align and process small_rcmc_r1.fq.gz
2025-06-06 22:58:35,978 - INFO - Command: 
    bwa mem -5SP -T0 -t2 test_pipeline_output/align/chunk_0/genome_index/test.fa.gz tests/small-region-capture-micro-c/small_rcmc_r1.fq.gz tests/small-region-capture-micro-c/small_rcmc_r2.fq.gz |     pairtools parse --min-mapq 20 --walks-policy 5unique     --max-inter-align-gap 30 --add-columns pos5,pos3,dist_to_5,dist_to_3,read_len     --nproc-in 2 --nproc-out 2 --chroms-path tests/small-region-capture-micro-c/test.chrom.sizes |     pairtools sort --nproc 2 -o test_pipeline_output/align/chunk_0/small_rcmc_r1.fq.gz.pairsam.gz
    
2025-06-06 22:58:44,756 - INFO - Completed Align and process small_rcmc_r1.fq.gz in 8.78 seconds
2025-06-06 22:58:44,756 - INFO - Processing FASTQ pair 2/2
2025-06-06 22:58:44,756 - INFO - Starting Extract reference index
2025-06-06 22:58:44,756 - INFO - Command: tar zxvf tests/small-region-capture-micro-c/test_bwa_index.tgz -C test_pipeline_output/align/chunk_1/genome_index
2025-06-06 22:58:44,783 - INFO - Completed Extract reference index in 0.03 seconds
2025-06-06 22:58:44,783 - INFO - Starting Find bwt file
2025-06-06 22:58:44,783 - INFO - Command: find test_pipeline_output/align/chunk_1/genome_index -name '*.bwt'
2025-06-06 22:58:44,791 - INFO - Completed Find bwt file in 0.01 seconds
2025-06-06 22:58:44,791 - INFO - Starting Align and process small_rcmc-extra-reads_r1.fq.gz
2025-06-06 22:58:44,791 - INFO - Command: 
    bwa mem -5SP -T0 -t2 test_pipeline_output/align/chunk_1/genome_index/test.fa.gz tests/small-region-capture-micro-c/small_rcmc-extra-reads_r1.fq.gz tests/small-region-capture-micro-c/small_rcmc-extra-reads_r2.fq.gz |     pairtools parse --min-mapq 20 --walks-policy 5unique     --max-inter-align-gap 30 --add-columns pos5,pos3,dist_to_5,dist_to_3,read_len     --nproc-in 2 --nproc-out 2 --chroms-path tests/small-region-capture-micro-c/test.chrom.sizes |     pairtools sort --nproc 2 -o test_pipeline_output/align/chunk_1/small_rcmc-extra-reads_r1.fq.gz.pairsam.gz
    
2025-06-06 22:58:45,985 - INFO - Completed Align and process small_rcmc-extra-reads_r1.fq.gz in 1.19 seconds
2025-06-06 22:58:45,985 - INFO - Starting Merge, dedup, and split pairs
2025-06-06 22:58:45,985 - INFO - Command: 
    pairtools merge --nproc 12 test_pipeline_output/align/chunk_0/small_rcmc_r1.fq.gz.pairsam.gz test_pipeline_output/align/chunk_1/small_rcmc-extra-reads_r1.fq.gz.pairsam.gz |     pairtools dedup --nproc-in 2 --nproc-out 8 --mark-dups --output-stats test_pipeline_output/merge/small-rcmc.stats.txt |     pairtools split --nproc-in 2 --nproc-out 8 --output-pairs test_pipeline_output/merge/small-rcmc.mapped.pairs --output-sam - |     samtools view -bS -@6 |     samtools sort -@6 -o test_pipeline_output/merge/small-rcmc.bam
    
2025-06-06 22:58:48,908 - INFO - Completed Merge, dedup, and split pairs in 2.92 seconds
2025-06-06 22:58:48,908 - INFO - Starting Index BAM
2025-06-06 22:58:48,908 - INFO - Command: samtools index test_pipeline_output/merge/small-rcmc.bam
2025-06-06 22:58:48,935 - INFO - Completed Index BAM in 0.03 seconds
2025-06-06 22:58:48,936 - INFO - Starting Generate cooler file
2025-06-06 22:58:48,936 - INFO - Command: cooler cload pairs -c1 2 -p1 3 -c2 4 -p2 5 tests/small-region-capture-micro-c/test.chrom.sizes:1000 test_pipeline_output/merge/small-rcmc.mapped.pairs test_pipeline_output/cooler/small-rcmc.cool
2025-06-06 22:58:58,994 - INFO - Completed Generate cooler file in 10.06 seconds
2025-06-06 22:58:58,994 - INFO - Starting Generate raw mcool file
2025-06-06 22:58:58,994 - INFO - Command: cooler zoomify --resolutions 1000N -o test_pipeline_output/cooler/small-rcmc.raw.mcool -p 4 test_pipeline_output/cooler/small-rcmc.cool
2025-06-06 22:59:00,366 - INFO - Completed Generate raw mcool file in 1.37 seconds
2025-06-06 22:59:00,366 - INFO - Starting Generate balanced mcool file
2025-06-06 22:59:00,366 - INFO - Command: cooler zoomify --resolutions 1000N -o test_pipeline_output/cooler/small-rcmc.balanced.mcool -p 4 --balance --balance-args '--nproc 4' test_pipeline_output/cooler/small-rcmc.cool
2025-06-06 22:59:03,606 - INFO - Completed Generate balanced mcool file in 3.24 seconds
2025-06-06 22:59:03,606 - INFO - Starting Convert pairs to Juicer format
2025-06-06 22:59:03,606 - INFO - Command: 
    awk 'BEGIN{OFS="\t"} /^#/{print} !/^#/{print $1,$2,$3,$4,$5,$6,$7,$8}' test_pipeline_output/merge/small-rcmc.mapped.pairs > test_pipeline_output/hic/small-rcmc.juicer.pairs
    
2025-06-06 22:59:03,755 - INFO - Completed Convert pairs to Juicer format in 0.15 seconds
2025-06-06 22:59:03,755 - INFO - Starting Generate Juicer HiC file
2025-06-06 22:59:03,755 - INFO - Command: 
    java -Xmx120g -Djava.awt.headless=true -jar /Users/<USER>/projects/topology-tools/juicer_tools/juicer_tools.jar pre         --threads 2         test_pipeline_output/hic/small-rcmc.juicer.pairs         test_pipeline_output/hic/small-rcmc.hic         tests/small-region-capture-micro-c/test.chrom.sizes
    
2025-06-06 22:59:04,300 - ERROR - Generate Juicer HiC file failed with return code 56
2025-06-06 22:59:04,300 - ERROR - STDERR: No mndIndex provided
Using single threaded preprocessor
java.lang.ArrayIndexOutOfBoundsException: Index 3 out of bounds for length 2
	at juicebox.tools.utils.original.mnditerator.ComplexLineParser.generateBasicPair(ComplexLineParser.java:56)
	at juicebox.tools.utils.original.mnditerator.MNDFileParser.parseDCICFormat(MNDFileParser.java:118)
	at juicebox.tools.utils.original.mnditerator.MNDFileParser.parse(MNDFileParser.java:83)
	at juicebox.tools.utils.original.mnditerator.GenericPairIterator.advance(GenericPairIterator.java:56)
	at juicebox.tools.utils.original.mnditerator.GenericPairIterator.next(GenericPairIterator.java:46)
	at juicebox.tools.utils.original.Preprocessor.computeWholeGenomeMatrix(Preprocessor.java:603)
	at juicebox.tools.utils.original.Preprocessor.writeBody(Preprocessor.java:690)
	at juicebox.tools.utils.original.Preprocessor.preprocess(Preprocessor.java:452)
	at juicebox.tools.clt.old.PreProcessing.run(PreProcessing.java:176)
	at juicebox.tools.HiCTools.main(HiCTools.java:97)

2025-06-06 22:59:04,300 - WARNING - Failed to generate HiC file: Generate Juicer HiC file failed
2025-06-06 22:59:04,300 - INFO - Starting Extract QC metrics
2025-06-06 22:59:04,300 - INFO - Command: 
    cat test_pipeline_output/merge/small-rcmc.stats.txt | grep -w "total" | cut -f2 > test_pipeline_output/qc/small-rcmc_qc.txt
    cat test_pipeline_output/merge/small-rcmc.stats.txt | grep -w "total_mapped" | cut -f2 >> test_pipeline_output/qc/small-rcmc_qc.txt
    cat test_pipeline_output/merge/small-rcmc.stats.txt | grep -w "total_nodups" | cut -f2 >> test_pipeline_output/qc/small-rcmc_qc.txt
    cat test_pipeline_output/merge/small-rcmc.stats.txt | grep -w "cis_1kb+" | cut -f2 >> test_pipeline_output/qc/small-rcmc_qc.txt
    cat test_pipeline_output/merge/small-rcmc.stats.txt | grep -w "cis_10kb+" | cut -f2 >> test_pipeline_output/qc/small-rcmc_qc.txt
    
2025-06-06 22:59:04,584 - INFO - Completed Extract QC metrics in 0.28 seconds
2025-06-06 22:59:04,584 - INFO - QC Results: {'reads_total': '100100', 'reads_mapped': '22670', 'reads_nodups': '22109', 'reads_cis_1kb': '14500', 'reads_cis_10kb': '8379'}
2025-06-06 22:59:04,584 - INFO - Pipeline completed successfully
2025-06-06 22:59:04,584 - INFO - Output files:
2025-06-06 22:59:04,584 - INFO -   Mapped pairs: test_pipeline_output/merge/small-rcmc.mapped.pairs
2025-06-06 22:59:04,584 - INFO -   BAM file: test_pipeline_output/merge/small-rcmc.bam
2025-06-06 22:59:04,584 - INFO -   HiC file: Failed to generate
2025-06-06 22:59:04,584 - INFO -   Raw mcool file: test_pipeline_output/cooler/small-rcmc.raw.mcool
2025-06-06 22:59:04,584 - INFO -   Balanced mcool file: test_pipeline_output/cooler/small-rcmc.balanced.mcool
2025-06-06 22:59:04,585 - INFO -   QC stats: {'reads_total': '100100', 'reads_mapped': '22670', 'reads_nodups': '22109', 'reads_cis_1kb': '14500', 'reads_cis_10kb': '8379'}
2025-06-06 23:04:38,866 - INFO - Starting Micro-C pipeline for sample small-rcmc-benchmark
2025-06-06 23:04:38,867 - INFO - Total fastq size: 0 GB
2025-06-06 23:04:38,867 - INFO - Processing FASTQ pair 1/2
2025-06-06 23:04:38,867 - INFO - Starting Extract reference index
2025-06-06 23:04:38,867 - INFO - Command: tar zxvf tests/small-region-capture-micro-c/test_bwa_index.tgz -C benchmark_results/small-rcmc-benchmark_output/align/chunk_0/genome_index
2025-06-06 23:04:38,892 - INFO - Completed Extract reference index in 0.02 seconds
2025-06-06 23:04:38,892 - INFO - Starting Find bwt file
2025-06-06 23:04:38,892 - INFO - Command: find benchmark_results/small-rcmc-benchmark_output/align/chunk_0/genome_index -name '*.bwt'
2025-06-06 23:04:38,901 - INFO - Completed Find bwt file in 0.01 seconds
2025-06-06 23:04:38,901 - INFO - Starting Align and process small_rcmc_r1.fq.gz
2025-06-06 23:04:38,901 - INFO - Command: 
    bwa mem -5SP -T0 -t4 benchmark_results/small-rcmc-benchmark_output/align/chunk_0/genome_index/test.fa.gz tests/small-region-capture-micro-c/small_rcmc_r1.fq.gz tests/small-region-capture-micro-c/small_rcmc_r2.fq.gz |     pairtools parse --min-mapq 20 --walks-policy 5unique     --max-inter-align-gap 30 --add-columns pos5,pos3,dist_to_5,dist_to_3,read_len     --nproc-in 4 --nproc-out 4 --chroms-path tests/small-region-capture-micro-c/test.chrom.sizes |     pairtools sort --nproc 4 -o benchmark_results/small-rcmc-benchmark_output/align/chunk_0/small_rcmc_r1.fq.gz.pairsam.gz
    
2025-06-06 23:04:44,693 - INFO - Completed Align and process small_rcmc_r1.fq.gz in 5.79 seconds
2025-06-06 23:04:44,694 - INFO - Processing FASTQ pair 2/2
2025-06-06 23:04:44,694 - INFO - Starting Extract reference index
2025-06-06 23:04:44,694 - INFO - Command: tar zxvf tests/small-region-capture-micro-c/test_bwa_index.tgz -C benchmark_results/small-rcmc-benchmark_output/align/chunk_1/genome_index
2025-06-06 23:04:44,719 - INFO - Completed Extract reference index in 0.03 seconds
2025-06-06 23:04:44,719 - INFO - Starting Find bwt file
2025-06-06 23:04:44,719 - INFO - Command: find benchmark_results/small-rcmc-benchmark_output/align/chunk_1/genome_index -name '*.bwt'
2025-06-06 23:04:44,726 - INFO - Completed Find bwt file in 0.01 seconds
2025-06-06 23:04:44,726 - INFO - Starting Align and process small_rcmc-extra-reads_r1.fq.gz
2025-06-06 23:04:44,726 - INFO - Command: 
    bwa mem -5SP -T0 -t4 benchmark_results/small-rcmc-benchmark_output/align/chunk_1/genome_index/test.fa.gz tests/small-region-capture-micro-c/small_rcmc-extra-reads_r1.fq.gz tests/small-region-capture-micro-c/small_rcmc-extra-reads_r2.fq.gz |     pairtools parse --min-mapq 20 --walks-policy 5unique     --max-inter-align-gap 30 --add-columns pos5,pos3,dist_to_5,dist_to_3,read_len     --nproc-in 4 --nproc-out 4 --chroms-path tests/small-region-capture-micro-c/test.chrom.sizes |     pairtools sort --nproc 4 -o benchmark_results/small-rcmc-benchmark_output/align/chunk_1/small_rcmc-extra-reads_r1.fq.gz.pairsam.gz
    
2025-06-06 23:04:45,894 - INFO - Completed Align and process small_rcmc-extra-reads_r1.fq.gz in 1.17 seconds
2025-06-06 23:04:45,895 - INFO - Starting Merge, dedup, and split pairs
2025-06-06 23:04:45,895 - INFO - Command: 
    pairtools merge --nproc 12 benchmark_results/small-rcmc-benchmark_output/align/chunk_0/small_rcmc_r1.fq.gz.pairsam.gz benchmark_results/small-rcmc-benchmark_output/align/chunk_1/small_rcmc-extra-reads_r1.fq.gz.pairsam.gz |     pairtools dedup --nproc-in 2 --nproc-out 8 --mark-dups --output-stats benchmark_results/small-rcmc-benchmark_output/merge/small-rcmc-benchmark.stats.txt |     pairtools split --nproc-in 2 --nproc-out 8 --output-pairs benchmark_results/small-rcmc-benchmark_output/merge/small-rcmc-benchmark.mapped.pairs --output-sam - |     samtools view -bS -@6 |     samtools sort -@6 -o benchmark_results/small-rcmc-benchmark_output/merge/small-rcmc-benchmark.bam
    
2025-06-06 23:04:48,775 - INFO - Completed Merge, dedup, and split pairs in 2.88 seconds
2025-06-06 23:04:48,775 - INFO - Starting Index BAM
2025-06-06 23:04:48,775 - INFO - Command: samtools index benchmark_results/small-rcmc-benchmark_output/merge/small-rcmc-benchmark.bam
2025-06-06 23:04:48,802 - INFO - Completed Index BAM in 0.03 seconds
2025-06-06 23:04:48,802 - INFO - Starting Generate cooler file
2025-06-06 23:04:48,802 - INFO - Command: cooler cload pairs -c1 2 -p1 3 -c2 4 -p2 5 tests/small-region-capture-micro-c/test.chrom.sizes:1000 benchmark_results/small-rcmc-benchmark_output/merge/small-rcmc-benchmark.mapped.pairs benchmark_results/small-rcmc-benchmark_output/cooler/small-rcmc-benchmark.cool
2025-06-06 23:04:50,258 - INFO - Completed Generate cooler file in 1.46 seconds
2025-06-06 23:04:50,258 - INFO - Starting Generate raw mcool file
2025-06-06 23:04:50,258 - INFO - Command: cooler zoomify --resolutions 1000N -o benchmark_results/small-rcmc-benchmark_output/cooler/small-rcmc-benchmark.raw.mcool -p 4 benchmark_results/small-rcmc-benchmark_output/cooler/small-rcmc-benchmark.cool
2025-06-06 23:04:51,599 - INFO - Completed Generate raw mcool file in 1.34 seconds
2025-06-06 23:04:51,599 - INFO - Starting Generate balanced mcool file
2025-06-06 23:04:51,599 - INFO - Command: cooler zoomify --resolutions 1000N -o benchmark_results/small-rcmc-benchmark_output/cooler/small-rcmc-benchmark.balanced.mcool -p 4 --balance --balance-args '--nproc 4' benchmark_results/small-rcmc-benchmark_output/cooler/small-rcmc-benchmark.cool
2025-06-06 23:04:54,864 - INFO - Completed Generate balanced mcool file in 3.26 seconds
2025-06-06 23:04:54,865 - INFO - Starting Convert pairs to Juicer format
2025-06-06 23:04:54,865 - INFO - Command: 
    awk 'BEGIN{OFS="\t"} /^#/{print} !/^#/{print $1,$2,$3,$4,$5,$6,$7,$8}' benchmark_results/small-rcmc-benchmark_output/merge/small-rcmc-benchmark.mapped.pairs > benchmark_results/small-rcmc-benchmark_output/hic/small-rcmc-benchmark.juicer.pairs
    
2025-06-06 23:04:55,014 - INFO - Completed Convert pairs to Juicer format in 0.15 seconds
2025-06-06 23:04:55,014 - INFO - Starting Generate Juicer HiC file
2025-06-06 23:04:55,014 - INFO - Command: 
    java -Xmx120g -Djava.awt.headless=true -jar /Users/<USER>/projects/topology-tools/juicer_tools/juicer_tools.jar pre         --threads 4         benchmark_results/small-rcmc-benchmark_output/hic/small-rcmc-benchmark.juicer.pairs         benchmark_results/small-rcmc-benchmark_output/hic/small-rcmc-benchmark.hic         tests/small-region-capture-micro-c/test.chrom.sizes
    
2025-06-06 23:04:55,559 - ERROR - Generate Juicer HiC file failed with return code 56
2025-06-06 23:04:55,559 - ERROR - STDERR: No mndIndex provided
Using single threaded preprocessor
java.lang.ArrayIndexOutOfBoundsException: Index 3 out of bounds for length 2
	at juicebox.tools.utils.original.mnditerator.ComplexLineParser.generateBasicPair(ComplexLineParser.java:56)
	at juicebox.tools.utils.original.mnditerator.MNDFileParser.parseDCICFormat(MNDFileParser.java:118)
	at juicebox.tools.utils.original.mnditerator.MNDFileParser.parse(MNDFileParser.java:83)
	at juicebox.tools.utils.original.mnditerator.GenericPairIterator.advance(GenericPairIterator.java:56)
	at juicebox.tools.utils.original.mnditerator.GenericPairIterator.next(GenericPairIterator.java:46)
	at juicebox.tools.utils.original.Preprocessor.computeWholeGenomeMatrix(Preprocessor.java:603)
	at juicebox.tools.utils.original.Preprocessor.writeBody(Preprocessor.java:690)
	at juicebox.tools.utils.original.Preprocessor.preprocess(Preprocessor.java:452)
	at juicebox.tools.clt.old.PreProcessing.run(PreProcessing.java:176)
	at juicebox.tools.HiCTools.main(HiCTools.java:97)

2025-06-06 23:04:55,559 - WARNING - Failed to generate HiC file: Generate Juicer HiC file failed
2025-06-06 23:04:55,559 - INFO - Starting Extract QC metrics
2025-06-06 23:04:55,559 - INFO - Command: 
    cat benchmark_results/small-rcmc-benchmark_output/merge/small-rcmc-benchmark.stats.txt | grep -w "total" | cut -f2 > benchmark_results/small-rcmc-benchmark_output/qc/small-rcmc-benchmark_qc.txt
    cat benchmark_results/small-rcmc-benchmark_output/merge/small-rcmc-benchmark.stats.txt | grep -w "total_mapped" | cut -f2 >> benchmark_results/small-rcmc-benchmark_output/qc/small-rcmc-benchmark_qc.txt
    cat benchmark_results/small-rcmc-benchmark_output/merge/small-rcmc-benchmark.stats.txt | grep -w "total_nodups" | cut -f2 >> benchmark_results/small-rcmc-benchmark_output/qc/small-rcmc-benchmark_qc.txt
    cat benchmark_results/small-rcmc-benchmark_output/merge/small-rcmc-benchmark.stats.txt | grep -w "cis_1kb+" | cut -f2 >> benchmark_results/small-rcmc-benchmark_output/qc/small-rcmc-benchmark_qc.txt
    cat benchmark_results/small-rcmc-benchmark_output/merge/small-rcmc-benchmark.stats.txt | grep -w "cis_10kb+" | cut -f2 >> benchmark_results/small-rcmc-benchmark_output/qc/small-rcmc-benchmark_qc.txt
    
2025-06-06 23:04:55,580 - INFO - Completed Extract QC metrics in 0.02 seconds
2025-06-06 23:04:55,580 - INFO - QC Results: {'reads_total': '100100', 'reads_mapped': '22670', 'reads_nodups': '22109', 'reads_cis_1kb': '14500', 'reads_cis_10kb': '8379'}
2025-06-06 23:04:55,580 - INFO - Pipeline completed successfully
2025-06-06 23:04:55,580 - INFO - Output files:
2025-06-06 23:04:55,580 - INFO -   Mapped pairs: benchmark_results/small-rcmc-benchmark_output/merge/small-rcmc-benchmark.mapped.pairs
2025-06-06 23:04:55,580 - INFO -   BAM file: benchmark_results/small-rcmc-benchmark_output/merge/small-rcmc-benchmark.bam
2025-06-06 23:04:55,580 - INFO -   HiC file: Failed to generate
2025-06-06 23:04:55,581 - INFO -   Raw mcool file: benchmark_results/small-rcmc-benchmark_output/cooler/small-rcmc-benchmark.raw.mcool
2025-06-06 23:04:55,581 - INFO -   Balanced mcool file: benchmark_results/small-rcmc-benchmark_output/cooler/small-rcmc-benchmark.balanced.mcool
2025-06-06 23:04:55,581 - INFO -   QC stats: {'reads_total': '100100', 'reads_mapped': '22670', 'reads_nodups': '22109', 'reads_cis_1kb': '14500', 'reads_cis_10kb': '8379'}
2025-06-06 23:04:55,617 - INFO - Starting Micro-C pipeline for sample large-rcmc-benchmark
2025-06-06 23:04:55,617 - INFO - Total fastq size: 0 GB
2025-06-06 23:04:55,617 - INFO - Processing FASTQ pair 1/1
2025-06-06 23:04:55,618 - INFO - Starting Extract reference index
2025-06-06 23:04:55,618 - INFO - Command: tar zxvf tests/large-test-data/test_bwa_index.tgz -C benchmark_results/large-rcmc-benchmark_output/align/chunk_0/genome_index
2025-06-06 23:04:55,644 - INFO - Completed Extract reference index in 0.03 seconds
2025-06-06 23:04:55,644 - INFO - Starting Find bwt file
2025-06-06 23:04:55,644 - INFO - Command: find benchmark_results/large-rcmc-benchmark_output/align/chunk_0/genome_index -name '*.bwt'
2025-06-06 23:04:55,653 - INFO - Completed Find bwt file in 0.01 seconds
2025-06-06 23:04:55,653 - INFO - Starting Align and process large_rcmc_20x_r1.fq.gz
2025-06-06 23:04:55,653 - INFO - Command: 
    bwa mem -5SP -T0 -t4 benchmark_results/large-rcmc-benchmark_output/align/chunk_0/genome_index/test.fa.gz tests/large-test-data/large_rcmc_20x_r1.fq.gz tests/large-test-data/large_rcmc_20x_r2.fq.gz |     pairtools parse --min-mapq 20 --walks-policy 5unique     --max-inter-align-gap 30 --add-columns pos5,pos3,dist_to_5,dist_to_3,read_len     --nproc-in 4 --nproc-out 4 --chroms-path tests/large-test-data/test.chrom.sizes |     pairtools sort --nproc 4 -o benchmark_results/large-rcmc-benchmark_output/align/chunk_0/large_rcmc_20x_r1.fq.gz.pairsam.gz
    
2025-06-06 23:06:10,179 - INFO - Completed Align and process large_rcmc_20x_r1.fq.gz in 74.53 seconds
2025-06-06 23:06:10,180 - INFO - Starting Merge, dedup, and split pairs
2025-06-06 23:06:10,180 - INFO - Command: 
    pairtools merge --nproc 12 benchmark_results/large-rcmc-benchmark_output/align/chunk_0/large_rcmc_20x_r1.fq.gz.pairsam.gz |     pairtools dedup --nproc-in 2 --nproc-out 8 --mark-dups --output-stats benchmark_results/large-rcmc-benchmark_output/merge/large-rcmc-benchmark.stats.txt |     pairtools split --nproc-in 2 --nproc-out 8 --output-pairs benchmark_results/large-rcmc-benchmark_output/merge/large-rcmc-benchmark.mapped.pairs --output-sam - |     samtools view -bS -@6 |     samtools sort -@6 -o benchmark_results/large-rcmc-benchmark_output/merge/large-rcmc-benchmark.bam
    
2025-06-06 23:06:35,666 - INFO - Completed Merge, dedup, and split pairs in 25.49 seconds
2025-06-06 23:06:35,667 - INFO - Starting Index BAM
2025-06-06 23:06:35,667 - INFO - Command: samtools index benchmark_results/large-rcmc-benchmark_output/merge/large-rcmc-benchmark.bam
2025-06-06 23:06:35,693 - INFO - Completed Index BAM in 0.03 seconds
2025-06-06 23:06:35,693 - INFO - Starting Generate cooler file
2025-06-06 23:06:35,693 - INFO - Command: cooler cload pairs -c1 2 -p1 3 -c2 4 -p2 5 tests/large-test-data/test.chrom.sizes:1000 benchmark_results/large-rcmc-benchmark_output/merge/large-rcmc-benchmark.mapped.pairs benchmark_results/large-rcmc-benchmark_output/cooler/large-rcmc-benchmark.cool
2025-06-06 23:06:37,005 - INFO - Completed Generate cooler file in 1.31 seconds
2025-06-06 23:06:37,005 - INFO - Starting Generate raw mcool file
2025-06-06 23:06:37,005 - INFO - Command: cooler zoomify --resolutions 1000N -o benchmark_results/large-rcmc-benchmark_output/cooler/large-rcmc-benchmark.raw.mcool -p 4 benchmark_results/large-rcmc-benchmark_output/cooler/large-rcmc-benchmark.cool
2025-06-06 23:06:38,349 - INFO - Completed Generate raw mcool file in 1.34 seconds
2025-06-06 23:06:38,350 - INFO - Starting Generate balanced mcool file
2025-06-06 23:06:38,350 - INFO - Command: cooler zoomify --resolutions 1000N -o benchmark_results/large-rcmc-benchmark_output/cooler/large-rcmc-benchmark.balanced.mcool -p 4 --balance --balance-args '--nproc 4' benchmark_results/large-rcmc-benchmark_output/cooler/large-rcmc-benchmark.cool
2025-06-06 23:06:41,610 - INFO - Completed Generate balanced mcool file in 3.26 seconds
2025-06-06 23:06:41,610 - INFO - Starting Convert pairs to Juicer format
2025-06-06 23:06:41,610 - INFO - Command: 
    awk 'BEGIN{OFS="\t"} /^#/{print} !/^#/{print $1,$2,$3,$4,$5,$6,$7,$8}' benchmark_results/large-rcmc-benchmark_output/merge/large-rcmc-benchmark.mapped.pairs > benchmark_results/large-rcmc-benchmark_output/hic/large-rcmc-benchmark.juicer.pairs
    
2025-06-06 23:06:41,770 - INFO - Completed Convert pairs to Juicer format in 0.16 seconds
2025-06-06 23:06:41,770 - INFO - Starting Generate Juicer HiC file
2025-06-06 23:06:41,770 - INFO - Command: 
    java -Xmx120g -Djava.awt.headless=true -jar /Users/<USER>/projects/topology-tools/juicer_tools/juicer_tools.jar pre         --threads 4         benchmark_results/large-rcmc-benchmark_output/hic/large-rcmc-benchmark.juicer.pairs         benchmark_results/large-rcmc-benchmark_output/hic/large-rcmc-benchmark.hic         tests/large-test-data/test.chrom.sizes
    
2025-06-06 23:06:42,302 - ERROR - Generate Juicer HiC file failed with return code 56
2025-06-06 23:06:42,302 - ERROR - STDERR: No mndIndex provided
Using single threaded preprocessor
java.lang.ArrayIndexOutOfBoundsException: Index 3 out of bounds for length 2
	at juicebox.tools.utils.original.mnditerator.ComplexLineParser.generateBasicPair(ComplexLineParser.java:56)
	at juicebox.tools.utils.original.mnditerator.MNDFileParser.parseDCICFormat(MNDFileParser.java:118)
	at juicebox.tools.utils.original.mnditerator.MNDFileParser.parse(MNDFileParser.java:83)
	at juicebox.tools.utils.original.mnditerator.GenericPairIterator.advance(GenericPairIterator.java:56)
	at juicebox.tools.utils.original.mnditerator.GenericPairIterator.next(GenericPairIterator.java:46)
	at juicebox.tools.utils.original.Preprocessor.computeWholeGenomeMatrix(Preprocessor.java:603)
	at juicebox.tools.utils.original.Preprocessor.writeBody(Preprocessor.java:690)
	at juicebox.tools.utils.original.Preprocessor.preprocess(Preprocessor.java:452)
	at juicebox.tools.clt.old.PreProcessing.run(PreProcessing.java:176)
	at juicebox.tools.HiCTools.main(HiCTools.java:97)

2025-06-06 23:06:42,302 - WARNING - Failed to generate HiC file: Generate Juicer HiC file failed
2025-06-06 23:06:42,303 - INFO - Starting Extract QC metrics
2025-06-06 23:06:42,303 - INFO - Command: 
    cat benchmark_results/large-rcmc-benchmark_output/merge/large-rcmc-benchmark.stats.txt | grep -w "total" | cut -f2 > benchmark_results/large-rcmc-benchmark_output/qc/large-rcmc-benchmark_qc.txt
    cat benchmark_results/large-rcmc-benchmark_output/merge/large-rcmc-benchmark.stats.txt | grep -w "total_mapped" | cut -f2 >> benchmark_results/large-rcmc-benchmark_output/qc/large-rcmc-benchmark_qc.txt
    cat benchmark_results/large-rcmc-benchmark_output/merge/large-rcmc-benchmark.stats.txt | grep -w "total_nodups" | cut -f2 >> benchmark_results/large-rcmc-benchmark_output/qc/large-rcmc-benchmark_qc.txt
    cat benchmark_results/large-rcmc-benchmark_output/merge/large-rcmc-benchmark.stats.txt | grep -w "cis_1kb+" | cut -f2 >> benchmark_results/large-rcmc-benchmark_output/qc/large-rcmc-benchmark_qc.txt
    cat benchmark_results/large-rcmc-benchmark_output/merge/large-rcmc-benchmark.stats.txt | grep -w "cis_10kb+" | cut -f2 >> benchmark_results/large-rcmc-benchmark_output/qc/large-rcmc-benchmark_qc.txt
    
2025-06-06 23:06:42,323 - INFO - Completed Extract QC metrics in 0.02 seconds
2025-06-06 23:06:42,323 - INFO - QC Results: {'reads_total': '2002000', 'reads_mapped': '453459', 'reads_nodups': '22126', 'reads_cis_1kb': '14511', 'reads_cis_10kb': '8390'}
2025-06-06 23:06:42,323 - INFO - Pipeline completed successfully
2025-06-06 23:06:42,323 - INFO - Output files:
2025-06-06 23:06:42,323 - INFO -   Mapped pairs: benchmark_results/large-rcmc-benchmark_output/merge/large-rcmc-benchmark.mapped.pairs
2025-06-06 23:06:42,323 - INFO -   BAM file: benchmark_results/large-rcmc-benchmark_output/merge/large-rcmc-benchmark.bam
2025-06-06 23:06:42,323 - INFO -   HiC file: Failed to generate
2025-06-06 23:06:42,323 - INFO -   Raw mcool file: benchmark_results/large-rcmc-benchmark_output/cooler/large-rcmc-benchmark.raw.mcool
2025-06-06 23:06:42,323 - INFO -   Balanced mcool file: benchmark_results/large-rcmc-benchmark_output/cooler/large-rcmc-benchmark.balanced.mcool
2025-06-06 23:06:42,323 - INFO -   QC stats: {'reads_total': '2002000', 'reads_mapped': '453459', 'reads_nodups': '22126', 'reads_cis_1kb': '14511', 'reads_cis_10kb': '8390'}
2025-06-06 23:08:32,325 - INFO - Starting Micro-C pipeline for sample test-small
2025-06-06 23:08:32,325 - INFO - Total fastq size: 0 GB
2025-06-06 23:08:32,325 - INFO - Processing FASTQ pair 1/1
2025-06-06 23:08:32,326 - INFO - Starting Extract reference index
2025-06-06 23:08:32,326 - INFO - Command: tar zxvf tests/small-region-capture-micro-c/test_bwa_index.tgz -C test_output/align/chunk_0/genome_index
2025-06-06 23:08:32,351 - INFO - Completed Extract reference index in 0.03 seconds
2025-06-06 23:08:32,351 - INFO - Starting Find bwt file
2025-06-06 23:08:32,351 - INFO - Command: find test_output/align/chunk_0/genome_index -name '*.bwt'
2025-06-06 23:08:32,360 - INFO - Completed Find bwt file in 0.01 seconds
2025-06-06 23:08:32,360 - INFO - Starting Align and process small_rcmc_r1.fq.gz
2025-06-06 23:08:32,360 - INFO - Command: 
    bwa mem -5SP -T0 -t2 test_output/align/chunk_0/genome_index/test.fa.gz tests/small-region-capture-micro-c/small_rcmc_r1.fq.gz tests/small-region-capture-micro-c/small_rcmc_r2.fq.gz |     pairtools parse --min-mapq 20 --walks-policy 5unique     --max-inter-align-gap 30 --add-columns pos5,pos3,dist_to_5,dist_to_3,read_len     --nproc-in 2 --nproc-out 2 --chroms-path tests/small-region-capture-micro-c/test.chrom.sizes |     pairtools sort --nproc 2 -o test_output/align/chunk_0/small_rcmc_r1.fq.gz.pairsam.gz
    
2025-06-06 23:08:41,091 - INFO - Completed Align and process small_rcmc_r1.fq.gz in 8.73 seconds
2025-06-06 23:08:41,091 - INFO - Starting Merge, dedup, and split pairs
2025-06-06 23:08:41,091 - INFO - Command: 
    pairtools merge --nproc 12 test_output/align/chunk_0/small_rcmc_r1.fq.gz.pairsam.gz |     pairtools dedup --nproc-in 2 --nproc-out 8 --mark-dups --output-stats test_output/merge/test-small.stats.txt |     pairtools split --nproc-in 2 --nproc-out 8 --output-pairs test_output/merge/test-small.mapped.pairs --output-sam - |     samtools view -bS -@6 |     samtools sort -@6 -o test_output/merge/test-small.bam
    
2025-06-06 23:08:43,865 - INFO - Completed Merge, dedup, and split pairs in 2.77 seconds
2025-06-06 23:08:43,866 - INFO - Starting Index BAM
2025-06-06 23:08:43,866 - INFO - Command: samtools index test_output/merge/test-small.bam
2025-06-06 23:08:43,891 - INFO - Completed Index BAM in 0.03 seconds
2025-06-06 23:08:43,892 - INFO - Starting Generate cooler file
2025-06-06 23:08:43,892 - INFO - Command: cooler cload pairs -c1 2 -p1 3 -c2 4 -p2 5 tests/small-region-capture-micro-c/test.chrom.sizes:1000 test_output/merge/test-small.mapped.pairs test_output/cooler/test-small.cool
2025-06-06 23:08:45,090 - INFO - Completed Generate cooler file in 1.20 seconds
2025-06-06 23:08:45,090 - INFO - Starting Generate raw mcool file
2025-06-06 23:08:45,091 - INFO - Command: cooler zoomify --resolutions 1000N -o test_output/cooler/test-small.raw.mcool -p 4 test_output/cooler/test-small.cool
2025-06-06 23:08:46,438 - INFO - Completed Generate raw mcool file in 1.35 seconds
2025-06-06 23:08:46,438 - INFO - Starting Generate balanced mcool file
2025-06-06 23:08:46,438 - INFO - Command: cooler zoomify --resolutions 1000N -o test_output/cooler/test-small.balanced.mcool -p 4 --balance --balance-args '--nproc 4' test_output/cooler/test-small.cool
2025-06-06 23:08:49,596 - INFO - Completed Generate balanced mcool file in 3.16 seconds
2025-06-06 23:08:49,596 - INFO - Starting Convert pairs to Juicer format
2025-06-06 23:08:49,596 - INFO - Command: 
    awk 'BEGIN{OFS="\t"} /^#/{print} !/^#/{print $1,$2,$3,$4,$5,$6,$7,$8}' test_output/merge/test-small.mapped.pairs > test_output/hic/test-small.juicer.pairs
    
2025-06-06 23:08:49,745 - INFO - Completed Convert pairs to Juicer format in 0.15 seconds
2025-06-06 23:08:49,745 - INFO - Starting Generate Juicer HiC file
2025-06-06 23:08:49,745 - INFO - Command: 
    java -Xmx120g -Djava.awt.headless=true -jar /Users/<USER>/projects/topology-tools/juicer_tools/juicer_tools.jar pre         --threads 2         test_output/hic/test-small.juicer.pairs         test_output/hic/test-small.hic         tests/small-region-capture-micro-c/test.chrom.sizes
    
2025-06-06 23:08:50,144 - ERROR - Generate Juicer HiC file failed with return code 56
2025-06-06 23:08:50,144 - ERROR - STDERR: No mndIndex provided
Using single threaded preprocessor
java.lang.ArrayIndexOutOfBoundsException: Index 3 out of bounds for length 2
	at juicebox.tools.utils.original.mnditerator.ComplexLineParser.generateBasicPair(ComplexLineParser.java:56)
	at juicebox.tools.utils.original.mnditerator.MNDFileParser.parseDCICFormat(MNDFileParser.java:118)
	at juicebox.tools.utils.original.mnditerator.MNDFileParser.parse(MNDFileParser.java:83)
	at juicebox.tools.utils.original.mnditerator.GenericPairIterator.advance(GenericPairIterator.java:56)
	at juicebox.tools.utils.original.mnditerator.GenericPairIterator.next(GenericPairIterator.java:46)
	at juicebox.tools.utils.original.Preprocessor.computeWholeGenomeMatrix(Preprocessor.java:603)
	at juicebox.tools.utils.original.Preprocessor.writeBody(Preprocessor.java:690)
	at juicebox.tools.utils.original.Preprocessor.preprocess(Preprocessor.java:452)
	at juicebox.tools.clt.old.PreProcessing.run(PreProcessing.java:176)
	at juicebox.tools.HiCTools.main(HiCTools.java:97)

2025-06-06 23:08:50,144 - WARNING - Failed to generate HiC file: Generate Juicer HiC file failed
2025-06-06 23:08:50,144 - INFO - Starting Extract QC metrics
2025-06-06 23:08:50,144 - INFO - Command: 
    cat test_output/merge/test-small.stats.txt | grep -w "total" | cut -f2 > test_output/qc/test-small_qc.txt
    cat test_output/merge/test-small.stats.txt | grep -w "total_mapped" | cut -f2 >> test_output/qc/test-small_qc.txt
    cat test_output/merge/test-small.stats.txt | grep -w "total_nodups" | cut -f2 >> test_output/qc/test-small_qc.txt
    cat test_output/merge/test-small.stats.txt | grep -w "cis_1kb+" | cut -f2 >> test_output/qc/test-small_qc.txt
    cat test_output/merge/test-small.stats.txt | grep -w "cis_10kb+" | cut -f2 >> test_output/qc/test-small_qc.txt
    
2025-06-06 23:08:50,164 - INFO - Completed Extract QC metrics in 0.02 seconds
2025-06-06 23:08:50,165 - INFO - QC Results: {'reads_total': '100000', 'reads_mapped': '22646', 'reads_nodups': '22109', 'reads_cis_1kb': '14500', 'reads_cis_10kb': '8379'}
2025-06-06 23:08:50,165 - INFO - Pipeline completed successfully
2025-06-06 23:08:50,165 - INFO - Output files:
2025-06-06 23:08:50,165 - INFO -   Mapped pairs: test_output/merge/test-small.mapped.pairs
2025-06-06 23:08:50,165 - INFO -   BAM file: test_output/merge/test-small.bam
2025-06-06 23:08:50,165 - INFO -   HiC file: Failed to generate
2025-06-06 23:08:50,165 - INFO -   Raw mcool file: test_output/cooler/test-small.raw.mcool
2025-06-06 23:08:50,165 - INFO -   Balanced mcool file: test_output/cooler/test-small.balanced.mcool
2025-06-06 23:08:50,165 - INFO -   QC stats: {'reads_total': '100000', 'reads_mapped': '22646', 'reads_nodups': '22109', 'reads_cis_1kb': '14500', 'reads_cis_10kb': '8379'}
