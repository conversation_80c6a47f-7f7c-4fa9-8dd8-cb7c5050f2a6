{"sample_id": "demo-complete", "start_time": "2025-06-06T23:11:57.305264", "end_time": "2025-06-06T23:12:18.349897", "total_time_seconds": 21.044578790664673, "total_time_minutes": 0.3507429798444112, "success": true, "input_files": {"fastq_r1": ["tests/small-region-capture-micro-c/small_rcmc_r1.fq.gz"], "fastq_r2": ["tests/small-region-capture-micro-c/small_rcmc_r2.fq.gz"], "reference_bwa_idx": "tests/small-region-capture-micro-c/test_bwa_index.tgz", "chrom_sizes": "tests/small-region-capture-micro-c/test.chrom.sizes"}, "parameters": {"resolution": 1000, "bwa_cores": 2}, "output_directory": "demo_complete_output", "output_files": {"demo-complete.mapped.pairs": {"path": "demo_complete_output/merge/demo-complete.mapped.pairs", "size_mb": 3.2921886444091797, "exists": true}, "demo-complete.bam": {"path": "demo_complete_output/merge/demo-complete.bam", "size_mb": 3.759293556213379, "exists": true}, "demo-complete.bam.bai": {"path": "demo_complete_output/merge/demo-complete.bam.bai", "size_mb": 0.0036468505859375, "exists": true}, "demo-complete.cool": {"path": "demo_complete_output/cooler/demo-complete.cool", "size_mb": 0.0704660415649414, "exists": true}, "demo-complete.raw.mcool": {"path": "demo_complete_output/cooler/demo-complete.raw.mcool", "size_mb": 0.23025798797607422, "exists": true}, "demo-complete.balanced.mcool": {"path": "demo_complete_output/cooler/demo-complete.balanced.mcool", "size_mb": 0.26495838165283203, "exists": true}, "demo-complete_qc.txt": {"path": "demo_complete_output/qc/demo-complete_qc.txt", "size_mb": 2.86102294921875e-05, "exists": true}}}