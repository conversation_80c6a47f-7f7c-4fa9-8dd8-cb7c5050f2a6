2025-06-06 23:11:59,569 - INFO - Starting Micro-C pipeline for sample demo-complete
2025-06-06 23:11:59,569 - INFO - Total fastq size: 0 GB
2025-06-06 23:11:59,569 - INFO - Processing FASTQ pair 1/1
2025-06-06 23:11:59,569 - INFO - Starting Extract reference index
2025-06-06 23:11:59,570 - INFO - Command: tar zxvf tests/small-region-capture-micro-c/test_bwa_index.tgz -C demo_complete_output/align/chunk_0/genome_index
2025-06-06 23:11:59,596 - INFO - Completed Extract reference index in 0.03 seconds
2025-06-06 23:11:59,597 - INFO - Starting Find bwt file
2025-06-06 23:11:59,597 - INFO - Command: find demo_complete_output/align/chunk_0/genome_index -name '*.bwt'
2025-06-06 23:11:59,605 - INFO - Completed Find bwt file in 0.01 seconds
2025-06-06 23:11:59,605 - INFO - Starting Align and process small_rcmc_r1.fq.gz
2025-06-06 23:11:59,605 - INFO - Command: 
    bwa mem -5SP -T0 -t2 demo_complete_output/align/chunk_0/genome_index/test.fa.gz tests/small-region-capture-micro-c/small_rcmc_r1.fq.gz tests/small-region-capture-micro-c/small_rcmc_r2.fq.gz |     pairtools parse --min-mapq 20 --walks-policy 5unique     --max-inter-align-gap 30 --add-columns pos5,pos3,dist_to_5,dist_to_3,read_len     --nproc-in 2 --nproc-out 2 --chroms-path tests/small-region-capture-micro-c/test.chrom.sizes |     pairtools sort --nproc 2 -o demo_complete_output/align/chunk_0/small_rcmc_r1.fq.gz.pairsam.gz
    
2025-06-06 23:12:08,844 - INFO - Completed Align and process small_rcmc_r1.fq.gz in 9.24 seconds
2025-06-06 23:12:08,845 - INFO - Starting Merge, dedup, and split pairs
2025-06-06 23:12:08,845 - INFO - Command: 
    pairtools merge --nproc 12 demo_complete_output/align/chunk_0/small_rcmc_r1.fq.gz.pairsam.gz |     pairtools dedup --nproc-in 2 --nproc-out 8 --mark-dups --output-stats demo_complete_output/merge/demo-complete.stats.txt |     pairtools split --nproc-in 2 --nproc-out 8 --output-pairs demo_complete_output/merge/demo-complete.mapped.pairs --output-sam - |     samtools view -bS -@6 |     samtools sort -@6 -o demo_complete_output/merge/demo-complete.bam
    
2025-06-06 23:12:11,657 - INFO - Completed Merge, dedup, and split pairs in 2.81 seconds
2025-06-06 23:12:11,657 - INFO - Starting Index BAM
2025-06-06 23:12:11,657 - INFO - Command: samtools index demo_complete_output/merge/demo-complete.bam
2025-06-06 23:12:11,683 - INFO - Completed Index BAM in 0.03 seconds
2025-06-06 23:12:11,684 - INFO - Starting Generate cooler file
2025-06-06 23:12:11,684 - INFO - Command: cooler cload pairs -c1 2 -p1 3 -c2 4 -p2 5 tests/small-region-capture-micro-c/test.chrom.sizes:1000 demo_complete_output/merge/demo-complete.mapped.pairs demo_complete_output/cooler/demo-complete.cool
2025-06-06 23:12:13,039 - INFO - Completed Generate cooler file in 1.35 seconds
2025-06-06 23:12:13,039 - INFO - Starting Generate raw mcool file
2025-06-06 23:12:13,039 - INFO - Command: cooler zoomify --resolutions 1000N -o demo_complete_output/cooler/demo-complete.raw.mcool -p 4 demo_complete_output/cooler/demo-complete.cool
2025-06-06 23:12:14,365 - INFO - Completed Generate raw mcool file in 1.33 seconds
2025-06-06 23:12:14,365 - INFO - Starting Generate balanced mcool file
2025-06-06 23:12:14,365 - INFO - Command: cooler zoomify --resolutions 1000N -o demo_complete_output/cooler/demo-complete.balanced.mcool -p 4 --balance --balance-args '--nproc 4' demo_complete_output/cooler/demo-complete.cool
2025-06-06 23:12:17,621 - INFO - Completed Generate balanced mcool file in 3.26 seconds
2025-06-06 23:12:17,622 - INFO - Starting Convert pairs to Juicer format
2025-06-06 23:12:17,622 - INFO - Command: 
    awk 'BEGIN{OFS="\t"} /^#/{print} !/^#/{print $1,$2,$3,$4,$5,$6,$7,$8}' demo_complete_output/merge/demo-complete.mapped.pairs > demo_complete_output/hic/demo-complete.juicer.pairs
    
2025-06-06 23:12:17,771 - INFO - Completed Convert pairs to Juicer format in 0.15 seconds
2025-06-06 23:12:17,772 - INFO - Starting Generate Juicer HiC file
2025-06-06 23:12:17,772 - INFO - Command: 
    java -Xmx120g -Djava.awt.headless=true -jar /Users/<USER>/projects/topology-tools/juicer_tools/juicer_tools.jar pre         --threads 2         demo_complete_output/hic/demo-complete.juicer.pairs         demo_complete_output/hic/demo-complete.hic         tests/small-region-capture-micro-c/test.chrom.sizes
    
2025-06-06 23:12:18,323 - ERROR - Generate Juicer HiC file failed with return code 56
2025-06-06 23:12:18,323 - ERROR - STDERR: No mndIndex provided
Using single threaded preprocessor
java.lang.ArrayIndexOutOfBoundsException: Index 3 out of bounds for length 2
	at juicebox.tools.utils.original.mnditerator.ComplexLineParser.generateBasicPair(ComplexLineParser.java:56)
	at juicebox.tools.utils.original.mnditerator.MNDFileParser.parseDCICFormat(MNDFileParser.java:118)
	at juicebox.tools.utils.original.mnditerator.MNDFileParser.parse(MNDFileParser.java:83)
	at juicebox.tools.utils.original.mnditerator.GenericPairIterator.advance(GenericPairIterator.java:56)
	at juicebox.tools.utils.original.mnditerator.GenericPairIterator.next(GenericPairIterator.java:46)
	at juicebox.tools.utils.original.Preprocessor.computeWholeGenomeMatrix(Preprocessor.java:603)
	at juicebox.tools.utils.original.Preprocessor.writeBody(Preprocessor.java:690)
	at juicebox.tools.utils.original.Preprocessor.preprocess(Preprocessor.java:452)
	at juicebox.tools.clt.old.PreProcessing.run(PreProcessing.java:176)
	at juicebox.tools.HiCTools.main(HiCTools.java:97)

2025-06-06 23:12:18,323 - WARNING - Failed to generate HiC file: Generate Juicer HiC file failed
2025-06-06 23:12:18,324 - INFO - Starting Extract QC metrics
2025-06-06 23:12:18,324 - INFO - Command: 
    cat demo_complete_output/merge/demo-complete.stats.txt | grep -w "total" | cut -f2 > demo_complete_output/qc/demo-complete_qc.txt
    cat demo_complete_output/merge/demo-complete.stats.txt | grep -w "total_mapped" | cut -f2 >> demo_complete_output/qc/demo-complete_qc.txt
    cat demo_complete_output/merge/demo-complete.stats.txt | grep -w "total_nodups" | cut -f2 >> demo_complete_output/qc/demo-complete_qc.txt
    cat demo_complete_output/merge/demo-complete.stats.txt | grep -w "cis_1kb+" | cut -f2 >> demo_complete_output/qc/demo-complete_qc.txt
    cat demo_complete_output/merge/demo-complete.stats.txt | grep -w "cis_10kb+" | cut -f2 >> demo_complete_output/qc/demo-complete_qc.txt
    
2025-06-06 23:12:18,345 - INFO - Completed Extract QC metrics in 0.02 seconds
2025-06-06 23:12:18,345 - INFO - QC Results: {'reads_total': '100000', 'reads_mapped': '22646', 'reads_nodups': '22109', 'reads_cis_1kb': '14500', 'reads_cis_10kb': '8379'}
2025-06-06 23:12:18,345 - INFO - Pipeline completed successfully
2025-06-06 23:12:18,345 - INFO - Output files:
2025-06-06 23:12:18,345 - INFO -   Mapped pairs: demo_complete_output/merge/demo-complete.mapped.pairs
2025-06-06 23:12:18,345 - INFO -   BAM file: demo_complete_output/merge/demo-complete.bam
2025-06-06 23:12:18,345 - INFO -   HiC file: Failed to generate
2025-06-06 23:12:18,345 - INFO -   Raw mcool file: demo_complete_output/cooler/demo-complete.raw.mcool
2025-06-06 23:12:18,345 - INFO -   Balanced mcool file: demo_complete_output/cooler/demo-complete.balanced.mcool
2025-06-06 23:12:18,345 - INFO -   QC stats: {'reads_total': '100000', 'reads_mapped': '22646', 'reads_nodups': '22109', 'reads_cis_1kb': '14500', 'reads_cis_10kb': '8379'}
