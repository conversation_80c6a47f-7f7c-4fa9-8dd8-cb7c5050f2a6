Jun-09 08:04:38.619 [main] DEBUG nextflow.cli.Launcher - $> nextflow run nf/main.nf -profile test,docker
Jun-09 08:04:38.669 [main] DEBUG nextflow.cli.CmdRun - N E X T F L O W  ~  version 25.04.3
Jun-09 08:04:38.690 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/Users/<USER>/.nextflow/plugins; core-plugins: nf-amazon@2.15.0,nf-azure@1.16.0,nf-cloudcache@0.4.3,nf-codecommit@0.2.3,nf-console@1.2.1,nf-google@1.21.0,nf-k8s@1.0.0,nf-tower@1.11.3,nf-wave@1.12.1
Jun-09 08:04:38.712 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
Jun-09 08:04:38.713 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
Jun-09 08:04:38.714 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.12.0 in 'deployment' mode
Jun-09 08:04:38.721 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
Jun-09 08:04:38.733 [main] DEBUG nextflow.config.ConfigBuilder - Found config base: /Users/<USER>/projects/topology-tools/nf/nextflow.config
Jun-09 08:04:38.735 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /Users/<USER>/projects/topology-tools/nf/nextflow.config
Jun-09 08:04:38.755 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /Users/<USER>/.nextflow/secrets/store.json
Jun-09 08:04:38.757 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@30404dba] - activable => nextflow.secret.LocalSecretsProvider@30404dba
Jun-09 08:04:38.760 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `test,docker`
Jun-09 08:04:39.337 [main] DEBUG nextflow.config.ConfigBuilder - Available config profiles: [cluster, debug, test, local, docker, cloud, shifter, mamba, charliecloud, conda, singularity, arm, test_full, podman]
Jun-09 08:04:39.355 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declaration
Jun-09 08:04:39.366 [main] DEBUG nextflow.cli.CmdRun - Launching `nf/main.nf` [crazy_cuvier] DSL2 - revision: 7024aca753
Jun-09 08:04:39.366 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
Jun-09 08:04:39.367 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[]
Jun-09 08:04:39.396 [main] DEBUG nextflow.Session - Session UUID: 4d7b3666-6079-4bda-9f27-e49fd82b071f
Jun-09 08:04:39.397 [main] DEBUG nextflow.Session - Run name: crazy_cuvier
Jun-09 08:04:39.397 [main] DEBUG nextflow.Session - Executor pool size: 10
Jun-09 08:04:39.401 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
Jun-09 08:04:39.405 [main] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'FileTransfer' minSize=10; maxSize=30; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jun-09 08:04:39.420 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 25.04.3 build 5949
  Created: 02-06-2025 20:56 UTC (16:56 EDT)
  System: Mac OS X 15.4.1
  Runtime: Groovy 4.0.26 on OpenJDK 64-Bit Server VM 23.0.2+7
  Encoding: UTF-8 (UTF-8)
  Process: <EMAIL> [127.0.0.1]
  CPUs: 10 - Mem: 64 GB (996.1 MB) - Swap: 7 GB (1.4 GB)
Jun-09 08:04:39.428 [main] DEBUG nextflow.Session - Work-dir: /Users/<USER>/projects/topology-tools/work [Mac OS X]
Jun-09 08:04:39.429 [main] DEBUG nextflow.Session - Script base path does not exist or is not a directory: /Users/<USER>/projects/topology-tools/nf/bin
Jun-09 08:04:39.435 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
Jun-09 08:04:39.440 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
Jun-09 08:04:39.453 [main] DEBUG nextflow.Session - Observer factory (v2): LinObserverFactory
Jun-09 08:04:39.476 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
Jun-09 08:04:39.482 [main] DEBUG nextflow.util.CustomThreadPool - Creating default thread pool > poolSize: 11; maxThreads: 1000
Jun-09 08:04:39.515 [main] DEBUG nextflow.Session - Session start
Jun-09 08:04:39.517 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow started -- trace file: /Users/<USER>/projects/topology-tools/test_results/pipeline_info/execution_trace_2025-06-09_08-04-39.txt
Jun-09 08:04:39.649 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
Jun-09 08:04:39.963 [main] INFO  nextflow.Nextflow - 
========================================================================================
                        Micro-C Pipeline - Nextflow DSL2
========================================================================================
Sample ID       : test-sample
FASTQ R1        : ../tests/small-region-capture-micro-c/small_rcmc_r1.fq.gz
FASTQ R2        : ../tests/small-region-capture-micro-c/small_rcmc_r2.fq.gz
BWA Index       : ../tests/small-region-capture-micro-c/test_bwa_index.tgz
Chrom Sizes     : ../tests/small-region-capture-micro-c/test.chrom.sizes
Output Dir      : ./test_results
Resolution      : 1000
BWA Cores       : 2
Min MAPQ        : 20
========================================================================================

Jun-09 08:04:40.052 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name EXTRACT_BWA_INDEX
Jun-09 08:04:40.054 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:EXTRACT_BWA_INDEX` matches process EXTRACT_BWA_INDEX
Jun-09 08:04:40.062 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:04:40.062 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:04:40.066 [main] DEBUG nextflow.executor.Executor - [warm up] executor > local
Jun-09 08:04:40.069 [main] DEBUG n.processor.LocalPollingMonitor - Creating local task monitor for executor 'local' > cpus=10; memory=64 GB; capacity=10; pollInterval=100ms; dumpInterval=5m
Jun-09 08:04:40.071 [main] DEBUG n.processor.TaskPollingMonitor - >>> barrier register (monitor: local)
Jun-09 08:04:40.082 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'EXTRACT_BWA_INDEX': maxForks=0; fair=false; array=0
Jun-09 08:04:40.104 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name BWA_ALIGN_PAIRTOOLS
Jun-09 08:04:40.104 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:BWA_ALIGN_PAIRTOOLS` matches process BWA_ALIGN_PAIRTOOLS
Jun-09 08:04:40.105 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:04:40.106 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:04:40.106 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'BWA_ALIGN_PAIRTOOLS': maxForks=0; fair=false; array=0
Jun-09 08:04:40.115 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name MERGE_DEDUP_SPLIT
Jun-09 08:04:40.115 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:MERGE_DEDUP_SPLIT` matches process MERGE_DEDUP_SPLIT
Jun-09 08:04:40.117 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:04:40.117 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:04:40.117 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'MERGE_DEDUP_SPLIT': maxForks=0; fair=false; array=0
Jun-09 08:04:40.122 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name COOLER_PROCESS
Jun-09 08:04:40.122 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:COOLER_PROCESS` matches process COOLER_PROCESS
Jun-09 08:04:40.123 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:04:40.123 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:04:40.124 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'COOLER_PROCESS': maxForks=0; fair=false; array=0
Jun-09 08:04:40.130 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name JUICER_HIC
Jun-09 08:04:40.130 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:JUICER_HIC` matches process JUICER_HIC
Jun-09 08:04:40.131 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:04:40.131 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:04:40.132 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'JUICER_HIC': maxForks=0; fair=false; array=0
Jun-09 08:04:40.136 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name QC_METRICS
Jun-09 08:04:40.136 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:QC_METRICS` matches process QC_METRICS
Jun-09 08:04:40.138 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:04:40.138 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:04:40.139 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'QC_METRICS': maxForks=0; fair=false; array=0
Jun-09 08:04:40.141 [main] DEBUG nextflow.Session - Workflow process names [dsl2]: MERGE_DEDUP_SPLIT, COOLER_PROCESS, EXTRACT_BWA_INDEX, JUICER_HIC, BWA_ALIGN_PAIRTOOLS, QC_METRICS
Jun-09 08:04:40.143 [main] DEBUG nextflow.Session - Igniting dataflow network (10)
Jun-09 08:04:40.148 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > EXTRACT_BWA_INDEX
Jun-09 08:04:40.148 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > BWA_ALIGN_PAIRTOOLS
Jun-09 08:04:40.148 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > MERGE_DEDUP_SPLIT
Jun-09 08:04:40.148 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > COOLER_PROCESS
Jun-09 08:04:40.148 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > JUICER_HIC
Jun-09 08:04:40.148 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > QC_METRICS
Jun-09 08:04:40.148 [main] DEBUG nextflow.script.ScriptRunner - Parsed script files:
  Script_43f01a504ba15b5e: /Users/<USER>/projects/topology-tools/nf/modules/merge_dedup_split.nf
  Script_6c968723c735dde5: /Users/<USER>/projects/topology-tools/nf/modules/qc_metrics.nf
  Script_900e8f290a0d3a82: /Users/<USER>/projects/topology-tools/nf/modules/cooler_process.nf
  Script_a457b5df1e6a90ec: /Users/<USER>/projects/topology-tools/nf/modules/bwa_align_pairtools.nf
  Script_3c186acb8ea2eb2c: /Users/<USER>/projects/topology-tools/nf/modules/juicer_hic.nf
  Script_76840e3747540450: /Users/<USER>/projects/topology-tools/nf/main.nf
  Script_50236dab2297e7d7: /Users/<USER>/projects/topology-tools/nf/modules/extract_bwa_index.nf
Jun-09 08:04:40.148 [main] DEBUG nextflow.script.ScriptRunner - > Awaiting termination 
Jun-09 08:04:40.148 [main] DEBUG nextflow.Session - Session await
Jun-09 08:04:40.172 [PathVisitor-2] ERROR nextflow.Channel - No signature of method: [Ljava.lang.String;.getFileSystem() is applicable for argument types: () values: []
org.codehaus.groovy.runtime.metaclass.MissingMethodExceptionNoStack: No signature of method: [Ljava.lang.String;.getFileSystem() is applicable for argument types: () values: []
Jun-09 08:04:40.172 [PathVisitor-1] ERROR nextflow.Channel - No signature of method: [Ljava.lang.String;.getFileSystem() is applicable for argument types: () values: []
org.codehaus.groovy.runtime.metaclass.MissingMethodExceptionNoStack: No signature of method: [Ljava.lang.String;.getFileSystem() is applicable for argument types: () values: []
Jun-09 08:04:40.188 [PathVisitor-2] DEBUG nextflow.Session - Session aborted -- Cause: No signature of method: [Ljava.lang.String;.getFileSystem() is applicable for argument types: () values: []
Jun-09 08:04:40.191 [PathVisitor-1] DEBUG nextflow.Session - Session aborted -- Cause: No signature of method: [Ljava.lang.String;.getFileSystem() is applicable for argument types: () values: []
Jun-09 08:04:40.223 [PathVisitor-1] DEBUG nextflow.Session - The following nodes are still active:
[process] EXTRACT_BWA_INDEX
  status=ACTIVE
  port 0: (queue) OPEN  ; channel: chunk_id
  port 1: (value) bound ; channel: bwa_index_tar
  port 2: (cntrl) -     ; channel: $

[process] BWA_ALIGN_PAIRTOOLS
  status=ACTIVE
  port 0: (queue) OPEN  ; channel: -
  port 1: (queue) OPEN  ; channel: -
  port 2: (value) bound ; channel: chrom_sizes
  port 3: (cntrl) -     ; channel: $

[process] MERGE_DEDUP_SPLIT
  status=ACTIVE
  port 0: (value) OPEN  ; channel: -
  port 1: (cntrl) -     ; channel: $

[process] COOLER_PROCESS
  status=ACTIVE
  port 0: (value) OPEN  ; channel: -
  port 1: (value) bound ; channel: chrom_sizes
  port 2: (cntrl) -     ; channel: $

[process] JUICER_HIC
  status=ACTIVE
  port 0: (value) OPEN  ; channel: -
  port 1: (value) bound ; channel: chrom_sizes
  port 2: (cntrl) -     ; channel: $

[process] QC_METRICS
  status=ACTIVE
  port 0: (value) OPEN  ; channel: -
  port 1: (cntrl) -     ; channel: $

Jun-09 08:04:40.225 [PathVisitor-2] DEBUG nextflow.Session - The following nodes are still active:
[process] EXTRACT_BWA_INDEX
  status=ACTIVE
  port 0: (queue) OPEN  ; channel: chunk_id
  port 1: (value) bound ; channel: bwa_index_tar
  port 2: (cntrl) -     ; channel: $

[process] BWA_ALIGN_PAIRTOOLS
  status=ACTIVE
  port 0: (queue) OPEN  ; channel: -
  port 1: (queue) OPEN  ; channel: -
  port 2: (value) bound ; channel: chrom_sizes
  port 3: (cntrl) -     ; channel: $

[process] MERGE_DEDUP_SPLIT
  status=ACTIVE
  port 0: (value) OPEN  ; channel: -
  port 1: (cntrl) -     ; channel: $

[process] COOLER_PROCESS
  status=ACTIVE
  port 0: (value) OPEN  ; channel: -
  port 1: (value) bound ; channel: chrom_sizes
  port 2: (cntrl) -     ; channel: $

[process] JUICER_HIC
  status=ACTIVE
  port 0: (value) OPEN  ; channel: -
  port 1: (value) bound ; channel: chrom_sizes
  port 2: (cntrl) -     ; channel: $

[process] QC_METRICS
  status=ACTIVE
  port 0: (value) OPEN  ; channel: -
  port 1: (cntrl) -     ; channel: $

Jun-09 08:04:40.227 [PathVisitor-1] ERROR nextflow.Nextflow - Pipeline execution stopped with the following message: No signature of method: [Ljava.lang.String;.getFileSystem() is applicable for argument types: () values: []
Jun-09 08:04:40.227 [PathVisitor-2] ERROR nextflow.Nextflow - Pipeline execution stopped with the following message: No signature of method: [Ljava.lang.String;.getFileSystem() is applicable for argument types: () values: []
Jun-09 08:04:40.227 [main] DEBUG nextflow.Session - Session await > all processes finished
Jun-09 08:04:40.228 [main] DEBUG nextflow.Session - Session await > all barriers passed
Jun-09 08:04:40.228 [Task monitor] DEBUG n.processor.TaskPollingMonitor - <<< barrier arrives (monitor: local) - terminating tasks monitor poll loop
Jun-09 08:04:40.229 [main] INFO  nextflow.Nextflow - 
========================================================================================
                        Pipeline Execution Summary
========================================================================================
Completed at    : 2025-06-09T08:04:40.228165-04:00
Duration        : 769ms
Success         : false
Work directory  : /Users/<USER>/projects/topology-tools/work
Exit status     : null
Error message   : No signature of method: [Ljava.lang.String;.getFileSystem() is applicable for argument types: () values: []
========================================================================================

Jun-09 08:04:40.229 [main] ERROR nextflow.Nextflow - 💥 Pipeline failed!
Jun-09 08:04:40.230 [main] ERROR nextflow.Nextflow - Check the error message above and workflow logs for details.
Jun-09 08:04:40.232 [main] DEBUG n.trace.WorkflowStatsObserver - Workflow completed > WorkflowStats[succeededCount=0; failedCount=0; ignoredCount=0; cachedCount=0; pendingCount=0; submittedCount=0; runningCount=0; retriesCount=0; abortedCount=0; succeedDuration=0ms; failedDuration=0ms; cachedDuration=0ms;loadCpus=0; loadMemory=0; peakRunning=0; peakCpus=0; peakMemory=0; ]
Jun-09 08:04:40.232 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow completed -- saving trace file
Jun-09 08:04:40.233 [main] DEBUG nextflow.trace.ReportObserver - Workflow completed -- rendering execution report
Jun-09 08:04:41.078 [main] DEBUG nextflow.trace.TimelineObserver - Workflow completed -- rendering execution timeline
Jun-09 08:04:41.175 [main] WARN  nextflow.dag.GraphvizRenderer - Graphviz is required to render the execution DAG in the given format -- See http://www.graphviz.org for more info.
Jun-09 08:04:41.178 [main] DEBUG nextflow.cache.CacheDB - Closing CacheDB done
Jun-09 08:04:41.187 [main] DEBUG nextflow.script.ScriptRunner - > Execution complete -- Goodbye
