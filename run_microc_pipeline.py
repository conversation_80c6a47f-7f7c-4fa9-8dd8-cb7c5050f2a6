#!/usr/bin/env python3
"""
Comprehensive wrapper script for the Micro-C pipeline.
Includes validation, execution, monitoring, and reporting.
"""

import os
import sys
import argparse
import subprocess
import time
import threading
import json
from datetime import datetime
from pathlib import Path

class PipelineRunner:
    def __init__(self, args):
        self.args = args
        self.start_time = time.time()
        self.pipeline_process = None
        self.monitor_process = None
        
    def validate_configuration(self):
        """Run configuration validation"""
        print("🔍 Step 1: Validating configuration...")
        
        validation_cmd = [
            'python', 'validate_config.py',
            '--sample_id', self.args.sample_id,
            '--fastq_r1'] + self.args.fastq_r1 + [
            '--fastq_r2'] + self.args.fastq_r2 + [
            '--reference_bwa_idx', self.args.reference_bwa_idx,
            '--chrom_sizes', self.args.chrom_sizes,
            '--output_dir', self.args.output_dir,
            '--resolution', str(self.args.resolution),
            '--bwa_cores', str(self.args.bwa_cores)
        ]
        
        try:
            result = subprocess.run(validation_cmd, check=True)
            print("✅ Configuration validation passed")
            return True
        except subprocess.CalledProcessError as e:
            print(f"❌ Configuration validation failed with return code {e.returncode}")
            return False
        except Exception as e:
            print(f"❌ Error running validation: {e}")
            return False
    
    def start_monitoring(self):
        """Start pipeline monitoring in background"""
        if not self.args.no_monitor:
            print("📊 Starting pipeline monitoring...")
            
            monitor_cmd = [
                'python', 'pipeline_monitor.py',
                '--output_dir', self.args.output_dir,
                '--sample_id', self.args.sample_id
            ]
            
            try:
                # Start monitor in background
                self.monitor_process = subprocess.Popen(
                    monitor_cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE
                )
                time.sleep(2)  # Give monitor time to start
                return True
            except Exception as e:
                print(f"⚠️  Could not start monitoring: {e}")
                return False
        return True
    
    def run_pipeline(self):
        """Run the main pipeline"""
        print("🧬 Step 2: Running Micro-C pipeline...")
        
        pipeline_cmd = [
            'python', 'microc_pipeline.py',
            '--sample_id', self.args.sample_id,
            '--fastq_r1'] + self.args.fastq_r1 + [
            '--fastq_r2'] + self.args.fastq_r2 + [
            '--reference_bwa_idx', self.args.reference_bwa_idx,
            '--chrom_sizes', self.args.chrom_sizes,
            '--output_dir', self.args.output_dir,
            '--resolution', str(self.args.resolution),
            '--bwa_cores', str(self.args.bwa_cores)
        ]
        
        print(f"🚀 Command: {' '.join(pipeline_cmd)}")
        
        try:
            # Run pipeline
            self.pipeline_process = subprocess.Popen(
                pipeline_cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            # Stream output in real-time
            log_file = os.path.join(self.args.output_dir, f"{self.args.sample_id}_pipeline.log")
            with open(log_file, 'w') as log_f:
                for line in iter(self.pipeline_process.stdout.readline, ''):
                    if line:
                        print(line.rstrip())
                        log_f.write(line)
                        log_f.flush()
            
            # Wait for completion
            return_code = self.pipeline_process.wait()
            
            if return_code == 0:
                print("✅ Pipeline completed successfully")
                return True
            else:
                print(f"❌ Pipeline failed with return code {return_code}")
                return False
                
        except Exception as e:
            print(f"❌ Error running pipeline: {e}")
            return False
    
    def stop_monitoring(self):
        """Stop monitoring process"""
        if self.monitor_process:
            try:
                self.monitor_process.terminate()
                self.monitor_process.wait(timeout=10)
            except:
                try:
                    self.monitor_process.kill()
                except:
                    pass
    
    def generate_report(self, success):
        """Generate final report"""
        print("📋 Step 3: Generating final report...")
        
        total_time = time.time() - self.start_time
        
        report = {
            'sample_id': self.args.sample_id,
            'start_time': datetime.fromtimestamp(self.start_time).isoformat(),
            'end_time': datetime.now().isoformat(),
            'total_time_seconds': total_time,
            'total_time_minutes': total_time / 60,
            'success': success,
            'input_files': {
                'fastq_r1': self.args.fastq_r1,
                'fastq_r2': self.args.fastq_r2,
                'reference_bwa_idx': self.args.reference_bwa_idx,
                'chrom_sizes': self.args.chrom_sizes
            },
            'parameters': {
                'resolution': self.args.resolution,
                'bwa_cores': self.args.bwa_cores
            },
            'output_directory': self.args.output_dir
        }
        
        # Check output files
        output_files = {}
        expected_files = [
            ('merge', f'{self.args.sample_id}.mapped.pairs'),
            ('merge', f'{self.args.sample_id}.bam'),
            ('merge', f'{self.args.sample_id}.bam.bai'),
            ('cooler', f'{self.args.sample_id}.cool'),
            ('cooler', f'{self.args.sample_id}.raw.mcool'),
            ('cooler', f'{self.args.sample_id}.balanced.mcool'),
            ('qc', f'{self.args.sample_id}_qc.txt')
        ]
        
        for subdir, filename in expected_files:
            filepath = os.path.join(self.args.output_dir, subdir, filename)
            if os.path.exists(filepath):
                size_mb = os.path.getsize(filepath) / (1024**2)
                output_files[filename] = {
                    'path': filepath,
                    'size_mb': size_mb,
                    'exists': True
                }
            else:
                output_files[filename] = {
                    'path': filepath,
                    'size_mb': 0,
                    'exists': False
                }
        
        report['output_files'] = output_files
        
        # Save report
        report_file = os.path.join(self.args.output_dir, f"{self.args.sample_id}_final_report.json")
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        # Print summary
        print("\n" + "="*80)
        print("📋 PIPELINE EXECUTION SUMMARY")
        print("="*80)
        print(f"Sample ID: {self.args.sample_id}")
        print(f"Total time: {total_time/60:.2f} minutes")
        print(f"Status: {'✅ SUCCESS' if success else '❌ FAILED'}")
        
        print(f"\n📁 Output files:")
        for filename, info in output_files.items():
            status = "✅" if info['exists'] else "❌"
            size_str = f"({info['size_mb']:.1f} MB)" if info['exists'] else "(missing)"
            print(f"   {status} {filename} {size_str}")
        
        print(f"\n📊 Report saved to: {report_file}")
        
        if success:
            print("\n🎉 Pipeline completed successfully!")
            print("\nNext steps:")
            print("1. Review QC metrics in the output directory")
            print("2. Load contact matrices into visualization tools")
            print("3. Perform downstream analysis")
        else:
            print("\n💥 Pipeline failed. Check logs for details:")
            log_file = os.path.join(self.args.output_dir, f"{self.args.sample_id}_pipeline.log")
            if os.path.exists(log_file):
                print(f"   Pipeline log: {log_file}")
            
            monitor_log = os.path.join(self.args.output_dir, f"{self.args.sample_id}_monitor.log")
            if os.path.exists(monitor_log):
                print(f"   Monitor log: {monitor_log}")
        
        print("="*80)
    
    def run(self):
        """Main execution method"""
        print("🧬 MICRO-C PIPELINE RUNNER")
        print("="*50)
        print(f"Sample: {self.args.sample_id}")
        print(f"Output: {self.args.output_dir}")
        print("="*50)
        
        try:
            # Step 1: Validate configuration
            if not self.args.skip_validation:
                if not self.validate_configuration():
                    print("❌ Validation failed. Use --skip-validation to bypass.")
                    return False
            else:
                print("⚠️  Skipping validation (--skip-validation)")
            
            # Step 2: Start monitoring
            self.start_monitoring()
            
            # Step 3: Run pipeline
            success = self.run_pipeline()
            
            # Step 4: Stop monitoring
            self.stop_monitoring()
            
            # Step 5: Generate report
            self.generate_report(success)
            
            return success
            
        except KeyboardInterrupt:
            print("\n\n🛑 Pipeline interrupted by user")
            
            # Clean up processes
            if self.pipeline_process:
                self.pipeline_process.terminate()
            self.stop_monitoring()
            
            self.generate_report(False)
            return False
        
        except Exception as e:
            print(f"\n❌ Unexpected error: {e}")
            self.stop_monitoring()
            self.generate_report(False)
            return False

def main():
    parser = argparse.ArgumentParser(
        description="Comprehensive Micro-C pipeline runner with validation and monitoring",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Basic usage
  python run_microc_pipeline.py --sample_id my_sample \\
    --fastq_r1 reads_R1.fq.gz --fastq_r2 reads_R2.fq.gz \\
    --reference_bwa_idx genome.tgz --chrom_sizes genome.sizes \\
    --output_dir output

  # High-performance run
  python run_microc_pipeline.py --sample_id large_sample \\
    --fastq_r1 reads_R1.fq.gz --fastq_r2 reads_R2.fq.gz \\
    --reference_bwa_idx genome.tgz --chrom_sizes genome.sizes \\
    --output_dir output --bwa_cores 16 --resolution 5000

  # Skip validation and monitoring
  python run_microc_pipeline.py --sample_id test \\
    --fastq_r1 reads_R1.fq.gz --fastq_r2 reads_R2.fq.gz \\
    --reference_bwa_idx genome.tgz --chrom_sizes genome.sizes \\
    --output_dir output --skip-validation --no-monitor
        """
    )
    
    # Required arguments
    parser.add_argument("--sample_id", required=True, help="Sample identifier")
    parser.add_argument("--fastq_r1", nargs='+', required=True, help="R1 FASTQ files")
    parser.add_argument("--fastq_r2", nargs='+', required=True, help="R2 FASTQ files")
    parser.add_argument("--reference_bwa_idx", required=True, help="BWA index tar.gz file")
    parser.add_argument("--chrom_sizes", required=True, help="Chromosome sizes file")
    parser.add_argument("--output_dir", required=True, help="Output directory")
    
    # Optional arguments
    parser.add_argument("--resolution", type=int, default=1000, help="Resolution in bp (default: 1000)")
    parser.add_argument("--bwa_cores", type=int, default=2, help="Number of BWA cores (default: 2)")
    
    # Control arguments
    parser.add_argument("--skip-validation", action="store_true", 
                       help="Skip configuration validation")
    parser.add_argument("--no-monitor", action="store_true",
                       help="Disable real-time monitoring")
    
    args = parser.parse_args()
    
    runner = PipelineRunner(args)
    success = runner.run()
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
