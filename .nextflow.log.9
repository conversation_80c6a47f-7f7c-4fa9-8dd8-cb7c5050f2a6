Jun-09 08:09:35.181 [main] DEBUG nextflow.cli.Launcher - $> nextflow run nf/main.nf -profile test
Jun-09 08:09:35.235 [main] DEBUG nextflow.cli.CmdRun - N E X T F L O W  ~  version 25.04.3
Jun-09 08:09:35.258 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/Users/<USER>/.nextflow/plugins; core-plugins: nf-amazon@2.15.0,nf-azure@1.16.0,nf-cloudcache@0.4.3,nf-codecommit@0.2.3,nf-console@1.2.1,nf-google@1.21.0,nf-k8s@1.0.0,nf-tower@1.11.3,nf-wave@1.12.1
Jun-09 08:09:35.281 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
Jun-09 08:09:35.282 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
Jun-09 08:09:35.285 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.12.0 in 'deployment' mode
Jun-09 08:09:35.293 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
Jun-09 08:09:35.306 [main] DEBUG nextflow.config.ConfigBuilder - Found config base: /Users/<USER>/projects/topology-tools/nf/nextflow.config
Jun-09 08:09:35.308 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /Users/<USER>/projects/topology-tools/nf/nextflow.config
Jun-09 08:09:35.330 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /Users/<USER>/.nextflow/secrets/store.json
Jun-09 08:09:35.333 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@73877e19] - activable => nextflow.secret.LocalSecretsProvider@73877e19
Jun-09 08:09:35.336 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `test`
Jun-09 08:09:35.987 [main] DEBUG nextflow.config.ConfigBuilder - Available config profiles: [cluster, debug, test, local, docker, cloud, shifter, mamba, charliecloud, conda, singularity, arm, test_full, podman]
Jun-09 08:09:36.008 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declaration
Jun-09 08:09:36.019 [main] DEBUG nextflow.cli.CmdRun - Launching `nf/main.nf` [sleepy_wing] DSL2 - revision: 26f924d8d5
Jun-09 08:09:36.020 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
Jun-09 08:09:36.020 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[]
Jun-09 08:09:36.053 [main] DEBUG nextflow.Session - Session UUID: 43c40017-7d36-4aa9-8b59-d30793300790
Jun-09 08:09:36.054 [main] DEBUG nextflow.Session - Run name: sleepy_wing
Jun-09 08:09:36.054 [main] DEBUG nextflow.Session - Executor pool size: 10
Jun-09 08:09:36.060 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
Jun-09 08:09:36.063 [main] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'FileTransfer' minSize=10; maxSize=30; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jun-09 08:09:36.078 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 25.04.3 build 5949
  Created: 02-06-2025 20:56 UTC (16:56 EDT)
  System: Mac OS X 15.4.1
  Runtime: Groovy 4.0.26 on OpenJDK 64-Bit Server VM 23.0.2+7
  Encoding: UTF-8 (UTF-8)
  Process: <EMAIL> [127.0.0.1]
  CPUs: 10 - Mem: 64 GB (269.1 MB) - Swap: 7 GB (1.4 GB)
Jun-09 08:09:36.087 [main] DEBUG nextflow.Session - Work-dir: /Users/<USER>/projects/topology-tools/work [Mac OS X]
Jun-09 08:09:36.087 [main] DEBUG nextflow.Session - Script base path does not exist or is not a directory: /Users/<USER>/projects/topology-tools/nf/bin
Jun-09 08:09:36.095 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
Jun-09 08:09:36.100 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
Jun-09 08:09:36.116 [main] DEBUG nextflow.Session - Observer factory (v2): LinObserverFactory
Jun-09 08:09:36.140 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
Jun-09 08:09:36.147 [main] DEBUG nextflow.util.CustomThreadPool - Creating default thread pool > poolSize: 11; maxThreads: 1000
Jun-09 08:09:36.182 [main] DEBUG nextflow.Session - Session start
Jun-09 08:09:36.184 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow started -- trace file: /Users/<USER>/projects/topology-tools/test_results/pipeline_info/execution_trace_2025-06-09_08-09-35.txt
Jun-09 08:09:36.342 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
Jun-09 08:09:36.673 [main] INFO  nextflow.Nextflow - 
========================================================================================
                        Micro-C Pipeline - Nextflow DSL2
========================================================================================
Sample ID       : test-sample
FASTQ R1        : ../tests/small-region-capture-micro-c/small_rcmc_r1.fq.gz
FASTQ R2        : ../tests/small-region-capture-micro-c/small_rcmc_r2.fq.gz
BWA Index       : ../tests/small-region-capture-micro-c/test_bwa_index.tgz
Chrom Sizes     : ../tests/small-region-capture-micro-c/test.chrom.sizes
Output Dir      : ./test_results
Resolution      : 1000
BWA Cores       : 2
Min MAPQ        : 20
========================================================================================

Jun-09 08:09:36.774 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name EXTRACT_BWA_INDEX
Jun-09 08:09:36.776 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:EXTRACT_BWA_INDEX` matches process EXTRACT_BWA_INDEX
Jun-09 08:09:36.787 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:09:36.787 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:09:36.792 [main] DEBUG nextflow.executor.Executor - [warm up] executor > local
Jun-09 08:09:36.796 [main] DEBUG n.processor.LocalPollingMonitor - Creating local task monitor for executor 'local' > cpus=10; memory=64 GB; capacity=10; pollInterval=100ms; dumpInterval=5m
Jun-09 08:09:36.798 [main] DEBUG n.processor.TaskPollingMonitor - >>> barrier register (monitor: local)
Jun-09 08:09:36.815 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'EXTRACT_BWA_INDEX': maxForks=0; fair=false; array=0
Jun-09 08:09:36.844 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name BWA_ALIGN_PAIRTOOLS
Jun-09 08:09:36.844 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:BWA_ALIGN_PAIRTOOLS` matches process BWA_ALIGN_PAIRTOOLS
Jun-09 08:09:36.846 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:09:36.846 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:09:36.847 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'BWA_ALIGN_PAIRTOOLS': maxForks=0; fair=false; array=0
Jun-09 08:09:36.859 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name MERGE_DEDUP_SPLIT
Jun-09 08:09:36.859 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:MERGE_DEDUP_SPLIT` matches process MERGE_DEDUP_SPLIT
Jun-09 08:09:36.861 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:09:36.861 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:09:36.861 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'MERGE_DEDUP_SPLIT': maxForks=0; fair=false; array=0
Jun-09 08:09:36.868 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name COOLER_PROCESS
Jun-09 08:09:36.869 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:COOLER_PROCESS` matches process COOLER_PROCESS
Jun-09 08:09:36.871 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:09:36.871 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:09:36.871 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'COOLER_PROCESS': maxForks=0; fair=false; array=0
Jun-09 08:09:36.879 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name JUICER_HIC
Jun-09 08:09:36.879 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:JUICER_HIC` matches process JUICER_HIC
Jun-09 08:09:36.881 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:09:36.881 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:09:36.882 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'JUICER_HIC': maxForks=0; fair=false; array=0
Jun-09 08:09:36.887 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name QC_METRICS
Jun-09 08:09:36.887 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:QC_METRICS` matches process QC_METRICS
Jun-09 08:09:36.888 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:09:36.889 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:09:36.889 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'QC_METRICS': maxForks=0; fair=false; array=0
Jun-09 08:09:36.892 [main] DEBUG nextflow.Session - Workflow process names [dsl2]: MERGE_DEDUP_SPLIT, COOLER_PROCESS, EXTRACT_BWA_INDEX, JUICER_HIC, BWA_ALIGN_PAIRTOOLS, QC_METRICS
Jun-09 08:09:36.894 [main] DEBUG nextflow.Session - Igniting dataflow network (10)
Jun-09 08:09:36.900 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > EXTRACT_BWA_INDEX
Jun-09 08:09:36.901 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > BWA_ALIGN_PAIRTOOLS
Jun-09 08:09:36.901 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > MERGE_DEDUP_SPLIT
Jun-09 08:09:36.901 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > COOLER_PROCESS
Jun-09 08:09:36.901 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > JUICER_HIC
Jun-09 08:09:36.901 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > QC_METRICS
Jun-09 08:09:36.901 [main] DEBUG nextflow.script.ScriptRunner - Parsed script files:
  Script_43f01a504ba15b5e: /Users/<USER>/projects/topology-tools/nf/modules/merge_dedup_split.nf
  Script_6c968723c735dde5: /Users/<USER>/projects/topology-tools/nf/modules/qc_metrics.nf
  Script_a457b5df1e6a90ec: /Users/<USER>/projects/topology-tools/nf/modules/bwa_align_pairtools.nf
  Script_900e8f290a0d3a82: /Users/<USER>/projects/topology-tools/nf/modules/cooler_process.nf
  Script_3c186acb8ea2eb2c: /Users/<USER>/projects/topology-tools/nf/modules/juicer_hic.nf
  Script_3cb8aa18b03099d1: /Users/<USER>/projects/topology-tools/nf/main.nf
  Script_f4e17a1e4235a98c: /Users/<USER>/projects/topology-tools/nf/modules/extract_bwa_index.nf
Jun-09 08:09:36.901 [main] DEBUG nextflow.script.ScriptRunner - > Awaiting termination 
Jun-09 08:09:36.901 [main] DEBUG nextflow.Session - Session await
Jun-09 08:09:36.954 [Actor Thread 8] DEBUG nextflow.util.HashBuilder - Unable to get file attributes file: /Users/<USER>/projects/tests/small-region-capture-micro-c/test_bwa_index.tgz -- Cause: java.nio.file.NoSuchFileException: /Users/<USER>/projects/tests/small-region-capture-micro-c/test_bwa_index.tgz
Jun-09 08:09:37.011 [Task submitter] WARN  n.executor.BashWrapperBuilder - Task runtime metrics are not reported when using macOS without a container engine
Jun-09 08:09:37.058 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:09:37.059 [Task submitter] INFO  nextflow.Session - [f9/c9f65d] Submitted process > EXTRACT_BWA_INDEX (small_rcmc_r1.fq)
Jun-09 08:09:37.121 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 1; name: EXTRACT_BWA_INDEX (small_rcmc_r1.fq); status: COMPLETED; exit: 1; error: -; workDir: /Users/<USER>/projects/topology-tools/work/f9/c9f65da52ca6257db37ef9e7a26482]
Jun-09 08:09:37.121 [Task monitor] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'TaskFinalizer' minSize=10; maxSize=30; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jun-09 08:09:37.126 [TaskFinalizer-1] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=EXTRACT_BWA_INDEX (small_rcmc_r1.fq); work-dir=/Users/<USER>/projects/topology-tools/work/f9/c9f65da52ca6257db37ef9e7a26482
  error [nextflow.exception.ProcessFailedException]: Process `EXTRACT_BWA_INDEX (small_rcmc_r1.fq)` terminated with an error exit status (1)
Jun-09 08:09:37.132 [TaskFinalizer-1] INFO  nextflow.processor.TaskProcessor - [f9/c9f65d] NOTE: Process `EXTRACT_BWA_INDEX (small_rcmc_r1.fq)` terminated with an error exit status (1) -- Execution is retried (1)
Jun-09 08:09:37.139 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:09:37.140 [Task submitter] INFO  nextflow.Session - [33/c3fa3b] Re-submitted process > EXTRACT_BWA_INDEX (small_rcmc_r1.fq)
Jun-09 08:09:37.188 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 2; name: EXTRACT_BWA_INDEX (small_rcmc_r1.fq); status: COMPLETED; exit: 1; error: -; workDir: /Users/<USER>/projects/topology-tools/work/33/c3fa3b5862930681df74dc7fd379a7]
Jun-09 08:09:37.189 [TaskFinalizer-2] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=EXTRACT_BWA_INDEX (small_rcmc_r1.fq); work-dir=/Users/<USER>/projects/topology-tools/work/33/c3fa3b5862930681df74dc7fd379a7
  error [nextflow.exception.ProcessFailedException]: Process `EXTRACT_BWA_INDEX (small_rcmc_r1.fq)` terminated with an error exit status (1)
Jun-09 08:09:37.190 [TaskFinalizer-2] INFO  nextflow.processor.TaskProcessor - [33/c3fa3b] NOTE: Process `EXTRACT_BWA_INDEX (small_rcmc_r1.fq)` terminated with an error exit status (1) -- Execution is retried (2)
Jun-09 08:09:37.196 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:09:37.196 [Task submitter] INFO  nextflow.Session - [e5/5d9d4e] Re-submitted process > EXTRACT_BWA_INDEX (small_rcmc_r1.fq)
Jun-09 08:09:37.240 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 3; name: EXTRACT_BWA_INDEX (small_rcmc_r1.fq); status: COMPLETED; exit: 1; error: -; workDir: /Users/<USER>/projects/topology-tools/work/e5/5d9d4ef55b8e5d1dc1ef65637e4ab7]
Jun-09 08:09:37.241 [TaskFinalizer-3] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=EXTRACT_BWA_INDEX (small_rcmc_r1.fq); work-dir=/Users/<USER>/projects/topology-tools/work/e5/5d9d4ef55b8e5d1dc1ef65637e4ab7
  error [nextflow.exception.ProcessFailedException]: Process `EXTRACT_BWA_INDEX (small_rcmc_r1.fq)` terminated with an error exit status (1)
Jun-09 08:09:37.249 [TaskFinalizer-3] ERROR nextflow.processor.TaskProcessor - Error executing process > 'EXTRACT_BWA_INDEX (small_rcmc_r1.fq)'

Caused by:
  Process `EXTRACT_BWA_INDEX (small_rcmc_r1.fq)` terminated with an error exit status (1)


Command executed:

  # Create genome index directory
  mkdir -p genome_index
  
  # Extract BWA index (compatible with BusyBox tar)
  if tar --help 2>&1 | grep -q "GNU tar"; then
      # GNU tar supports -z flag
      tar zxvf test_bwa_index.tgz -C genome_index
  else
      # BusyBox tar doesn't support -z, use gzip -dc + tar
      gzip -dc test_bwa_index.tgz | tar xvf - -C genome_index
  fi
  
  # Find the reference FASTA file (should have .bwt extension companion)
  bwt_file=$(find genome_index -name "*.bwt" | head -1)
  if [ -z "$bwt_file" ]; then
      echo "Error: No BWA index files found in test_bwa_index.tgz"
      exit 1
  fi
  
  # Log the extracted files
  echo "Extracted BWA index files:"
  ls -la genome_index/
  
  # Create versions file
  cat <<-END_VERSIONS > versions.yml
  "EXTRACT_BWA_INDEX":
      bwa: $(echo $(bwa 2>&1) | sed 's/^.*Version: //; s/Contact:.*$//')
      tar: $(tar --version 2>/dev/null | head -1 | sed 's/tar (GNU tar) //' || echo "BusyBox tar")
  END_VERSIONS

Command exit status:
  1

Command output:
  (empty)

Command error:
  gzip: can't stat: test_bwa_index.tgz (test_bwa_index.tgz.gz): No such file or directory

Work dir:
  /Users/<USER>/projects/topology-tools/work/e5/5d9d4ef55b8e5d1dc1ef65637e4ab7

Tip: you can try to figure out what's wrong by changing to the process work dir and showing the script file named `.command.sh`
Jun-09 08:09:37.252 [TaskFinalizer-3] DEBUG nextflow.Session - Session aborted -- Cause: Process `EXTRACT_BWA_INDEX (small_rcmc_r1.fq)` terminated with an error exit status (1)
Jun-09 08:09:37.254 [main] DEBUG nextflow.Session - Session await > all processes finished
Jun-09 08:09:37.263 [TaskFinalizer-3] ERROR nextflow.Nextflow - Pipeline execution stopped with the following message: gzip: can't stat: test_bwa_index.tgz (test_bwa_index.tgz.gz): No such file or directory
Jun-09 08:09:37.264 [Task monitor] DEBUG n.processor.TaskPollingMonitor - <<< barrier arrives (monitor: local) - terminating tasks monitor poll loop
Jun-09 08:09:37.264 [main] DEBUG nextflow.Session - Session await > all barriers passed
Jun-09 08:09:37.266 [main] INFO  nextflow.Nextflow - 
========================================================================================
                        Pipeline Execution Summary
========================================================================================
Completed at    : 2025-06-09T08:09:37.264805-04:00
Duration        : 1.1s
Success         : false
Work directory  : /Users/<USER>/projects/topology-tools/work
Exit status     : 1
Error message   : gzip: can't stat: test_bwa_index.tgz (test_bwa_index.tgz.gz): No such file or directory
========================================================================================

Jun-09 08:09:37.267 [main] ERROR nextflow.Nextflow - 💥 Pipeline failed!
Jun-09 08:09:37.267 [main] ERROR nextflow.Nextflow - Check the error message above and workflow logs for details.
Jun-09 08:09:37.270 [main] DEBUG n.trace.WorkflowStatsObserver - Workflow completed > WorkflowStats[succeededCount=0; failedCount=3; ignoredCount=0; cachedCount=0; pendingCount=0; submittedCount=0; runningCount=0; retriesCount=2; abortedCount=0; succeedDuration=0ms; failedDuration=30ms; cachedDuration=0ms;loadCpus=0; loadMemory=0; peakRunning=1; peakCpus=2; peakMemory=6 GB; ]
Jun-09 08:09:37.270 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow completed -- saving trace file
Jun-09 08:09:37.270 [main] DEBUG nextflow.trace.ReportObserver - Workflow completed -- rendering execution report
Jun-09 08:09:38.189 [main] DEBUG nextflow.trace.TimelineObserver - Workflow completed -- rendering execution timeline
Jun-09 08:09:38.295 [main] WARN  nextflow.dag.GraphvizRenderer - Graphviz is required to render the execution DAG in the given format -- See http://www.graphviz.org for more info.
Jun-09 08:09:38.299 [main] DEBUG nextflow.cache.CacheDB - Closing CacheDB done
Jun-09 08:09:38.311 [main] DEBUG nextflow.script.ScriptRunner - > Execution complete -- Goodbye
