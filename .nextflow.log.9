Jun-09 08:35:42.713 [main] DEBUG nextflow.cli.Launcher - $> nextflow run nf/main.nf -profile test
Jun-09 08:35:42.770 [main] DEBUG nextflow.cli.CmdRun - N E X T F L O W  ~  version 25.04.3
Jun-09 08:35:42.798 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/Users/<USER>/.nextflow/plugins; core-plugins: nf-amazon@2.15.0,nf-azure@1.16.0,nf-cloudcache@0.4.3,nf-codecommit@0.2.3,nf-console@1.2.1,nf-google@1.21.0,nf-k8s@1.0.0,nf-tower@1.11.3,nf-wave@1.12.1
Jun-09 08:35:42.822 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
Jun-09 08:35:42.823 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
Jun-09 08:35:42.825 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.12.0 in 'deployment' mode
Jun-09 08:35:42.833 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
Jun-09 08:35:42.847 [main] DEBUG nextflow.config.ConfigBuilder - Found config base: /Users/<USER>/projects/topology-tools/nf/nextflow.config
Jun-09 08:35:42.849 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /Users/<USER>/projects/topology-tools/nf/nextflow.config
Jun-09 08:35:42.870 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /Users/<USER>/.nextflow/secrets/store.json
Jun-09 08:35:42.873 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@73877e19] - activable => nextflow.secret.LocalSecretsProvider@73877e19
Jun-09 08:35:42.877 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `test`
Jun-09 08:35:43.435 [main] DEBUG nextflow.config.ConfigBuilder - Available config profiles: [cluster, debug, test, local, docker, cloud, shifter, mamba, charliecloud, conda, singularity, arm, test_full, podman]
Jun-09 08:35:43.453 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declaration
Jun-09 08:35:43.464 [main] DEBUG nextflow.cli.CmdRun - Launching `nf/main.nf` [gloomy_leavitt] DSL2 - revision: e10359e183
Jun-09 08:35:43.465 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
Jun-09 08:35:43.465 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[]
Jun-09 08:35:43.498 [main] DEBUG nextflow.Session - Session UUID: a09e770b-6588-4710-a28e-afd32dc51f3b
Jun-09 08:35:43.499 [main] DEBUG nextflow.Session - Run name: gloomy_leavitt
Jun-09 08:35:43.499 [main] DEBUG nextflow.Session - Executor pool size: 10
Jun-09 08:35:43.504 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
Jun-09 08:35:43.506 [main] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'FileTransfer' minSize=10; maxSize=30; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jun-09 08:35:43.522 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 25.04.3 build 5949
  Created: 02-06-2025 20:56 UTC (16:56 EDT)
  System: Mac OS X 15.4.1
  Runtime: Groovy 4.0.26 on OpenJDK 64-Bit Server VM 23.0.2+7
  Encoding: UTF-8 (UTF-8)
  Process: <EMAIL> [127.0.0.1]
  CPUs: 10 - Mem: 64 GB (449 MB) - Swap: 7 GB (1.4 GB)
Jun-09 08:35:43.531 [main] DEBUG nextflow.Session - Work-dir: /Users/<USER>/projects/topology-tools/work [Mac OS X]
Jun-09 08:35:43.532 [main] DEBUG nextflow.Session - Script base path does not exist or is not a directory: /Users/<USER>/projects/topology-tools/nf/bin
Jun-09 08:35:43.539 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
Jun-09 08:35:43.543 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
Jun-09 08:35:43.558 [main] DEBUG nextflow.Session - Observer factory (v2): LinObserverFactory
Jun-09 08:35:43.582 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
Jun-09 08:35:43.589 [main] DEBUG nextflow.util.CustomThreadPool - Creating default thread pool > poolSize: 11; maxThreads: 1000
Jun-09 08:35:43.624 [main] DEBUG nextflow.Session - Session start
Jun-09 08:35:43.627 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow started -- trace file: /Users/<USER>/projects/topology-tools/test_results/pipeline_info/execution_trace_2025-06-09_08-35-43.txt
Jun-09 08:35:43.764 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
Jun-09 08:35:44.079 [main] INFO  nextflow.Nextflow - 
========================================================================================
                        Micro-C Pipeline - Nextflow DSL2
========================================================================================
Sample ID       : test-sample
FASTQ R1        : tests/small-region-capture-micro-c/small_rcmc_r1.fq.gz
FASTQ R2        : tests/small-region-capture-micro-c/small_rcmc_r2.fq.gz
BWA Index       : tests/small-region-capture-micro-c/test_bwa_index.tgz
Chrom Sizes     : tests/small-region-capture-micro-c/test.chrom.sizes
Output Dir      : ./test_results
Resolution      : 1000
BWA Cores       : 2
Min MAPQ        : 20
========================================================================================

Jun-09 08:35:44.170 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name EXTRACT_BWA_INDEX
Jun-09 08:35:44.172 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:EXTRACT_BWA_INDEX` matches process EXTRACT_BWA_INDEX
Jun-09 08:35:44.180 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:35:44.180 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:35:44.184 [main] DEBUG nextflow.executor.Executor - [warm up] executor > local
Jun-09 08:35:44.188 [main] DEBUG n.processor.LocalPollingMonitor - Creating local task monitor for executor 'local' > cpus=10; memory=64 GB; capacity=10; pollInterval=100ms; dumpInterval=5m
Jun-09 08:35:44.190 [main] DEBUG n.processor.TaskPollingMonitor - >>> barrier register (monitor: local)
Jun-09 08:35:44.201 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'EXTRACT_BWA_INDEX': maxForks=0; fair=false; array=0
Jun-09 08:35:44.223 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name BWA_ALIGN_PAIRTOOLS
Jun-09 08:35:44.224 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:BWA_ALIGN_PAIRTOOLS` matches process BWA_ALIGN_PAIRTOOLS
Jun-09 08:35:44.225 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:35:44.225 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:35:44.225 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'BWA_ALIGN_PAIRTOOLS': maxForks=0; fair=false; array=0
Jun-09 08:35:44.236 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name MERGE_DEDUP_SPLIT
Jun-09 08:35:44.236 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:MERGE_DEDUP_SPLIT` matches process MERGE_DEDUP_SPLIT
Jun-09 08:35:44.237 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:35:44.237 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:35:44.238 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'MERGE_DEDUP_SPLIT': maxForks=0; fair=false; array=0
Jun-09 08:35:44.242 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name COOLER_PROCESS
Jun-09 08:35:44.242 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:COOLER_PROCESS` matches process COOLER_PROCESS
Jun-09 08:35:44.243 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:35:44.243 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:35:44.244 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'COOLER_PROCESS': maxForks=0; fair=false; array=0
Jun-09 08:35:44.251 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name JUICER_HIC
Jun-09 08:35:44.252 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:JUICER_HIC` matches process JUICER_HIC
Jun-09 08:35:44.253 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:35:44.253 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:35:44.253 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'JUICER_HIC': maxForks=0; fair=false; array=0
Jun-09 08:35:44.257 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name QC_METRICS
Jun-09 08:35:44.258 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:QC_METRICS` matches process QC_METRICS
Jun-09 08:35:44.259 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:35:44.259 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:35:44.260 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'QC_METRICS': maxForks=0; fair=false; array=0
Jun-09 08:35:44.262 [main] DEBUG nextflow.Session - Workflow process names [dsl2]: MERGE_DEDUP_SPLIT, COOLER_PROCESS, EXTRACT_BWA_INDEX, JUICER_HIC, BWA_ALIGN_PAIRTOOLS, QC_METRICS
Jun-09 08:35:44.264 [main] DEBUG nextflow.Session - Igniting dataflow network (10)
Jun-09 08:35:44.268 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > EXTRACT_BWA_INDEX
Jun-09 08:35:44.268 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > BWA_ALIGN_PAIRTOOLS
Jun-09 08:35:44.268 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > MERGE_DEDUP_SPLIT
Jun-09 08:35:44.268 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > COOLER_PROCESS
Jun-09 08:35:44.268 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > JUICER_HIC
Jun-09 08:35:44.268 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > QC_METRICS
Jun-09 08:35:44.269 [main] DEBUG nextflow.script.ScriptRunner - Parsed script files:
  Script_43f01a504ba15b5e: /Users/<USER>/projects/topology-tools/nf/modules/merge_dedup_split.nf
  Script_6c968723c735dde5: /Users/<USER>/projects/topology-tools/nf/modules/qc_metrics.nf
  Script_fb09b2496743dbcc: /Users/<USER>/projects/topology-tools/nf/main.nf
  Script_900e8f290a0d3a82: /Users/<USER>/projects/topology-tools/nf/modules/cooler_process.nf
  Script_a457b5df1e6a90ec: /Users/<USER>/projects/topology-tools/nf/modules/bwa_align_pairtools.nf
  Script_3c186acb8ea2eb2c: /Users/<USER>/projects/topology-tools/nf/modules/juicer_hic.nf
  Script_f4e17a1e4235a98c: /Users/<USER>/projects/topology-tools/nf/modules/extract_bwa_index.nf
Jun-09 08:35:44.269 [main] DEBUG nextflow.script.ScriptRunner - > Awaiting termination 
Jun-09 08:35:44.269 [main] DEBUG nextflow.Session - Session await
Jun-09 08:35:44.360 [Task submitter] WARN  n.executor.BashWrapperBuilder - Task runtime metrics are not reported when using macOS without a container engine
Jun-09 08:35:44.370 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:35:44.371 [Task submitter] INFO  nextflow.Session - [e0/efc132] Submitted process > EXTRACT_BWA_INDEX (small_rcmc_r1.fq)
Jun-09 08:35:44.456 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 1; name: EXTRACT_BWA_INDEX (small_rcmc_r1.fq); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/e0/efc1325f3248074ca7d7b2e4993b46]
Jun-09 08:35:44.457 [Task monitor] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'TaskFinalizer' minSize=10; maxSize=30; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jun-09 08:35:44.486 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:35:44.487 [Task submitter] INFO  nextflow.Session - [56/e266ed] Submitted process > BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq)
Jun-09 08:35:44.548 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 2; name: BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq); status: COMPLETED; exit: 1; error: -; workDir: /Users/<USER>/projects/topology-tools/work/56/e266ed9b36f3c9d23b7f5470c2f8e2]
Jun-09 08:35:44.550 [TaskFinalizer-2] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq); work-dir=/Users/<USER>/projects/topology-tools/work/56/e266ed9b36f3c9d23b7f5470c2f8e2
  error [nextflow.exception.ProcessFailedException]: Process `BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq)` terminated with an error exit status (1)
Jun-09 08:35:44.555 [TaskFinalizer-2] INFO  nextflow.processor.TaskProcessor - [56/e266ed] NOTE: Process `BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq)` terminated with an error exit status (1) -- Execution is retried (1)
Jun-09 08:35:44.560 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:35:44.560 [Task submitter] INFO  nextflow.Session - [0d/220db5] Re-submitted process > BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq)
Jun-09 08:35:44.607 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 3; name: BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq); status: COMPLETED; exit: 1; error: -; workDir: /Users/<USER>/projects/topology-tools/work/0d/220db5d307f067ccc0b284c1ec9796]
Jun-09 08:35:44.608 [TaskFinalizer-3] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq); work-dir=/Users/<USER>/projects/topology-tools/work/0d/220db5d307f067ccc0b284c1ec9796
  error [nextflow.exception.ProcessFailedException]: Process `BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq)` terminated with an error exit status (1)
Jun-09 08:35:44.609 [TaskFinalizer-3] INFO  nextflow.processor.TaskProcessor - [0d/220db5] NOTE: Process `BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq)` terminated with an error exit status (1) -- Execution is retried (2)
Jun-09 08:35:44.614 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:35:44.615 [Task submitter] INFO  nextflow.Session - [f8/f78eec] Re-submitted process > BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq)
Jun-09 08:35:44.665 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 4; name: BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq); status: COMPLETED; exit: 1; error: -; workDir: /Users/<USER>/projects/topology-tools/work/f8/f78eecf08f12e6dbc6a0ba07ff6870]
Jun-09 08:35:44.665 [TaskFinalizer-4] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq); work-dir=/Users/<USER>/projects/topology-tools/work/f8/f78eecf08f12e6dbc6a0ba07ff6870
  error [nextflow.exception.ProcessFailedException]: Process `BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq)` terminated with an error exit status (1)
Jun-09 08:35:44.673 [TaskFinalizer-4] ERROR nextflow.processor.TaskProcessor - Error executing process > 'BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq)'

Caused by:
  Process `BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq)` terminated with an error exit status (1)


Command executed:

  # Find the reference genome file
  bwt_file=$(find genome_index -name "*.bwt" | head -1)
  if [ -z "$bwt_file" ]; then
      echo "Error: No BWA index files found in genome_index"
      exit 1
  fi
  
  # Get the reference genome prefix (remove .bwt extension)
  genome_index_fa=${bwt_file%.bwt}
  
  echo "Using BWA index: $genome_index_fa"
  echo "Processing FASTQ files: small_rcmc_r1.fq.gz, small_rcmc_r2.fq.gz"
  echo "Output file: small_rcmc_r1.fq.pairsam.gz"
  
  # Run BWA alignment piped to pairtools
  bwa mem -5SP -T0 -t2 $genome_index_fa small_rcmc_r1.fq.gz small_rcmc_r2.fq.gz | \
  pairtools parse \
      --min-mapq 20 \
      --walks-policy 5unique \
      --max-inter-align-gap 30 \
      --add-columns pos5,pos3,dist_to_5,dist_to_3,read_len \
      --nproc-in 2 \
      --nproc-out 2 \
      --chroms-path test.chrom.sizes | \
  pairtools sort \
      --nproc 2 \
      -o small_rcmc_r1.fq.pairsam.gz
  
  # Verify output file was created
  if [ ! -f "small_rcmc_r1.fq.pairsam.gz" ]; then
      echo "Error: Output file small_rcmc_r1.fq.pairsam.gz was not created"
      exit 1
  fi
  
  echo "Successfully created small_rcmc_r1.fq.pairsam.gz"
  echo "File size: $(du -h small_rcmc_r1.fq.pairsam.gz)"
  
  # Create versions file
  cat <<-END_VERSIONS > versions.yml
  "BWA_ALIGN_PAIRTOOLS":
      bwa: $(echo $(bwa 2>&1) | sed 's/^.*Version: //; s/Contact:.*$//')
      pairtools: $(pairtools --version 2>&1 | sed 's/pairtools, version //')
      samtools: $(echo $(samtools --version 2>&1) | sed 's/^.*samtools //; s/Using.*$//')
  END_VERSIONS

Command exit status:
  1

Command output:
  Error: No BWA index files found in genome_index

Command error:
  Error: No BWA index files found in genome_index

Work dir:
  /Users/<USER>/projects/topology-tools/work/f8/f78eecf08f12e6dbc6a0ba07ff6870

Tip: when you have fixed the problem you can continue the execution adding the option `-resume` to the run command line
Jun-09 08:35:44.675 [TaskFinalizer-4] DEBUG nextflow.Session - Session aborted -- Cause: Process `BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq)` terminated with an error exit status (1)
Jun-09 08:35:44.675 [main] DEBUG nextflow.Session - Session await > all processes finished
Jun-09 08:35:44.684 [TaskFinalizer-4] ERROR nextflow.Nextflow - Pipeline execution stopped with the following message: Error: No BWA index files found in genome_index
Jun-09 08:35:44.684 [main] DEBUG nextflow.Session - Session await > all barriers passed
Jun-09 08:35:44.684 [Task monitor] DEBUG n.processor.TaskPollingMonitor - <<< barrier arrives (monitor: local) - terminating tasks monitor poll loop
Jun-09 08:35:44.686 [main] INFO  nextflow.Nextflow - 
========================================================================================
                        Pipeline Execution Summary
========================================================================================
Completed at    : 2025-06-09T08:35:44.684864-04:00
Duration        : 1.1s
Success         : false
Work directory  : /Users/<USER>/projects/topology-tools/work
Exit status     : 1
Error message   : Error: No BWA index files found in genome_index
========================================================================================

Jun-09 08:35:44.686 [main] ERROR nextflow.Nextflow - 💥 Pipeline failed!
Jun-09 08:35:44.687 [main] ERROR nextflow.Nextflow - Check the error message above and workflow logs for details.
Jun-09 08:35:44.690 [main] DEBUG n.trace.WorkflowStatsObserver - Workflow completed > WorkflowStats[succeededCount=1; failedCount=3; ignoredCount=0; cachedCount=0; pendingCount=0; submittedCount=0; runningCount=0; retriesCount=2; abortedCount=0; succeedDuration=106ms; failedDuration=24ms; cachedDuration=0ms;loadCpus=0; loadMemory=0; peakRunning=1; peakCpus=2; peakMemory=6 GB; ]
Jun-09 08:35:44.690 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow completed -- saving trace file
Jun-09 08:35:44.691 [main] DEBUG nextflow.trace.ReportObserver - Workflow completed -- rendering execution report
Jun-09 08:35:45.562 [main] DEBUG nextflow.trace.TimelineObserver - Workflow completed -- rendering execution timeline
Jun-09 08:35:45.660 [main] WARN  nextflow.dag.GraphvizRenderer - Graphviz is required to render the execution DAG in the given format -- See http://www.graphviz.org for more info.
Jun-09 08:35:45.664 [main] DEBUG nextflow.cache.CacheDB - Closing CacheDB done
Jun-09 08:35:45.675 [main] DEBUG nextflow.script.ScriptRunner - > Execution complete -- Goodbye
