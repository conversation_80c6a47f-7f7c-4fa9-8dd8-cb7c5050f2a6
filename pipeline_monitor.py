#!/usr/bin/env python3
"""
Real-time monitoring script for the Micro-C pipeline.
Monitors resource usage, progress, and provides live updates.
"""

import os
import time
import psutil
import subprocess
import argparse
import json
from datetime import datetime, timedelta
import threading
import signal
import sys

class PipelineMonitor:
    def __init__(self, output_dir, sample_id):
        self.output_dir = output_dir
        self.sample_id = sample_id
        self.monitoring = True
        self.start_time = time.time()
        self.log_file = os.path.join(output_dir, f"{sample_id}_monitor.log")
        self.stats_file = os.path.join(output_dir, f"{sample_id}_stats.json")
        self.stats = {
            'start_time': datetime.now().isoformat(),
            'cpu_usage': [],
            'memory_usage': [],
            'disk_usage': [],
            'pipeline_stages': [],
            'current_stage': 'Starting'
        }
        
    def signal_handler(self, signum, frame):
        """Handle Ctrl+C gracefully"""
        print("\n\n🛑 Monitoring stopped by user")
        self.monitoring = False
        self.save_final_stats()
        sys.exit(0)
        
    def monitor_resources(self):
        """Monitor system resources in background thread"""
        while self.monitoring:
            try:
                # CPU and memory usage
                cpu_percent = psutil.cpu_percent(interval=1)
                memory = psutil.virtual_memory()
                
                # Disk usage for output directory
                if os.path.exists(self.output_dir):
                    disk_usage = psutil.disk_usage(self.output_dir)
                    disk_free_gb = disk_usage.free / (1024**3)
                else:
                    disk_free_gb = 0
                
                # Record stats
                timestamp = time.time()
                self.stats['cpu_usage'].append({
                    'timestamp': timestamp,
                    'cpu_percent': cpu_percent
                })
                self.stats['memory_usage'].append({
                    'timestamp': timestamp,
                    'memory_percent': memory.percent,
                    'memory_used_gb': memory.used / (1024**3),
                    'memory_available_gb': memory.available / (1024**3)
                })
                self.stats['disk_usage'].append({
                    'timestamp': timestamp,
                    'disk_free_gb': disk_free_gb
                })
                
                # Keep only last 1000 data points to prevent memory bloat
                for key in ['cpu_usage', 'memory_usage', 'disk_usage']:
                    if len(self.stats[key]) > 1000:
                        self.stats[key] = self.stats[key][-1000:]
                
                time.sleep(5)  # Monitor every 5 seconds
                
            except Exception as e:
                print(f"Error monitoring resources: {e}")
                time.sleep(10)
    
    def detect_pipeline_stage(self):
        """Detect current pipeline stage by examining output files"""
        stages = [
            ('align', 'Alignment'),
            ('merge', 'Merge/Dedup'),
            ('cooler', 'Cooler Processing'),
            ('hic', 'HiC Generation'),
            ('qc', 'QC Analysis')
        ]
        
        current_stage = 'Starting'
        for stage_dir, stage_name in stages:
            stage_path = os.path.join(self.output_dir, stage_dir)
            if os.path.exists(stage_path) and os.listdir(stage_path):
                current_stage = stage_name
        
        # Check if pipeline is complete
        final_files = [
            os.path.join(self.output_dir, 'merge', f'{self.sample_id}.bam'),
            os.path.join(self.output_dir, 'cooler', f'{self.sample_id}.balanced.mcool')
        ]
        
        if all(os.path.exists(f) for f in final_files):
            current_stage = 'Complete'
        
        return current_stage
    
    def get_file_sizes(self):
        """Get sizes of key output files"""
        file_sizes = {}
        
        key_files = [
            ('merge', f'{self.sample_id}.mapped.pairs'),
            ('merge', f'{self.sample_id}.bam'),
            ('cooler', f'{self.sample_id}.cool'),
            ('cooler', f'{self.sample_id}.raw.mcool'),
            ('cooler', f'{self.sample_id}.balanced.mcool')
        ]
        
        for subdir, filename in key_files:
            filepath = os.path.join(self.output_dir, subdir, filename)
            if os.path.exists(filepath):
                size_mb = os.path.getsize(filepath) / (1024**2)
                file_sizes[filename] = size_mb
        
        return file_sizes
    
    def print_status(self):
        """Print current status"""
        elapsed = time.time() - self.start_time
        elapsed_str = str(timedelta(seconds=int(elapsed)))
        
        current_stage = self.detect_pipeline_stage()
        if current_stage != self.stats['current_stage']:
            self.stats['pipeline_stages'].append({
                'stage': current_stage,
                'timestamp': time.time(),
                'elapsed_seconds': elapsed
            })
            self.stats['current_stage'] = current_stage
        
        # Get latest resource stats
        cpu_current = self.stats['cpu_usage'][-1]['cpu_percent'] if self.stats['cpu_usage'] else 0
        memory_current = self.stats['memory_usage'][-1] if self.stats['memory_usage'] else {}
        disk_current = self.stats['disk_usage'][-1] if self.stats['disk_usage'] else {}
        
        # Get file sizes
        file_sizes = self.get_file_sizes()
        
        # Clear screen and print status
        os.system('clear' if os.name == 'posix' else 'cls')
        
        print("=" * 80)
        print(f"🧬 MICRO-C PIPELINE MONITOR - {self.sample_id}")
        print("=" * 80)
        print(f"⏱️  Elapsed Time: {elapsed_str}")
        print(f"🔄 Current Stage: {current_stage}")
        print(f"📊 CPU Usage: {cpu_current:.1f}%")
        
        if memory_current:
            print(f"💾 Memory: {memory_current.get('memory_percent', 0):.1f}% "
                  f"({memory_current.get('memory_used_gb', 0):.1f} GB used)")
        
        if disk_current:
            print(f"💿 Disk Free: {disk_current.get('disk_free_gb', 0):.1f} GB")
        
        print("\n📁 Output Files:")
        if file_sizes:
            for filename, size_mb in file_sizes.items():
                print(f"   {filename}: {size_mb:.1f} MB")
        else:
            print("   No output files yet...")
        
        print("\n📈 Stage Progress:")
        for stage_info in self.stats['pipeline_stages']:
            stage_elapsed = str(timedelta(seconds=int(stage_info['elapsed_seconds'])))
            print(f"   ✅ {stage_info['stage']} (at {stage_elapsed})")
        
        if current_stage != 'Complete':
            print(f"   🔄 {current_stage} (in progress...)")
        
        print("\n" + "=" * 80)
        print("Press Ctrl+C to stop monitoring")
        print("=" * 80)
    
    def save_stats(self):
        """Save current stats to file"""
        try:
            with open(self.stats_file, 'w') as f:
                json.dump(self.stats, f, indent=2)
        except Exception as e:
            print(f"Error saving stats: {e}")
    
    def save_final_stats(self):
        """Save final statistics"""
        self.stats['end_time'] = datetime.now().isoformat()
        self.stats['total_elapsed_seconds'] = time.time() - self.start_time
        self.save_stats()
        
        print(f"\n📊 Final statistics saved to: {self.stats_file}")
    
    def run(self):
        """Main monitoring loop"""
        # Set up signal handler for graceful shutdown
        signal.signal(signal.SIGINT, self.signal_handler)
        
        # Start resource monitoring thread
        monitor_thread = threading.Thread(target=self.monitor_resources, daemon=True)
        monitor_thread.start()
        
        print(f"🚀 Starting pipeline monitoring for {self.sample_id}")
        print(f"📁 Output directory: {self.output_dir}")
        print(f"📊 Stats file: {self.stats_file}")
        
        try:
            while self.monitoring:
                self.print_status()
                self.save_stats()
                
                # Check if pipeline is complete
                if self.stats['current_stage'] == 'Complete':
                    print("\n🎉 Pipeline completed successfully!")
                    self.monitoring = False
                    break
                
                time.sleep(10)  # Update every 10 seconds
                
        except KeyboardInterrupt:
            self.signal_handler(None, None)
        
        self.save_final_stats()

def main():
    parser = argparse.ArgumentParser(description="Monitor Micro-C pipeline execution")
    parser.add_argument("--output_dir", required=True,
                       help="Pipeline output directory to monitor")
    parser.add_argument("--sample_id", required=True,
                       help="Sample ID being processed")
    parser.add_argument("--log_level", default="INFO",
                       choices=["DEBUG", "INFO", "WARNING", "ERROR"],
                       help="Logging level")
    
    args = parser.parse_args()
    
    if not os.path.exists(args.output_dir):
        print(f"❌ Output directory does not exist: {args.output_dir}")
        sys.exit(1)
    
    monitor = PipelineMonitor(args.output_dir, args.sample_id)
    monitor.run()

if __name__ == "__main__":
    main()
