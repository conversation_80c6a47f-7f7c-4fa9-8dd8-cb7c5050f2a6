Jun-09 09:56:20.900 [main] DEBUG nextflow.cli.Launcher - $> nextflow run nf/main.nf -profile test,docker
Jun-09 09:56:20.957 [main] INFO  nextflow.cli.CmdRun - N E X T F L O W  ~  version 23.10.1
Jun-09 09:56:20.969 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/Users/<USER>/.nextflow/plugins; core-plugins: nf-amazon@2.1.4,nf-azure@1.3.3,nf-cloudcache@0.3.0,nf-codecommit@0.1.5,nf-console@1.0.6,nf-ga4gh@1.1.0,nf-google@1.8.3,nf-tower@1.6.3,nf-wave@1.0.1
Jun-09 09:56:20.978 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
Jun-09 09:56:20.979 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
Jun-09 09:56:20.981 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.4.1 in 'deployment' mode
Jun-09 09:56:20.987 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
Jun-09 09:56:20.998 [main] DEBUG nextflow.config.ConfigBuilder - Found config base: /Users/<USER>/projects/topology-tools/nf/nextflow.config
Jun-09 09:56:20.999 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /Users/<USER>/projects/topology-tools/nf/nextflow.config
Jun-09 09:56:21.012 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `test,docker`
Jun-09 09:56:21.636 [main] DEBUG nextflow.config.ConfigBuilder - Available config profiles: [cluster, debug, test, local, docker, cloud, shifter, mamba, charliecloud, conda, singularity, arm, test_full, podman]
Jun-09 09:56:21.654 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declararion
Jun-09 09:56:21.668 [main] INFO  nextflow.cli.CmdRun - Launching `nf/main.nf` [romantic_wilson] DSL2 - revision: cb60860841
Jun-09 09:56:21.669 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
Jun-09 09:56:21.669 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[]
Jun-09 09:56:21.674 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /Users/<USER>/.nextflow/secrets/store.json
Jun-09 09:56:21.676 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@306c7bf6] - activable => nextflow.secret.LocalSecretsProvider@306c7bf6
Jun-09 09:56:21.712 [main] DEBUG nextflow.Session - Session UUID: 1e241348-9ff9-4519-a8ef-72eaa2744a45
Jun-09 09:56:21.713 [main] DEBUG nextflow.Session - Run name: romantic_wilson
Jun-09 09:56:21.713 [main] DEBUG nextflow.Session - Executor pool size: 10
Jun-09 09:56:21.718 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
Jun-09 09:56:21.721 [main] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'FileTransfer' minSize=10; maxSize=30; workQueue=LinkedBlockingQueue[10000]; allowCoreThreadTimeout=false
Jun-09 09:56:21.759 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 23.10.1 build 5891
  Created: 12-01-2024 22:01 UTC (17:01 EDT)
  System: Mac OS X 15.4.1
  Runtime: Groovy 3.0.19 on OpenJDK 64-Bit Server VM 11.0.26+4-LTS
  Encoding: UTF-8 (UTF-8)
  Process: <EMAIL> [127.0.0.1]
  CPUs: 10 - Mem: 64 GB (7.5 GB) - Swap: 7 GB (1.4 GB)
Jun-09 09:56:21.769 [main] DEBUG nextflow.Session - Work-dir: /Users/<USER>/projects/topology-tools/work [Mac OS X]
Jun-09 09:56:21.770 [main] DEBUG nextflow.Session - Script base path does not exist or is not a directory: /Users/<USER>/projects/topology-tools/nf/bin
Jun-09 09:56:21.776 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
Jun-09 09:56:21.781 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
Jun-09 09:56:21.808 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
Jun-09 09:56:21.814 [main] DEBUG nextflow.util.CustomThreadPool - Creating default thread pool > poolSize: 11; maxThreads: 1000
Jun-09 09:56:21.859 [main] DEBUG nextflow.Session - Session start
Jun-09 09:56:21.862 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow started -- trace file: /Users/<USER>/projects/topology-tools/test_results/pipeline_info/execution_trace_2025-06-09_09-56-21.txt
Jun-09 09:56:22.085 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
Jun-09 09:56:22.419 [main] INFO  nextflow.Nextflow - 
========================================================================================
                        Micro-C Pipeline - Nextflow DSL2
========================================================================================
Sample ID       : small-rcmc
FASTQ R1        : tests/small-region-capture-micro-c/small_rcmc_r1.fq.gz,tests/small-region-capture-micro-c/small_rcmc-extra-reads_r1.fq.gz
FASTQ R2        : tests/small-region-capture-micro-c/small_rcmc_r2.fq.gz,tests/small-region-capture-micro-c/small_rcmc-extra-reads_r2.fq.gz
BWA Index       : tests/small-region-capture-micro-c/test_bwa_index.tgz
Chrom Sizes     : tests/small-region-capture-micro-c/test.chrom.sizes
Output Dir      : ./test_results
Resolution      : 1000
BWA Cores       : 2
Min MAPQ        : 20
========================================================================================

Jun-09 09:56:22.508 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name EXTRACT_BWA_INDEX
Jun-09 09:56:22.509 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:EXTRACT_BWA_INDEX` matches process EXTRACT_BWA_INDEX
Jun-09 09:56:22.512 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 09:56:22.512 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 09:56:22.517 [main] DEBUG nextflow.executor.Executor - [warm up] executor > local
Jun-09 09:56:22.519 [main] DEBUG n.processor.LocalPollingMonitor - Creating local task monitor for executor 'local' > cpus=10; memory=64 GB; capacity=10; pollInterval=100ms; dumpInterval=5m
Jun-09 09:56:22.521 [main] DEBUG n.processor.TaskPollingMonitor - >>> barrier register (monitor: local)
Jun-09 09:56:22.568 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name BWA_ALIGN_PAIRTOOLS
Jun-09 09:56:22.568 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:BWA_ALIGN_PAIRTOOLS` matches process BWA_ALIGN_PAIRTOOLS
Jun-09 09:56:22.569 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 09:56:22.569 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 09:56:22.579 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name MERGE_DEDUP_SPLIT
Jun-09 09:56:22.580 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:MERGE_DEDUP_SPLIT` matches process MERGE_DEDUP_SPLIT
Jun-09 09:56:22.580 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 09:56:22.580 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 09:56:22.585 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name COOLER_PROCESS
Jun-09 09:56:22.585 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:COOLER_PROCESS` matches process COOLER_PROCESS
Jun-09 09:56:22.586 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 09:56:22.586 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 09:56:22.592 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name JUICER_HIC
Jun-09 09:56:22.592 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:JUICER_HIC` matches process JUICER_HIC
Jun-09 09:56:22.593 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 09:56:22.593 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 09:56:22.596 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name QC_METRICS
Jun-09 09:56:22.597 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:QC_METRICS` matches process QC_METRICS
Jun-09 09:56:22.597 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 09:56:22.597 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 09:56:22.599 [main] DEBUG nextflow.Session - Workflow process names [dsl2]: MERGE_DEDUP_SPLIT, COOLER_PROCESS, EXTRACT_BWA_INDEX, JUICER_HIC, BWA_ALIGN_PAIRTOOLS, QC_METRICS
Jun-09 09:56:22.599 [main] DEBUG nextflow.Session - Igniting dataflow network (9)
Jun-09 09:56:22.605 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > EXTRACT_BWA_INDEX
Jun-09 09:56:22.606 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > BWA_ALIGN_PAIRTOOLS
Jun-09 09:56:22.606 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > MERGE_DEDUP_SPLIT
Jun-09 09:56:22.606 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > COOLER_PROCESS
Jun-09 09:56:22.607 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > JUICER_HIC
Jun-09 09:56:22.607 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > QC_METRICS
Jun-09 09:56:22.608 [main] DEBUG nextflow.script.ScriptRunner - Parsed script files:
  Script_43f01a504ba15b5e: /Users/<USER>/projects/topology-tools/nf/./modules/merge_dedup_split.nf
  Script_6c968723c735dde5: /Users/<USER>/projects/topology-tools/nf/./modules/qc_metrics.nf
  Script_900e8f290a0d3a82: /Users/<USER>/projects/topology-tools/nf/./modules/cooler_process.nf
  Script_3c186acb8ea2eb2c: /Users/<USER>/projects/topology-tools/nf/./modules/juicer_hic.nf
  Script_59383c090845de85: /Users/<USER>/projects/topology-tools/nf/main.nf
  Script_e05012147db5f27a: /Users/<USER>/projects/topology-tools/nf/./modules/bwa_align_pairtools.nf
  Script_f4e17a1e4235a98c: /Users/<USER>/projects/topology-tools/nf/./modules/extract_bwa_index.nf
Jun-09 09:56:22.608 [main] DEBUG nextflow.script.ScriptRunner - > Awaiting termination 
Jun-09 09:56:22.608 [main] DEBUG nextflow.Session - Session await
Jun-09 09:56:22.715 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 09:56:22.716 [Task submitter] INFO  nextflow.Session - [f5/d14075] Submitted process > EXTRACT_BWA_INDEX (small_rcmc_r1.fq)
Jun-09 09:56:22.728 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 09:56:22.729 [Task submitter] INFO  nextflow.Session - [bd/dad53f] Submitted process > EXTRACT_BWA_INDEX (small_rcmc-extra-reads_r1.fq)
Jun-09 09:56:26.001 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 1; name: EXTRACT_BWA_INDEX (small_rcmc_r1.fq); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/f5/d140753a4fbcd6963303d6bef8fb96]
Jun-09 09:56:26.025 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 2; name: EXTRACT_BWA_INDEX (small_rcmc-extra-reads_r1.fq); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/bd/dad53f90cef41e464756622344a1ac]
Jun-09 09:56:26.032 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 09:56:26.032 [Task submitter] INFO  nextflow.Session - [79/0ccb06] Submitted process > BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq)
Jun-09 09:56:26.035 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 09:56:26.035 [Task submitter] INFO  nextflow.Session - [1f/75dfb8] Submitted process > BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq)
Jun-09 09:56:27.215 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 3; name: BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq); status: COMPLETED; exit: 125; error: -; workDir: /Users/<USER>/projects/topology-tools/work/79/0ccb066b832897291136c258182af7]
Jun-09 09:56:27.217 [Task monitor] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq); work-dir=/Users/<USER>/projects/topology-tools/work/79/0ccb066b832897291136c258182af7
  error [nextflow.exception.ProcessFailedException]: Process `BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq)` terminated with an error exit status (125)
Jun-09 09:56:27.220 [Task monitor] INFO  nextflow.processor.TaskProcessor - [79/0ccb06] NOTE: Process `BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq)` terminated with an error exit status (125) -- Execution is retried (1)
Jun-09 09:56:27.226 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 09:56:27.226 [Task submitter] INFO  nextflow.Session - [4a/56c4f2] Re-submitted process > BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq)
Jun-09 09:56:27.260 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 4; name: BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq); status: COMPLETED; exit: 125; error: -; workDir: /Users/<USER>/projects/topology-tools/work/1f/75dfb81ee8ec3ba0aad24324d0f4ec]
Jun-09 09:56:27.260 [Task monitor] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq); work-dir=/Users/<USER>/projects/topology-tools/work/1f/75dfb81ee8ec3ba0aad24324d0f4ec
  error [nextflow.exception.ProcessFailedException]: Process `BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq)` terminated with an error exit status (125)
Jun-09 09:56:27.261 [Task monitor] INFO  nextflow.processor.TaskProcessor - [1f/75dfb8] NOTE: Process `BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq)` terminated with an error exit status (125) -- Execution is retried (1)
Jun-09 09:56:27.267 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 09:56:27.267 [Task submitter] INFO  nextflow.Session - [08/7bc9e3] Re-submitted process > BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq)
Jun-09 09:56:28.032 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 6; name: BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq); status: COMPLETED; exit: 125; error: -; workDir: /Users/<USER>/projects/topology-tools/work/08/7bc9e3acc89db5d36384ab656cd29d]
Jun-09 09:56:28.033 [Task monitor] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq); work-dir=/Users/<USER>/projects/topology-tools/work/08/7bc9e3acc89db5d36384ab656cd29d
  error [nextflow.exception.ProcessFailedException]: Process `BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq)` terminated with an error exit status (125)
Jun-09 09:56:28.033 [Task monitor] INFO  nextflow.processor.TaskProcessor - [08/7bc9e3] NOTE: Process `BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq)` terminated with an error exit status (125) -- Execution is retried (2)
Jun-09 09:56:28.038 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 5; name: BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq); status: COMPLETED; exit: 125; error: -; workDir: /Users/<USER>/projects/topology-tools/work/4a/56c4f261cdc502f584dc331edb6955]
Jun-09 09:56:28.038 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 09:56:28.038 [Task submitter] INFO  nextflow.Session - [8a/0793fe] Re-submitted process > BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq)
Jun-09 09:56:28.038 [Task monitor] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq); work-dir=/Users/<USER>/projects/topology-tools/work/4a/56c4f261cdc502f584dc331edb6955
  error [nextflow.exception.ProcessFailedException]: Process `BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq)` terminated with an error exit status (125)
Jun-09 09:56:28.038 [Task monitor] INFO  nextflow.processor.TaskProcessor - [4a/56c4f2] NOTE: Process `BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq)` terminated with an error exit status (125) -- Execution is retried (2)
Jun-09 09:56:28.042 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 09:56:28.043 [Task submitter] INFO  nextflow.Session - [78/094e10] Re-submitted process > BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq)
Jun-09 09:56:28.910 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 7; name: BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq); status: COMPLETED; exit: 125; error: -; workDir: /Users/<USER>/projects/topology-tools/work/8a/0793fe0a217726661db2062c0d7278]
Jun-09 09:56:28.910 [Task monitor] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq); work-dir=/Users/<USER>/projects/topology-tools/work/8a/0793fe0a217726661db2062c0d7278
  error [nextflow.exception.ProcessFailedException]: Process `BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq)` terminated with an error exit status (125)
Jun-09 09:56:28.916 [Task monitor] ERROR nextflow.processor.TaskProcessor - Error executing process > 'BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq)'

Caused by:
  Process `BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq)` terminated with an error exit status (125)

Command executed:

  # Find the reference genome file (follow symlinks with -L)
  bwt_file=$(find -L genome_index -name "*.bwt" | head -1)
  if [ -z "$bwt_file" ]; then
      echo "Error: No BWA index files found in genome_index"
      exit 1
  fi
  
  # Get the reference genome prefix (remove .bwt extension)
  genome_index_fa=${bwt_file%.bwt}
  
  echo "Using BWA index: $genome_index_fa"
  echo "Processing FASTQ files: small_rcmc-extra-reads_r1.fq.gz, small_rcmc-extra-reads_r2.fq.gz"
  echo "Output file: small_rcmc-extra-reads_r1.fq.pairsam.gz"
  
  # Run BWA alignment piped to pairtools
  bwa mem -5SP -T0 -t2 $genome_index_fa small_rcmc-extra-reads_r1.fq.gz small_rcmc-extra-reads_r2.fq.gz | \
  pairtools parse \
      --min-mapq 20 \
      --walks-policy 5unique \
      --max-inter-align-gap 30 \
      --add-columns pos5,pos3,dist_to_5,dist_to_3,read_len \
      --nproc-in 2 \
      --nproc-out 2 \
      --chroms-path test.chrom.sizes | \
  pairtools sort \
      --nproc 2 \
      -o small_rcmc-extra-reads_r1.fq.pairsam.gz
  
  # Verify output file was created
  if [ ! -f "small_rcmc-extra-reads_r1.fq.pairsam.gz" ]; then
      echo "Error: Output file small_rcmc-extra-reads_r1.fq.pairsam.gz was not created"
      exit 1
  fi
  
  echo "Successfully created small_rcmc-extra-reads_r1.fq.pairsam.gz"
  echo "File size: $(du -h small_rcmc-extra-reads_r1.fq.pairsam.gz)"
  
  # Create versions file
  cat <<-END_VERSIONS > versions.yml
  "BWA_ALIGN_PAIRTOOLS":
      bwa: $(echo $(bwa 2>&1) | sed 's/^.*Version: //; s/Contact:.*$//')
      pairtools: $(pairtools --version 2>&1 | sed 's/pairtools, version //')
      samtools: $(echo $(samtools --version 2>&1) | sed 's/^.*samtools //; s/Using.*$//')
  END_VERSIONS

Command exit status:
  125

Command output:
  (empty)

Command error:
  Unable to find image 'quay.io/biocontainers/mulled-v2-bwa-pairtools-samtools:latest' locally
  docker: Error response from daemon: unauthorized: access to the requested resource is not authorized.
  See 'docker run --help'.

Work dir:
  /Users/<USER>/projects/topology-tools/work/8a/0793fe0a217726661db2062c0d7278

Tip: view the complete command output by changing to the process work dir and entering the command `cat .command.out`
Jun-09 09:56:28.918 [Task monitor] DEBUG nextflow.Session - Session aborted -- Cause: Process `BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq)` terminated with an error exit status (125)
Jun-09 09:56:28.925 [Task monitor] DEBUG nextflow.Session - The following nodes are still active:
[process] MERGE_DEDUP_SPLIT
  status=ACTIVE
  port 0: (value) OPEN  ; channel: -
  port 1: (cntrl) -     ; channel: $

[process] COOLER_PROCESS
  status=ACTIVE
  port 0: (value) OPEN  ; channel: -
  port 1: (value) bound ; channel: chrom_sizes
  port 2: (cntrl) -     ; channel: $

[process] JUICER_HIC
  status=ACTIVE
  port 0: (value) OPEN  ; channel: -
  port 1: (value) bound ; channel: chrom_sizes
  port 2: (cntrl) -     ; channel: $

[process] QC_METRICS
  status=ACTIVE
  port 0: (value) OPEN  ; channel: -
  port 1: (cntrl) -     ; channel: $

Jun-09 09:56:28.926 [Task monitor] ERROR nextflow.Nextflow - Pipeline execution stopped with the following message: Unable to find image 'quay.io/biocontainers/mulled-v2-bwa-pairtools-samtools:latest' locally
docker: Error response from daemon: unauthorized: access to the requested resource is not authorized.
See 'docker run --help'.
Jun-09 09:56:28.928 [main] DEBUG nextflow.Session - Session await > all processes finished
Jun-09 09:56:28.928 [main] DEBUG nextflow.Session - Session await > all barriers passed
Jun-09 09:56:28.929 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 8; name: BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq); status: COMPLETED; exit: 125; error: -; workDir: /Users/<USER>/projects/topology-tools/work/78/094e101f85cfb708c655e3c4850748]
Jun-09 09:56:28.929 [Task monitor] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq); work-dir=/Users/<USER>/projects/topology-tools/work/78/094e101f85cfb708c655e3c4850748
  error [nextflow.exception.ProcessFailedException]: Process `BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq)` terminated with an error exit status (125)
Jun-09 09:56:28.929 [main] INFO  nextflow.Nextflow - 
    ========================================================================================
                            Pipeline Execution Summary
    ========================================================================================
    Completed at    : 2025-06-09T09:56:28.928863-04:00
    Duration        : 7.1s
    Success         : false
    Work directory  : /Users/<USER>/projects/topology-tools/work
    Exit status     : 125
    Error message   : Unable to find image 'quay.io/biocontainers/mulled-v2-bwa-pairtools-samtools:latest' locally
docker: Error response from daemon: unauthorized: access to the requested resource is not authorized.
See 'docker run --help'.
    ========================================================================================
    
Jun-09 09:56:28.930 [main] ERROR nextflow.Nextflow - 💥 Pipeline failed!
Jun-09 09:56:28.930 [main] ERROR nextflow.Nextflow - Check the error message above and workflow logs for details.
Jun-09 09:56:28.931 [Task monitor] DEBUG n.processor.TaskPollingMonitor - <<< barrier arrives (monitor: local) - terminating tasks monitor poll loop
Jun-09 09:56:28.933 [main] DEBUG n.trace.WorkflowStatsObserver - Workflow completed > WorkflowStats[succeededCount=2; failedCount=6; ignoredCount=0; cachedCount=0; pendingCount=0; submittedCount=0; runningCount=0; retriesCount=4; abortedCount=0; succeedDuration=4s; failedDuration=10.6s; cachedDuration=0ms;loadCpus=0; loadMemory=0; peakRunning=2; peakCpus=4; peakMemory=12 GB; ]
Jun-09 09:56:28.933 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow completed -- saving trace file
Jun-09 09:56:28.934 [main] DEBUG nextflow.trace.ReportObserver - Workflow completed -- rendering execution report
Jun-09 09:56:29.823 [main] DEBUG nextflow.trace.TimelineObserver - Workflow completed -- rendering execution timeline
Jun-09 09:56:29.922 [main] WARN  nextflow.dag.GraphvizRenderer - Graphviz is required to render the execution DAG in the given format -- See http://www.graphviz.org for more info.
Jun-09 09:56:29.926 [main] DEBUG nextflow.cache.CacheDB - Closing CacheDB done
Jun-09 09:56:29.936 [main] DEBUG nextflow.script.ScriptRunner - > Execution complete -- Goodbye
