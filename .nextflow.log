Jun-09 08:57:06.039 [main] DEBUG nextflow.cli.Launcher - $> nextflow run nf/main.nf -profile test
Jun-09 08:57:06.096 [main] DEBUG nextflow.cli.CmdRun - N E X T F L O W  ~  version 25.04.3
Jun-09 08:57:06.115 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/Users/<USER>/.nextflow/plugins; core-plugins: nf-amazon@2.15.0,nf-azure@1.16.0,nf-cloudcache@0.4.3,nf-codecommit@0.2.3,nf-console@1.2.1,nf-google@1.21.0,nf-k8s@1.0.0,nf-tower@1.11.3,nf-wave@1.12.1
Jun-09 08:57:06.136 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
Jun-09 08:57:06.137 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
Jun-09 08:57:06.139 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.12.0 in 'deployment' mode
Jun-09 08:57:06.146 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
Jun-09 08:57:06.158 [main] DEBUG nextflow.config.ConfigBuilder - Found config base: /Users/<USER>/projects/topology-tools/nf/nextflow.config
Jun-09 08:57:06.160 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /Users/<USER>/projects/topology-tools/nf/nextflow.config
Jun-09 08:57:06.181 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /Users/<USER>/.nextflow/secrets/store.json
Jun-09 08:57:06.184 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@5d235104] - activable => nextflow.secret.LocalSecretsProvider@5d235104
Jun-09 08:57:06.187 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `test`
Jun-09 08:57:06.738 [main] DEBUG nextflow.config.ConfigBuilder - Available config profiles: [cluster, debug, test, local, docker, cloud, shifter, mamba, charliecloud, conda, singularity, arm, test_full, podman]
Jun-09 08:57:06.756 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declaration
Jun-09 08:57:06.766 [main] DEBUG nextflow.cli.CmdRun - Launching `nf/main.nf` [magical_lamarck] DSL2 - revision: cb60860841
Jun-09 08:57:06.767 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
Jun-09 08:57:06.767 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[]
Jun-09 08:57:06.799 [main] DEBUG nextflow.Session - Session UUID: cf305e0c-fa50-44fe-9d98-6e613c4523e4
Jun-09 08:57:06.799 [main] DEBUG nextflow.Session - Run name: magical_lamarck
Jun-09 08:57:06.799 [main] DEBUG nextflow.Session - Executor pool size: 10
Jun-09 08:57:06.804 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
Jun-09 08:57:06.807 [main] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'FileTransfer' minSize=10; maxSize=30; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jun-09 08:57:06.821 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 25.04.3 build 5949
  Created: 02-06-2025 20:56 UTC (16:56 EDT)
  System: Mac OS X 15.4.1
  Runtime: Groovy 4.0.26 on OpenJDK 64-Bit Server VM 23.0.2+7
  Encoding: UTF-8 (UTF-8)
  Process: <EMAIL> [127.0.0.1]
  CPUs: 10 - Mem: 64 GB (1.4 GB) - Swap: 7 GB (1.4 GB)
Jun-09 08:57:06.829 [main] DEBUG nextflow.Session - Work-dir: /Users/<USER>/projects/topology-tools/work [Mac OS X]
Jun-09 08:57:06.830 [main] DEBUG nextflow.Session - Script base path does not exist or is not a directory: /Users/<USER>/projects/topology-tools/nf/bin
Jun-09 08:57:06.836 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
Jun-09 08:57:06.841 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
Jun-09 08:57:06.855 [main] DEBUG nextflow.Session - Observer factory (v2): LinObserverFactory
Jun-09 08:57:06.877 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
Jun-09 08:57:06.882 [main] DEBUG nextflow.util.CustomThreadPool - Creating default thread pool > poolSize: 11; maxThreads: 1000
Jun-09 08:57:06.912 [main] DEBUG nextflow.Session - Session start
Jun-09 08:57:06.915 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow started -- trace file: /Users/<USER>/projects/topology-tools/test_results/pipeline_info/execution_trace_2025-06-09_08-57-06.txt
Jun-09 08:57:07.066 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
Jun-09 08:57:07.348 [main] INFO  nextflow.Nextflow - 
========================================================================================
                        Micro-C Pipeline - Nextflow DSL2
========================================================================================
Sample ID       : small-rcmc
FASTQ R1        : tests/small-region-capture-micro-c/small_rcmc_r1.fq.gz,tests/small-region-capture-micro-c/small_rcmc-extra-reads_r1.fq.gz
FASTQ R2        : tests/small-region-capture-micro-c/small_rcmc_r2.fq.gz,tests/small-region-capture-micro-c/small_rcmc-extra-reads_r2.fq.gz
BWA Index       : tests/small-region-capture-micro-c/test_bwa_index.tgz
Chrom Sizes     : tests/small-region-capture-micro-c/test.chrom.sizes
Output Dir      : ./test_results
Resolution      : 1000
BWA Cores       : 2
Min MAPQ        : 20
========================================================================================

Jun-09 08:57:07.439 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name EXTRACT_BWA_INDEX
Jun-09 08:57:07.441 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:EXTRACT_BWA_INDEX` matches process EXTRACT_BWA_INDEX
Jun-09 08:57:07.450 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:57:07.450 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:57:07.454 [main] DEBUG nextflow.executor.Executor - [warm up] executor > local
Jun-09 08:57:07.459 [main] DEBUG n.processor.LocalPollingMonitor - Creating local task monitor for executor 'local' > cpus=10; memory=64 GB; capacity=10; pollInterval=100ms; dumpInterval=5m
Jun-09 08:57:07.460 [main] DEBUG n.processor.TaskPollingMonitor - >>> barrier register (monitor: local)
Jun-09 08:57:07.472 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'EXTRACT_BWA_INDEX': maxForks=0; fair=false; array=0
Jun-09 08:57:07.493 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name BWA_ALIGN_PAIRTOOLS
Jun-09 08:57:07.493 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:BWA_ALIGN_PAIRTOOLS` matches process BWA_ALIGN_PAIRTOOLS
Jun-09 08:57:07.495 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:57:07.495 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:57:07.495 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'BWA_ALIGN_PAIRTOOLS': maxForks=0; fair=false; array=0
Jun-09 08:57:07.507 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_high` matches labels `process_high` for process with name MERGE_DEDUP_SPLIT
Jun-09 08:57:07.507 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:MERGE_DEDUP_SPLIT` matches process MERGE_DEDUP_SPLIT
Jun-09 08:57:07.508 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:57:07.508 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:57:07.508 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'MERGE_DEDUP_SPLIT': maxForks=0; fair=false; array=0
Jun-09 08:57:07.513 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name COOLER_PROCESS
Jun-09 08:57:07.514 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:COOLER_PROCESS` matches process COOLER_PROCESS
Jun-09 08:57:07.515 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:57:07.515 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:57:07.516 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'COOLER_PROCESS': maxForks=0; fair=false; array=0
Jun-09 08:57:07.522 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_medium` matches labels `process_medium` for process with name JUICER_HIC
Jun-09 08:57:07.523 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:JUICER_HIC` matches process JUICER_HIC
Jun-09 08:57:07.524 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:57:07.524 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:57:07.524 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'JUICER_HIC': maxForks=0; fair=false; array=0
Jun-09 08:57:07.529 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withLabel:process_low` matches labels `process_low` for process with name QC_METRICS
Jun-09 08:57:07.529 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:QC_METRICS` matches process QC_METRICS
Jun-09 08:57:07.530 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: null
Jun-09 08:57:07.530 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
Jun-09 08:57:07.530 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'QC_METRICS': maxForks=0; fair=false; array=0
Jun-09 08:57:07.533 [main] DEBUG nextflow.Session - Workflow process names [dsl2]: MERGE_DEDUP_SPLIT, COOLER_PROCESS, EXTRACT_BWA_INDEX, JUICER_HIC, BWA_ALIGN_PAIRTOOLS, QC_METRICS
Jun-09 08:57:07.534 [main] DEBUG nextflow.Session - Igniting dataflow network (9)
Jun-09 08:57:07.540 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > EXTRACT_BWA_INDEX
Jun-09 08:57:07.540 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > BWA_ALIGN_PAIRTOOLS
Jun-09 08:57:07.540 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > MERGE_DEDUP_SPLIT
Jun-09 08:57:07.540 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > COOLER_PROCESS
Jun-09 08:57:07.540 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > JUICER_HIC
Jun-09 08:57:07.540 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > QC_METRICS
Jun-09 08:57:07.541 [main] DEBUG nextflow.script.ScriptRunner - Parsed script files:
  Script_43f01a504ba15b5e: /Users/<USER>/projects/topology-tools/nf/modules/merge_dedup_split.nf
  Script_6c968723c735dde5: /Users/<USER>/projects/topology-tools/nf/modules/qc_metrics.nf
  Script_3c186acb8ea2eb2c: /Users/<USER>/projects/topology-tools/nf/modules/juicer_hic.nf
  Script_900e8f290a0d3a82: /Users/<USER>/projects/topology-tools/nf/modules/cooler_process.nf
  Script_59383c090845de85: /Users/<USER>/projects/topology-tools/nf/main.nf
  Script_f4e17a1e4235a98c: /Users/<USER>/projects/topology-tools/nf/modules/extract_bwa_index.nf
  Script_e05012147db5f27a: /Users/<USER>/projects/topology-tools/nf/modules/bwa_align_pairtools.nf
Jun-09 08:57:07.541 [main] DEBUG nextflow.script.ScriptRunner - > Awaiting termination 
Jun-09 08:57:07.541 [main] DEBUG nextflow.Session - Session await
Jun-09 08:57:07.629 [Task submitter] WARN  n.executor.BashWrapperBuilder - Task runtime metrics are not reported when using macOS without a container engine
Jun-09 08:57:07.637 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:57:07.638 [Task submitter] INFO  nextflow.Session - [13/17161c] Submitted process > EXTRACT_BWA_INDEX (small_rcmc_r1.fq)
Jun-09 08:57:07.645 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:57:07.645 [Task submitter] INFO  nextflow.Session - [c6/828528] Submitted process > EXTRACT_BWA_INDEX (small_rcmc-extra-reads_r1.fq)
Jun-09 08:57:07.734 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 1; name: EXTRACT_BWA_INDEX (small_rcmc_r1.fq); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/13/17161c57717ab9f24fc37aad01632a]
Jun-09 08:57:07.735 [Task monitor] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'TaskFinalizer' minSize=10; maxSize=30; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
Jun-09 08:57:07.739 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 2; name: EXTRACT_BWA_INDEX (small_rcmc-extra-reads_r1.fq); status: COMPLETED; exit: 0; error: -; workDir: /Users/<USER>/projects/topology-tools/work/c6/828528796ffab2b3bf891154ab6f94]
Jun-09 08:57:07.767 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:57:07.767 [Task submitter] INFO  nextflow.Session - [17/0f5d71] Submitted process > BWA_ALIGN_PAIRTOOLS (small_rcmc_r1.fq)
Jun-09 08:57:07.771 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:57:07.772 [Task submitter] INFO  nextflow.Session - [61/ed147b] Submitted process > BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq)
Jun-09 08:57:07.846 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 4; name: BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq); status: COMPLETED; exit: 127; error: -; workDir: /Users/<USER>/projects/topology-tools/work/61/ed147b1683e449696786f8e5509938]
Jun-09 08:57:07.849 [TaskFinalizer-3] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq); work-dir=/Users/<USER>/projects/topology-tools/work/61/ed147b1683e449696786f8e5509938
  error [nextflow.exception.ProcessFailedException]: Process `BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq)` terminated with an error exit status (127)
Jun-09 08:57:07.854 [TaskFinalizer-3] INFO  nextflow.processor.TaskProcessor - [61/ed147b] NOTE: Process `BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq)` terminated with an error exit status (127) -- Execution is retried (1)
Jun-09 08:57:07.860 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:57:07.860 [Task submitter] INFO  nextflow.Session - [f3/2014e6] Re-submitted process > BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq)
Jun-09 08:57:07.921 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 5; name: BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq); status: COMPLETED; exit: 127; error: -; workDir: /Users/<USER>/projects/topology-tools/work/f3/2014e6aa597ec95f9c0fd62d08ad1b]
Jun-09 08:57:07.921 [TaskFinalizer-4] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq); work-dir=/Users/<USER>/projects/topology-tools/work/f3/2014e6aa597ec95f9c0fd62d08ad1b
  error [nextflow.exception.ProcessFailedException]: Process `BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq)` terminated with an error exit status (127)
Jun-09 08:57:07.922 [TaskFinalizer-4] INFO  nextflow.processor.TaskProcessor - [f3/2014e6] NOTE: Process `BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq)` terminated with an error exit status (127) -- Execution is retried (2)
Jun-09 08:57:07.926 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
Jun-09 08:57:07.927 [Task submitter] INFO  nextflow.Session - [ce/9acc8c] Re-submitted process > BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq)
Jun-09 08:57:07.985 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 6; name: BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq); status: COMPLETED; exit: 127; error: -; workDir: /Users/<USER>/projects/topology-tools/work/ce/9acc8c89ae97d7711f36c3c4947851]
Jun-09 08:57:07.986 [TaskFinalizer-5] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq); work-dir=/Users/<USER>/projects/topology-tools/work/ce/9acc8c89ae97d7711f36c3c4947851
  error [nextflow.exception.ProcessFailedException]: Process `BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq)` terminated with an error exit status (127)
Jun-09 08:57:07.993 [TaskFinalizer-5] ERROR nextflow.processor.TaskProcessor - Error executing process > 'BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq)'

Caused by:
  Process `BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq)` terminated with an error exit status (127)


Command executed:

  # Find the reference genome file (follow symlinks with -L)
  bwt_file=$(find -L genome_index -name "*.bwt" | head -1)
  if [ -z "$bwt_file" ]; then
      echo "Error: No BWA index files found in genome_index"
      exit 1
  fi
  
  # Get the reference genome prefix (remove .bwt extension)
  genome_index_fa=${bwt_file%.bwt}
  
  echo "Using BWA index: $genome_index_fa"
  echo "Processing FASTQ files: small_rcmc-extra-reads_r1.fq.gz, small_rcmc-extra-reads_r2.fq.gz"
  echo "Output file: small_rcmc-extra-reads_r1.fq.pairsam.gz"
  
  # Run BWA alignment piped to pairtools
  bwa mem -5SP -T0 -t2 $genome_index_fa small_rcmc-extra-reads_r1.fq.gz small_rcmc-extra-reads_r2.fq.gz | \
  pairtools parse \
      --min-mapq 20 \
      --walks-policy 5unique \
      --max-inter-align-gap 30 \
      --add-columns pos5,pos3,dist_to_5,dist_to_3,read_len \
      --nproc-in 2 \
      --nproc-out 2 \
      --chroms-path test.chrom.sizes | \
  pairtools sort \
      --nproc 2 \
      -o small_rcmc-extra-reads_r1.fq.pairsam.gz
  
  # Verify output file was created
  if [ ! -f "small_rcmc-extra-reads_r1.fq.pairsam.gz" ]; then
      echo "Error: Output file small_rcmc-extra-reads_r1.fq.pairsam.gz was not created"
      exit 1
  fi
  
  echo "Successfully created small_rcmc-extra-reads_r1.fq.pairsam.gz"
  echo "File size: $(du -h small_rcmc-extra-reads_r1.fq.pairsam.gz)"
  
  # Create versions file
  cat <<-END_VERSIONS > versions.yml
  "BWA_ALIGN_PAIRTOOLS":
      bwa: $(echo $(bwa 2>&1) | sed 's/^.*Version: //; s/Contact:.*$//')
      pairtools: $(pairtools --version 2>&1 | sed 's/pairtools, version //')
      samtools: $(echo $(samtools --version 2>&1) | sed 's/^.*samtools //; s/Using.*$//')
  END_VERSIONS

Command exit status:
  127

Command output:
  Using BWA index: genome_index/test.fa.gz
  Processing FASTQ files: small_rcmc-extra-reads_r1.fq.gz, small_rcmc-extra-reads_r2.fq.gz
  Output file: small_rcmc-extra-reads_r1.fq.pairsam.gz

Command error:
  Using BWA index: genome_index/test.fa.gz
  Processing FASTQ files: small_rcmc-extra-reads_r1.fq.gz, small_rcmc-extra-reads_r2.fq.gz
  Output file: small_rcmc-extra-reads_r1.fq.pairsam.gz
  .command.sh: line 18: pairtools: command not found
  .command.sh: line 26: pairtools: command not found
  [M::bwa_idx_load_from_disk] read 0 ALT contigs
  [M::process] read 200 sequences (20200 bp)...
  [M::mem_pestat] # candidate unique pairs for (FF, FR, RF, RR): (3, 17, 3, 2)
  [M::mem_pestat] skip orientation FF as there are not enough pairs
  [M::mem_pestat] analyzing insert size distribution for orientation FR...
  [M::mem_pestat] (25, 50, 75) percentile: (190, 210, 1082)
  [M::mem_pestat] low and high boundaries for computing mean and std.dev: (1, 2866)
  [M::mem_pestat] mean and std.dev: (351.53, 335.61)
  [M::mem_pestat] low and high boundaries for proper pairs: (1, 3758)
  [M::mem_pestat] skip orientation RF as there are not enough pairs
  [M::mem_pestat] skip orientation RR as there are not enough pairs
  [M::mem_process_seqs] Processed 200 reads in 0.012 CPU sec, 0.006 real sec
  [fputs] Broken pipe

Work dir:
  /Users/<USER>/projects/topology-tools/work/ce/9acc8c89ae97d7711f36c3c4947851

Tip: you can replicate the issue by changing to the process work dir and entering the command `bash .command.run`
Jun-09 08:57:07.995 [TaskFinalizer-5] DEBUG nextflow.Session - Session aborted -- Cause: Process `BWA_ALIGN_PAIRTOOLS (small_rcmc-extra-reads_r1.fq)` terminated with an error exit status (127)
Jun-09 08:57:08.004 [TaskFinalizer-5] DEBUG nextflow.Session - The following nodes are still active:
[process] MERGE_DEDUP_SPLIT
  status=ACTIVE
  port 0: (value) OPEN  ; channel: -
  port 1: (cntrl) -     ; channel: $

[process] COOLER_PROCESS
  status=ACTIVE
  port 0: (value) OPEN  ; channel: -
  port 1: (value) bound ; channel: chrom_sizes
  port 2: (cntrl) -     ; channel: $

[process] JUICER_HIC
  status=ACTIVE
  port 0: (value) OPEN  ; channel: -
  port 1: (value) bound ; channel: chrom_sizes
  port 2: (cntrl) -     ; channel: $

[process] QC_METRICS
  status=ACTIVE
  port 0: (value) OPEN  ; channel: -
  port 1: (cntrl) -     ; channel: $

Jun-09 08:57:08.006 [TaskFinalizer-5] ERROR nextflow.Nextflow - Pipeline execution stopped with the following message: Using BWA index: genome_index/test.fa.gz
Processing FASTQ files: small_rcmc-extra-reads_r1.fq.gz, small_rcmc-extra-reads_r2.fq.gz
Output file: small_rcmc-extra-reads_r1.fq.pairsam.gz
/Users/<USER>/projects/topology-tools/work/ce/9acc8c89ae97d7711f36c3c4947851/.command.sh: line 18: pairtools: command not found
/Users/<USER>/projects/topology-tools/work/ce/9acc8c89ae97d7711f36c3c4947851/.command.sh: line 26: pairtools: command not found
[M::bwa_idx_load_from_disk] read 0 ALT contigs
[M::process] read 200 sequences (20200 bp)...
[M::mem_pestat] # candidate unique pairs for (FF, FR, RF, RR): (3, 17, 3, 2)
[M::mem_pestat] skip orientation FF as there are not enough pairs
[M::mem_pestat] analyzing insert size distribution for orientation FR...
[M::mem_pestat] (25, 50, 75) percentile: (190, 210, 1082)
[M::mem_pestat] low and high boundaries for computing mean and std.dev: (1, 2866)
[M::mem_pestat] mean and std.dev: (351.53, 335.61)
[M::mem_pestat] low and high boundaries for proper pairs: (1, 3758)
[M::mem_pestat] skip orientation RF as there are not enough pairs
[M::mem_pestat] skip orientation RR as there are not enough pairs
[M::mem_process_seqs] Processed 200 reads in 0.012 CPU sec, 0.006 real sec
[fputs] Broken pipe
Jun-09 08:57:08.006 [main] DEBUG nextflow.Session - Session await > all processes finished
Jun-09 08:57:08.006 [main] DEBUG nextflow.Session - Session await > all barriers passed
Jun-09 08:57:08.007 [Task monitor] DEBUG n.processor.TaskPollingMonitor - <<< barrier arrives (monitor: local) - terminating tasks monitor poll loop
Jun-09 08:57:08.007 [TaskFinalizer-2] ERROR nextflow.processor.PublishDir - Failed to publish file: /Users/<USER>/projects/topology-tools/work/c6/828528796ffab2b3bf891154ab6f94/genome_index; to: /Users/<USER>/projects/topology-tools/test_results/align/[:]/genome_index/genome_index [symlink] -- See log file for details
dev.failsafe.FailsafeException: java.lang.InterruptedException: sleep interrupted
	at dev.failsafe.FailsafeExecutor.call(FailsafeExecutor.java:444)
	at dev.failsafe.FailsafeExecutor.get(FailsafeExecutor.java:129)
	at nextflow.processor.PublishDir.retryableProcessFile(PublishDir.groovy:416)
	at nextflow.processor.PublishDir.safeProcessFile(PublishDir.groovy:387)
	at nextflow.processor.PublishDir.apply1(PublishDir.groovy:359)
	at nextflow.processor.PublishDir.apply0(PublishDir.groovy:266)
	at nextflow.processor.PublishDir.apply(PublishDir.groovy:324)
	at org.codehaus.groovy.vmplugin.v8.IndyInterface.fromCache(IndyInterface.java:321)
	at nextflow.processor.TaskProcessor.publishOutputs0(TaskProcessor.groovy:1420)
	at nextflow.processor.TaskProcessor.publishOutputs(TaskProcessor.groovy:1395)
	at org.codehaus.groovy.vmplugin.v8.IndyInterface.fromCache(IndyInterface.java:321)
	at nextflow.processor.TaskProcessor.finalizeTask0(TaskProcessor.groovy:2437)
	at org.codehaus.groovy.vmplugin.v8.IndyInterface.fromCache(IndyInterface.java:321)
	at nextflow.processor.TaskProcessor.finalizeTask(TaskProcessor.groovy:2408)
	at nextflow.processor.TaskPollingMonitor.finalizeTask(TaskPollingMonitor.groovy:696)
	at nextflow.processor.TaskPollingMonitor.safeFinalizeTask(TaskPollingMonitor.groovy:686)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.codehaus.groovy.reflection.CachedMethod.invoke(CachedMethod.java:343)
	at groovy.lang.MetaMethod.doMethodInvoke(MetaMethod.java:328)
	at groovy.lang.MetaClassImpl.doInvokeMethod(MetaClassImpl.java:1333)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1088)
	at groovy.lang.MetaClassImpl.invokeMethod(MetaClassImpl.java:1007)
	at org.codehaus.groovy.runtime.InvokerHelper.invokePogoMethod(InvokerHelper.java:645)
	at org.codehaus.groovy.runtime.InvokerHelper.invokeMethod(InvokerHelper.java:628)
	at org.codehaus.groovy.runtime.InvokerHelper.invokeMethodSafe(InvokerHelper.java:82)
	at nextflow.processor.TaskPollingMonitor$_checkTaskStatus_lambda8.doCall(TaskPollingMonitor.groovy:676)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:572)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1575)
Caused by: java.lang.InterruptedException: sleep interrupted
	at java.base/java.lang.Thread.sleepNanos0(Native Method)
	at java.base/java.lang.Thread.sleepNanos(Thread.java:496)
	at java.base/java.lang.Thread.sleep(Thread.java:527)
	at dev.failsafe.internal.RetryPolicyExecutor.lambda$apply$0(RetryPolicyExecutor.java:90)
	at dev.failsafe.SyncExecutionImpl.executeSync(SyncExecutionImpl.java:176)
	at dev.failsafe.FailsafeExecutor.call(FailsafeExecutor.java:437)
	... 31 common frames omitted
Jun-09 08:57:08.021 [main] INFO  nextflow.Nextflow - 
    ========================================================================================
                            Pipeline Execution Summary
    ========================================================================================
    Completed at    : 2025-06-09T08:57:08.007128-04:00
    Duration        : 1.1s
    Success         : false
    Work directory  : /Users/<USER>/projects/topology-tools/work
    Exit status     : 127
    Error message   : Using BWA index: genome_index/test.fa.gz
Processing FASTQ files: small_rcmc-extra-reads_r1.fq.gz, small_rcmc-extra-reads_r2.fq.gz
Output file: small_rcmc-extra-reads_r1.fq.pairsam.gz
/Users/<USER>/projects/topology-tools/work/ce/9acc8c89ae97d7711f36c3c4947851/.command.sh: line 18: pairtools: command not found
/Users/<USER>/projects/topology-tools/work/ce/9acc8c89ae97d7711f36c3c4947851/.command.sh: line 26: pairtools: command not found
[M::bwa_idx_load_from_disk] read 0 ALT contigs
[M::process] read 200 sequences (20200 bp)...
[M::mem_pestat] # candidate unique pairs for (FF, FR, RF, RR): (3, 17, 3, 2)
[M::mem_pestat] skip orientation FF as there are not enough pairs
[M::mem_pestat] analyzing insert size distribution for orientation FR...
[M::mem_pestat] (25, 50, 75) percentile: (190, 210, 1082)
[M::mem_pestat] low and high boundaries for computing mean and std.dev: (1, 2866)
[M::mem_pestat] mean and std.dev: (351.53, 335.61)
[M::mem_pestat] low and high boundaries for proper pairs: (1, 3758)
[M::mem_pestat] skip orientation RF as there are not enough pairs
[M::mem_pestat] skip orientation RR as there are not enough pairs
[M::mem_process_seqs] Processed 200 reads in 0.012 CPU sec, 0.006 real sec
[fputs] Broken pipe
    ========================================================================================
    
Jun-09 08:57:08.023 [main] ERROR nextflow.Nextflow - 💥 Pipeline failed!
Jun-09 08:57:08.024 [main] ERROR nextflow.Nextflow - Check the error message above and workflow logs for details.
Jun-09 08:57:08.027 [main] WARN  n.processor.TaskPollingMonitor - Killing running tasks (1)
Jun-09 08:57:08.039 [main] DEBUG n.trace.WorkflowStatsObserver - Workflow completed > WorkflowStats[succeededCount=2; failedCount=3; ignoredCount=0; cachedCount=0; pendingCount=0; submittedCount=0; runningCount=0; retriesCount=2; abortedCount=1; succeedDuration=246ms; failedDuration=28ms; cachedDuration=0ms;loadCpus=0; loadMemory=0; peakRunning=3; peakCpus=6; peakMemory=16 GB; ]
Jun-09 08:57:08.039 [main] DEBUG nextflow.trace.TraceFileObserver - Workflow completed -- saving trace file
Jun-09 08:57:08.041 [main] DEBUG nextflow.trace.ReportObserver - Workflow completed -- rendering execution report
Jun-09 08:57:08.917 [main] DEBUG nextflow.trace.TimelineObserver - Workflow completed -- rendering execution timeline
Jun-09 08:57:09.013 [main] WARN  nextflow.dag.GraphvizRenderer - Graphviz is required to render the execution DAG in the given format -- See http://www.graphviz.org for more info.
Jun-09 08:57:09.016 [main] DEBUG nextflow.cache.CacheDB - Closing CacheDB done
Jun-09 08:57:09.027 [main] DEBUG nextflow.script.ScriptRunner - > Execution complete -- Goodbye
