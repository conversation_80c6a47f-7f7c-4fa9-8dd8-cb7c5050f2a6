#!/usr/bin/env python3

import argparse
import os
import subprocess
import time
import glob
import shutil
import logging
from datetime import datetime

def setup_logging():
    """Configure logging"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler("microc_pipeline.log"),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger("microc_pipeline")

def run_command(cmd, log, task_name):
    """Run a shell command and log its execution time"""
    log.info(f"Starting {task_name}")
    start_time = time.time()
    
    log.info(f"Command: {cmd}")
    process = subprocess.Popen(cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    stdout, stderr = process.communicate()
    
    if process.returncode != 0:
        log.error(f"{task_name} failed with return code {process.returncode}")
        log.error(f"STDERR: {stderr.decode()}")
        raise Exception(f"{task_name} failed")
    
    end_time = time.time()
    log.info(f"Completed {task_name} in {end_time - start_time:.2f} seconds")
    return stdout.decode()

def sum_fastq_size(fastq_r1_files, fastq_r2_files, log):
    """Calculate total size of fastq files in GB"""
    total_size = 0
    for f in fastq_r1_files + fastq_r2_files:
        total_size += os.path.getsize(f)
    
    size_gb = round(total_size / (1024**3))
    log.info(f"Total fastq size: {size_gb} GB")
    return size_gb

def microc_align(fastq_r1, fastq_r2, sample_id, reference_index, reference_index_prefix, 
                chrom_sizes, bwa_cores, mapq, output_dir, log):
    """Run the microc_align step"""
    
    chunk_id = os.path.basename(fastq_r1).replace("_R1.fastq.gz", "")
    output_pairsam = os.path.join(output_dir, f"{chunk_id}.pairsam.gz")
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Prepare genome index
    genome_index_dir = os.path.join(output_dir, "genome_index")
    os.makedirs(genome_index_dir, exist_ok=True)
    
    if reference_index:
        cmd = f"tar zxvf {reference_index} -C {genome_index_dir}"
        run_command(cmd, log, "Extract reference index")
    elif reference_index_prefix:
        cmd = f"""
        cd {genome_index_dir} && 
        gsutil cp {reference_index_prefix}.amb . && 
        gsutil cp {reference_index_prefix}.ann . && 
        gsutil cp {reference_index_prefix}.bwt . && 
        gsutil cp {reference_index_prefix}.pac . && 
        gsutil cp {reference_index_prefix}.sa .
        """
        run_command(cmd, log, "Download reference index")
    else:
        raise Exception("Either reference_index or reference_index_prefix must be provided")
    
    # Find genome index name
    cmd = f"find {genome_index_dir} -name '*.bwt'"
    bwt_file = run_command(cmd, log, "Find bwt file").strip()
    genome_index_fa = bwt_file.replace(".bwt", "")
    
    # Run alignment and pairtools
    cmd = f"""
    bwa mem -5SP -T0 -t{bwa_cores} {genome_index_fa} {fastq_r1} {fastq_r2} | \
    pairtools parse --min-mapq {mapq} --walks-policy 5unique \
    --max-inter-align-gap 30 --add-columns pos5,pos3,dist_to_5,dist_to_3,read_len \
    --nproc-in {bwa_cores} --nproc-out {bwa_cores} --chroms-path {chrom_sizes} | \
    pairtools sort --nproc {bwa_cores} -o {output_pairsam}
    """
    
    run_command(cmd, log, f"Align and process {chunk_id}")
    return output_pairsam

def merge_pairs(pairsams, sample_id, output_dir, log):
    """Run the merge_pairs step"""
    
    os.makedirs(output_dir, exist_ok=True)
    
    # Output files
    mapped_pairs = os.path.join(output_dir, f"{sample_id}.mapped.pairs")
    stats_file = os.path.join(output_dir, f"{sample_id}.stats.txt")
    bam_file = os.path.join(output_dir, f"{sample_id}.bam")
    
    # Merge, dedup, and split
    pairsams_str = " ".join(pairsams)
    cmd = f"""
    pairtools merge --nproc 12 {pairsams_str} | \
    pairtools dedup --nproc-in 2 --nproc-out 8 --mark-dups --output-stats {stats_file} | \
    pairtools split --nproc-in 2 --nproc-out 8 --output-pairs {mapped_pairs} --output-sam - | \
    samtools view -bS -@6 | \
    samtools sort -@6 -o {bam_file}
    """
    
    run_command(cmd, log, "Merge, dedup, and split pairs")
    
    # Index BAM
    cmd = f"samtools index {bam_file}"
    run_command(cmd, log, "Index BAM")
    
    return {
        "mapped_pairs": mapped_pairs,
        "stats_file": stats_file,
        "bam_file": bam_file,
        "bai_file": f"{bam_file}.bai"
    }

def juicer_hic(sample_id, chrom_sizes, mapped_pairs, output_dir, cores, log):
    """Run the juicer_hic step"""
    
    os.makedirs(output_dir, exist_ok=True)
    hic_file = os.path.join(output_dir, f"{sample_id}.hic")
    
    cmd = f"""
    java -Xmx120g -Djava.awt.headless=true -jar /usr/local/bin/juicer_tools_1.22.01.jar pre \
        --threads {cores} \
        {mapped_pairs} \
        {hic_file} \
        {chrom_sizes}
    """
    
    run_command(cmd, log, "Generate Juicer HiC file")
    return hic_file

def cooler_process(sample_id, chrom_sizes, mapped_pairs, resolution, output_dir, log):
    """Run the cooler step"""
    
    os.makedirs(output_dir, exist_ok=True)
    cool_file = os.path.join(output_dir, f"{sample_id}.cool")
    raw_mcool = os.path.join(output_dir, f"{sample_id}.raw.mcool")
    balanced_mcool = os.path.join(output_dir, f"{sample_id}.balanced.mcool")
    
    # Generate .cool file
    cmd = f"cooler cload pairs -c1 2 -p1 3 -c2 4 -p2 5 {chrom_sizes}:{resolution} {mapped_pairs} {cool_file}"
    run_command(cmd, log, "Generate cooler file")
    
    # Generate raw mcool file
    cmd = f"cooler zoomify --resolutions {resolution}N -o {raw_mcool} -p 4 {cool_file}"
    run_command(cmd, log, "Generate raw mcool file")
    
    # Generate balanced mcool file
    cmd = f"cooler zoomify --resolutions {resolution}N -o {balanced_mcool} -p 4 --balance --balance-args '--nproc 4' {cool_file}"
    run_command(cmd, log, "Generate balanced mcool file")
    
    return {
        "cool_file": cool_file,
        "raw_mcool": raw_mcool,
        "balanced_mcool": balanced_mcool
    }

def run_qc(sample_id, stats_file, output_dir, log):
    """Run QC stats extraction"""
    
    os.makedirs(output_dir, exist_ok=True)
    qc_file = os.path.join(output_dir, f"{sample_id}_qc.txt")
    
    # Extract QC metrics
    cmd = f"""
    cat {stats_file} | grep -w "total" | cut -f2 > {qc_file}
    cat {stats_file} | grep -w "total_mapped" | cut -f2 >> {qc_file}
    cat {stats_file} | grep -w "total_nodups" | cut -f2 >> {qc_file}
    cat {stats_file} | grep -w "cis_1kb+" | cut -f2 >> {qc_file}
    cat {stats_file} | grep -w "cis_10kb+" | cut -f2 >> {qc_file}
    """
    
    run_command(cmd, log, "Extract QC metrics")
    
    # Read QC metrics
    with open(qc_file, 'r') as f:
        qc_stats = [line.strip() for line in f]
    
    qc_results = {
        "reads_total": qc_stats[0],
        "reads_mapped": qc_stats[1],
        "reads_nodups": qc_stats[2],
        "reads_cis_1kb": qc_stats[3],
        "reads_cis_10kb": qc_stats[4]
    }
    
    log.info(f"QC Results: {qc_results}")
    return qc_results

def main():
    parser = argparse.ArgumentParser(description="Standalone Micro-C Pipeline")
    
    # Required arguments
    parser.add_argument("--sample_id", required=True, help="Sample ID")
    parser.add_argument("--fastq_r1", required=True, nargs="+", help="R1 FASTQ files")
    parser.add_argument("--fastq_r2", required=True, nargs="+", help="R2 FASTQ files")
    parser.add_argument("--chrom_sizes", required=True, help="Chromosome sizes file")
    
    # Optional arguments
    parser.add_argument("--reference_bwa_idx", help="BWA index tar.gz file")
    parser.add_argument("--reference_bwa_idx_prefix", help="BWA index prefix (GCS URI)")
    parser.add_argument("--output_dir", default="microc_output", help="Output directory")
    parser.add_argument("--mapq", default="20", help="Minimum mapping quality")
    parser.add_argument("--bwa_cores", type=int, default=5, help="Number of cores for BWA")
    parser.add_argument("--resolution", default="10000", help="Resolution for cooler")
    
    args = parser.parse_args()
    
    # Setup logging
    log = setup_logging()
    log.info(f"Starting Micro-C pipeline for sample {args.sample_id}")
    
    # Create output directories
    align_dir = os.path.join(args.output_dir, "align")
    merge_dir = os.path.join(args.output_dir, "merge")
    hic_dir = os.path.join(args.output_dir, "hic")
    cooler_dir = os.path.join(args.output_dir, "cooler")
    qc_dir = os.path.join(args.output_dir, "qc")
    
    # Calculate total fastq size
    total_size_gb = sum_fastq_size(args.fastq_r1, args.fastq_r2, log)
    
    # Run alignment for each fastq pair
    pairsam_files = []
    for i, (r1, r2) in enumerate(zip(args.fastq_r1, args.fastq_r2)):
        log.info(f"Processing FASTQ pair {i+1}/{len(args.fastq_r1)}")
        pairsam = microc_align(
            r1, r2, args.sample_id, args.reference_bwa_idx, args.reference_bwa_idx_prefix,
            args.chrom_sizes, args.bwa_cores, args.mapq, 
            os.path.join(align_dir, f"chunk_{i}"), log
        )
        pairsam_files.append(pairsam)
    
    # Merge pairs
    merge_results = merge_pairs(pairsam_files, args.sample_id, merge_dir, log)
    
    # Run juicer_hic
    hic_file = juicer_hic(args.sample_id, args.chrom_sizes, merge_results["mapped_pairs"], 
                         hic_dir, args.bwa_cores, log)
    
    # Run cooler
    cooler_results = cooler_process(args.sample_id, args.chrom_sizes, merge_results["mapped_pairs"], 
                                  args.resolution, cooler_dir, log)
    
    # Run QC
    qc_results = run_qc(args.sample_id, merge_results["stats_file"], qc_dir, log)
    
    # Print summary
    log.info("Pipeline completed successfully")
    log.info(f"Output files:")
    log.info(f"  Mapped pairs: {merge_results['mapped_pairs']}")
    log.info(f"  BAM file: {merge_results['bam_file']}")
    log.info(f"  HiC file: {hic_file}")
    log.info(f"  Raw mcool file: {cooler_results['raw_mcool']}")
    log.info(f"  Balanced mcool file: {cooler_results['balanced_mcool']}")
    log.info(f"  QC stats: {qc_results}")

if __name__ == "__main__":
    main()