#!/usr/bin/env python3
"""
Performance analysis and bottleneck identification for the Micro-C pipeline.
Analyzes benchmark results and provides optimization recommendations.
"""

import json
import re

def parse_timing_from_stderr(stderr_text):
    """Extract detailed timing information from stderr log"""
    timing_data = {}
    
    # Parse timing information from log lines
    lines = stderr_text.split('\n')
    for line in lines:
        if "Completed" in line and "in" in line and "seconds" in line:
            # Extract step name and timing
            match = re.search(r'Completed (.+?) in ([\d.]+) seconds', line)
            if match:
                step_name = match.group(1)
                time_seconds = float(match.group(2))
                timing_data[step_name] = time_seconds
    
    return timing_data

def analyze_performance():
    """Analyze performance results and identify bottlenecks"""
    
    # Load benchmark results
    with open('benchmark_results/benchmark_summary.json', 'r') as f:
        results = json.load(f)
    
    print("=== MICRO-C PIPELINE PERFORMANCE ANALYSIS ===\n")
    
    # Overall performance summary
    print("1. OVERALL PERFORMANCE SUMMARY")
    print("-" * 40)
    
    for result in results:
        sample_id = result['sample_id']
        total_time = result['total_time_minutes']
        input_size = result['input_size_gb']
        throughput = result['throughput_gb_per_hour']
        memory = result['peak_memory_gb']
        
        print(f"\nSample: {sample_id}")
        print(f"  Input size: {input_size:.3f} GB")
        print(f"  Total time: {total_time:.2f} minutes")
        print(f"  Throughput: {throughput:.2f} GB/hour")
        print(f"  Peak memory: {memory:.3f} GB")
        
        # Parse detailed timing from stderr
        if 'stderr' in result:
            timing_data = parse_timing_from_stderr(result['stderr'])
            if timing_data:
                print(f"  Step-by-step timing:")
                total_step_time = sum(timing_data.values())
                for step, time_sec in sorted(timing_data.items(), key=lambda x: x[1], reverse=True):
                    percentage = (time_sec / total_step_time) * 100 if total_step_time > 0 else 0
                    print(f"    {step}: {time_sec:.2f}s ({percentage:.1f}%)")
    
    # Scaling analysis
    print("\n\n2. SCALING ANALYSIS")
    print("-" * 40)
    
    if len(results) >= 2:
        small_result = next((r for r in results if 'small' in r['sample_id']), None)
        large_result = next((r for r in results if 'large' in r['sample_id']), None)
        
        if small_result and large_result:
            size_ratio = large_result['input_size_gb'] / small_result['input_size_gb']
            time_ratio = large_result['total_time_minutes'] / small_result['total_time_minutes']
            
            print(f"Size increase: {size_ratio:.1f}x")
            print(f"Time increase: {time_ratio:.1f}x")
            print(f"Scaling efficiency: {(size_ratio/time_ratio)*100:.1f}%")
            
            if time_ratio > size_ratio * 1.2:
                print("⚠️  Pipeline shows sub-linear scaling - potential bottlenecks present")
            else:
                print("✅ Pipeline shows good linear scaling")
    
    # Bottleneck identification
    print("\n\n3. BOTTLENECK IDENTIFICATION")
    print("-" * 40)
    
    bottlenecks = []
    
    for result in results:
        if 'stderr' in result:
            timing_data = parse_timing_from_stderr(result['stderr'])
            if timing_data:
                total_time = sum(timing_data.values())
                
                # Identify steps taking >20% of total time
                for step, time_sec in timing_data.items():
                    percentage = (time_sec / total_time) * 100
                    if percentage > 20:
                        bottlenecks.append({
                            'sample': result['sample_id'],
                            'step': step,
                            'time_seconds': time_sec,
                            'percentage': percentage
                        })
    
    if bottlenecks:
        print("Major bottlenecks (>20% of total time):")
        for bottleneck in sorted(bottlenecks, key=lambda x: x['percentage'], reverse=True):
            print(f"  {bottleneck['step']}: {bottleneck['time_seconds']:.1f}s ({bottleneck['percentage']:.1f}%) in {bottleneck['sample']}")
    else:
        print("No major bottlenecks identified (no single step >20% of total time)")
    
    # Optimization recommendations
    print("\n\n4. OPTIMIZATION RECOMMENDATIONS")
    print("-" * 40)
    
    recommendations = []
    
    # Analyze specific bottlenecks
    for result in results:
        if 'stderr' in result:
            timing_data = parse_timing_from_stderr(result['stderr'])
            if timing_data:
                total_time = sum(timing_data.values())
                
                # Check alignment step
                align_steps = [k for k in timing_data.keys() if 'Align and process' in k]
                if align_steps:
                    align_time = sum(timing_data[step] for step in align_steps)
                    align_pct = (align_time / total_time) * 100
                    if align_pct > 50:
                        recommendations.append(
                            f"🔧 Alignment is {align_pct:.1f}% of total time. Consider:\n"
                            "   - Increasing BWA cores (--bwa_cores)\n"
                            "   - Using faster storage (SSD)\n"
                            "   - Splitting large FASTQ files for parallel processing"
                        )
                
                # Check merge/dedup step
                if 'Merge, dedup, and split pairs' in timing_data:
                    merge_time = timing_data['Merge, dedup, and split pairs']
                    merge_pct = (merge_time / total_time) * 100
                    if merge_pct > 30:
                        recommendations.append(
                            f"🔧 Merge/dedup is {merge_pct:.1f}% of total time. Consider:\n"
                            "   - Increasing pairtools cores\n"
                            "   - Using more memory for sorting\n"
                            "   - Optimizing I/O with faster storage"
                        )
                
                # Check cooler steps
                cooler_steps = [k for k in timing_data.keys() if 'cooler' in k.lower() or 'mcool' in k.lower()]
                if cooler_steps:
                    cooler_time = sum(timing_data[step] for step in cooler_steps)
                    cooler_pct = (cooler_time / total_time) * 100
                    if cooler_pct > 20:
                        recommendations.append(
                            f"🔧 Cooler processing is {cooler_pct:.1f}% of total time. Consider:\n"
                            "   - Increasing cooler parallelization\n"
                            "   - Using lower resolution for initial analysis\n"
                            "   - Skipping balance step for large datasets"
                        )
    
    # Memory analysis
    max_memory = max(r['peak_memory_gb'] for r in results)
    if max_memory < 1.0:
        recommendations.append(
            "💾 Memory usage is very low (<1GB). Pipeline is likely I/O bound rather than memory bound."
        )
    
    # Throughput analysis
    avg_throughput = sum(r['throughput_gb_per_hour'] for r in results) / len(results)
    if avg_throughput < 10:
        recommendations.append(
            f"⚡ Low throughput ({avg_throughput:.1f} GB/hour). For production use:\n"
            "   - Increase parallelization (more cores)\n"
            "   - Use faster storage (NVMe SSD)\n"
            "   - Consider distributed processing for very large datasets"
        )
    
    if recommendations:
        for i, rec in enumerate(recommendations, 1):
            print(f"{i}. {rec}\n")
    else:
        print("✅ Pipeline performance appears well-optimized for current dataset sizes.")
    
    # Scalability projections
    print("\n5. SCALABILITY PROJECTIONS")
    print("-" * 40)
    
    if len(results) >= 2:
        # Project performance for larger datasets
        large_result = max(results, key=lambda x: x['input_size_gb'])
        throughput = large_result['throughput_gb_per_hour']
        
        print("Estimated processing times for larger datasets:")
        for size_gb in [1, 10, 100, 1000]:
            time_hours = size_gb / throughput
            if time_hours < 1:
                print(f"  {size_gb:4d} GB: {time_hours*60:.1f} minutes")
            elif time_hours < 24:
                print(f"  {size_gb:4d} GB: {time_hours:.1f} hours")
            else:
                print(f"  {size_gb:4d} GB: {time_hours/24:.1f} days")
        
        print(f"\n⚠️  Note: These are linear projections based on current throughput.")
        print(f"   Real performance may degrade for very large datasets due to:")
        print(f"   - Memory limitations")
        print(f"   - I/O bottlenecks") 
        print(f"   - Sorting complexity (O(n log n))")

def main():
    analyze_performance()

if __name__ == "__main__":
    main()
